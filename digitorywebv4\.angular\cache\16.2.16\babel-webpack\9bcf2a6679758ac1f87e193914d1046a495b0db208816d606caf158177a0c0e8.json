{"ast": null, "code": "import { Chart, registerables } from '../dist/chart.mjs';\nChart.register(...registerables);\nexport default Chart;", "map": {"version": 3, "names": ["Chart", "registerables", "register"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/chart.js/auto/auto.mjs"], "sourcesContent": ["import {Chart, registerables} from '../dist/chart.mjs';\n\nChart.register(...registerables);\n\nexport default Chart;\n"], "mappings": "AAAA,SAAQA,KAAK,EAAEC,aAAa,QAAO,mBAAmB;AAEtDD,KAAK,CAACE,QAAQ,CAAC,GAAGD,aAAa,CAAC;AAEhC,eAAeD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}