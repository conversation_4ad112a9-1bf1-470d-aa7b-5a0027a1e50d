<div class="smart-dashboard-container">
  <!-- Main Layout -->
  <div class="main-layout">
    <!-- Left Sidebar -->
    <div class="left-sidebar">
      <!-- Dashboard Selection -->
      <div class="dashboard-selection">
        <mat-form-field appearance="outline" class="dashboard-dropdown">
          <mat-label>Select Dashboard</mat-label>
          <mat-select [(value)]="selectedDashboard">
            <mat-option value="purchase">Purchase Dashboard</mat-option>
            <mat-option value="sales">Sales Dashboard</mat-option>
            <mat-option value="inventory">Inventory Dashboard</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <!-- Smart Filters Section -->
      <div class="filters-section">
        <h3 class="filters-title">
          <mat-icon>tune</mat-icon>
          Smart Filters
          <span class="filter-count">1</span>
        </h3>

        <!-- Restaurants Filter -->
        <div class="filter-group">
          <h4 class="filter-label">
            <mat-icon>restaurant</mat-icon>
            Restaurants
          </h4>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Select restaurants</mat-label>
            <mat-select [(value)]="selectedLocation" (selectionChange)="onLocationChange()">
              <mat-option>
                <ngx-mat-select-search
                  [formControl]="locationFilterCtrl"
                  placeholderLabel="Search locations..."
                  noEntriesFoundLabel="No locations found">
                </ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let branch of filteredBranches" [value]="branch.restaurantIdOld">
                {{branch.branchName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Base Date Filter -->
        <div class="filter-group">
          <h4 class="filter-label">
            <mat-icon>event</mat-icon>
            Base Date
          </h4>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Select base date</mat-label>
            <mat-select [formControl]="baseDateCtrl" (selectionChange)="onDateChange()">
              <mat-option value="deliveryDate">Delivery Date</mat-option>
              <mat-option value="orderDate">Order Date</mat-option>
              <mat-option value="createdDate">Created Date</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Start Date Filter -->
        <div class="filter-group">
          <h4 class="filter-label">
            <mat-icon>date_range</mat-icon>
            Start Date
          </h4>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Start Date</mat-label>
            <input matInput [matDatepicker]="startPicker" [formControl]="startDate" (dateChange)="onDateChange()">
            <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
            <mat-datepicker #startPicker></mat-datepicker>
          </mat-form-field>
        </div>

        <!-- End Date Filter -->
        <div class="filter-group">
          <h4 class="filter-label">
            <mat-icon>date_range</mat-icon>
            End Date
          </h4>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>End Date</mat-label>
            <input matInput [matDatepicker]="endPicker" [formControl]="endDate" (dateChange)="onDateChange()">
            <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
            <mat-datepicker #endPicker></mat-datepicker>
          </mat-form-field>
        </div>

        <!-- Reset Filters Button -->
        <div class="filter-actions">
          <button mat-stroked-button class="reset-filters-btn" (click)="loadDashboardData()">
            <mat-icon>refresh</mat-icon>
            Reset filters
          </button>
        </div>
      </div>
    </div>

    <!-- Right Content Area -->
    <div class="right-content">
      <!-- Top Search Bar -->
      <div class="search-header">
        <div class="assistant-info">
          <mat-icon class="assistant-icon">smart_toy</mat-icon>
          <div class="assistant-text">
            <span class="assistant-title">Smart Dashboard Assistant</span>
            <span class="assistant-status">Ready to analyze</span>
          </div>
        </div>
        <div class="search-container">
          <mat-form-field appearance="outline" class="search-field">
            <input matInput
                   placeholder="Ask me about your business data"
                   [formControl]="searchQuery"
                   (keyup.enter)="onSearchQuery()" />
            <mat-icon matSuffix class="search-icon" (click)="onSearchQuery()">search</mat-icon>
          </mat-form-field>
        </div>
      </div>

      <!-- Dashboard Content Area -->
      <div class="dashboard-content-area">
        <!-- Loading Spinner -->
        <div *ngIf="isLoading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Loading dashboard data...</p>
        </div>

        <!-- Dashboard Grid -->
        <div *ngIf="!isLoading && (summaryCards.length > 0 || charts.length > 0)" class="dashboard-grid">
          <!-- Summary Cards Row -->
          <div *ngIf="summaryCards.length > 0" class="summary-cards-row">
            <mat-card *ngFor="let card of summaryCards" class="summary-card" [style.border-left-color]="card.color">
              <mat-card-content>
                <div class="card-content">
                  <div class="card-icon" [style.color]="card.color">
                    <mat-icon>{{card.icon}}</mat-icon>
                  </div>
                  <div class="card-info">
                    <div class="card-value">{{card.value}}</div>
                    <div class="card-label">{{card.label}}</div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Charts Grid -->
          <div *ngIf="charts.length > 0" class="charts-grid">
            <mat-card *ngFor="let chart of charts; let i = index"
                      class="chart-card"
                      [ngClass]="{
                        'full-width': chart.type === 'line' || chart.title.toLowerCase().includes('trend'),
                        'half-width': chart.type !== 'line' && !chart.title.toLowerCase().includes('trend'),
                        'third-width': chart.type === 'doughnut' || chart.type === 'pie'
                      }">
              <mat-card-header>
                <mat-card-title class="chart-title">{{chart.title}}</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="chart-container" [attr.data-chart-type]="chart.type">
                  <!-- Line Chart -->
                  <canvas *ngIf="chart.type === 'line'"
                          baseChart
                          [type]="'line'"
                          [data]="getChartData(chart)"
                          [options]="lineChartOptions">
                  </canvas>

                  <!-- Bar Chart -->
                  <canvas *ngIf="chart.type === 'bar'"
                          baseChart
                          [type]="'bar'"
                          [data]="getChartData(chart)"
                          [options]="barChartOptions">
                  </canvas>

                  <!-- Doughnut Chart -->
                  <canvas *ngIf="chart.type === 'doughnut' || chart.type === 'pie'"
                          baseChart
                          [type]="'doughnut'"
                          [data]="getChartData(chart)"
                          [options]="doughnutChartOptions">
                  </canvas>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>

        <!-- Single Unified Empty State -->
        <div *ngIf="!isLoading && summaryCards.length === 0 && charts.length === 0" class="empty-state">
          <mat-icon class="empty-icon">analytics</mat-icon>
          <h3>No Data Available</h3>
          <p>Please select a location and date range to view dashboard data.</p>
          <button mat-raised-button color="primary" (click)="loadDashboardData()">
            <mat-icon>refresh</mat-icon>
            Refresh Data
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
