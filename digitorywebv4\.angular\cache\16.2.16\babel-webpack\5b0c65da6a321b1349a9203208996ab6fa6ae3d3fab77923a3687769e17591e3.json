{"ast": null, "code": "// List of valid html blocks names, accorting to commonmark spec\n// http://jgm.github.io/CommonMark/spec.html#html-blocks\n\n'use strict';\n\nmodule.exports = ['address', 'article', 'aside', 'base', 'basefont', 'blockquote', 'body', 'caption', 'center', 'col', 'colgroup', 'dd', 'details', 'dialog', 'dir', 'div', 'dl', 'dt', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'frame', 'frameset', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hr', 'html', 'iframe', 'legend', 'li', 'link', 'main', 'menu', 'menuitem', 'nav', 'noframes', 'ol', 'optgroup', 'option', 'p', 'param', 'section', 'source', 'summary', 'table', 'tbody', 'td', 'tfoot', 'th', 'thead', 'title', 'tr', 'track', 'ul'];", "map": {"version": 3, "names": ["module", "exports"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/common/html_blocks.js"], "sourcesContent": ["// List of valid html blocks names, accorting to commonmark spec\n// http://jgm.github.io/CommonMark/spec.html#html-blocks\n\n'use strict';\n\n\nmodule.exports = [\n  'address',\n  'article',\n  'aside',\n  'base',\n  'basefont',\n  'blockquote',\n  'body',\n  'caption',\n  'center',\n  'col',\n  'colgroup',\n  'dd',\n  'details',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'frame',\n  'frameset',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hr',\n  'html',\n  'iframe',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'menu',\n  'menuitem',\n  'nav',\n  'noframes',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'param',\n  'section',\n  'source',\n  'summary',\n  'table',\n  'tbody',\n  'td',\n  'tfoot',\n  'th',\n  'thead',\n  'title',\n  'tr',\n  'track',\n  'ul'\n];\n"], "mappings": "AAAA;AACA;;AAEA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,CACf,SAAS,EACT,SAAS,EACT,OAAO,EACP,MAAM,EACN,UAAU,EACV,YAAY,EACZ,MAAM,EACN,SAAS,EACT,QAAQ,EACR,KAAK,EACL,UAAU,EACV,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,EACP,UAAU,EACV,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,UAAU,EACV,KAAK,EACL,UAAU,EACV,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,GAAG,EACH,OAAO,EACP,SAAS,EACT,QAAQ,EACR,SAAS,EACT,OAAO,EACP,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACP,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,CACL", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}