{"ast": null, "code": "/**\n * A specialized version of `baseAggregator` for arrays.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction arrayAggregator(array, setter, iteratee, accumulator) {\n  var index = -1,\n    length = array == null ? 0 : array.length;\n  while (++index < length) {\n    var value = array[index];\n    setter(accumulator, value, iteratee(value), array);\n  }\n  return accumulator;\n}\nexport default arrayAggregator;", "map": {"version": 3, "names": ["arrayAggregator", "array", "setter", "iteratee", "accumulator", "index", "length", "value"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/lodash-es/_arrayAggregator.js"], "sourcesContent": ["/**\n * A specialized version of `baseAggregator` for arrays.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction arrayAggregator(array, setter, iteratee, accumulator) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    var value = array[index];\n    setter(accumulator, value, iteratee(value), array);\n  }\n  return accumulator;\n}\n\nexport default arrayAggregator;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAeA,CAACC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EAC7D,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGL,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACK,MAAM;EAE7C,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIC,KAAK,GAAGN,KAAK,CAACI,KAAK,CAAC;IACxBH,MAAM,CAACE,WAAW,EAAEG,KAAK,EAAEJ,QAAQ,CAACI,KAAK,CAAC,EAAEN,KAAK,CAAC;EACpD;EACA,OAAOG,WAAW;AACpB;AAEA,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}