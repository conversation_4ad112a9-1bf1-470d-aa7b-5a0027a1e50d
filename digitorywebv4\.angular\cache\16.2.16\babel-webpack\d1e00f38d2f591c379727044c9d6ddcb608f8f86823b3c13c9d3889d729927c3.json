{"ast": null, "code": "// Paragraph\n\n'use strict';\n\nmodule.exports = function paragraph(state, startLine /*, endLine*/) {\n  var content,\n    terminate,\n    i,\n    l,\n    token,\n    oldParentType,\n    nextLine = startLine + 1,\n    terminatorRules = state.md.block.ruler.getRules('paragraph'),\n    endLine = state.lineMax;\n  oldParentType = state.parentType;\n  state.parentType = 'paragraph';\n\n  // jump line-by-line until empty one or EOF\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) {\n      continue;\n    }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) {\n      continue;\n    }\n\n    // Some tags can terminate paragraph without empty line.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) {\n      break;\n    }\n  }\n  content = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n  state.line = nextLine;\n  token = state.push('paragraph_open', 'p', 1);\n  token.map = [startLine, state.line];\n  token = state.push('inline', '', 0);\n  token.content = content;\n  token.map = [startLine, state.line];\n  token.children = [];\n  token = state.push('paragraph_close', 'p', -1);\n  state.parentType = oldParentType;\n  return true;\n};", "map": {"version": 3, "names": ["module", "exports", "paragraph", "state", "startLine", "content", "terminate", "i", "l", "token", "oldParentType", "nextLine", "terminatorRules", "md", "block", "ruler", "getRules", "endLine", "lineMax", "parentType", "isEmpty", "sCount", "blkIndent", "length", "getLines", "trim", "line", "push", "map", "children"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_block/paragraph.js"], "sourcesContent": ["// Paragraph\n\n'use strict';\n\n\nmodule.exports = function paragraph(state, startLine/*, endLine*/) {\n  var content, terminate, i, l, token, oldParentType,\n      nextLine = startLine + 1,\n      terminatorRules = state.md.block.ruler.getRules('paragraph'),\n      endLine = state.lineMax;\n\n  oldParentType = state.parentType;\n  state.parentType = 'paragraph';\n\n  // jump line-by-line until empty one or EOF\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { continue; }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { continue; }\n\n    // Some tags can terminate paragraph without empty line.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) { break; }\n  }\n\n  content = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n\n  state.line = nextLine;\n\n  token          = state.push('paragraph_open', 'p', 1);\n  token.map      = [ startLine, state.line ];\n\n  token          = state.push('inline', '', 0);\n  token.content  = content;\n  token.map      = [ startLine, state.line ];\n  token.children = [];\n\n  token          = state.push('paragraph_close', 'p', -1);\n\n  state.parentType = oldParentType;\n\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,SAASA,CAACC,KAAK,EAAEC,SAAS,gBAAe;EACjE,IAAIC,OAAO;IAAEC,SAAS;IAAEC,CAAC;IAAEC,CAAC;IAAEC,KAAK;IAAEC,aAAa;IAC9CC,QAAQ,GAAGP,SAAS,GAAG,CAAC;IACxBQ,eAAe,GAAGT,KAAK,CAACU,EAAE,CAACC,KAAK,CAACC,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC;IAC5DC,OAAO,GAAGd,KAAK,CAACe,OAAO;EAE3BR,aAAa,GAAGP,KAAK,CAACgB,UAAU;EAChChB,KAAK,CAACgB,UAAU,GAAG,WAAW;;EAE9B;EACA,OAAOR,QAAQ,GAAGM,OAAO,IAAI,CAACd,KAAK,CAACiB,OAAO,CAACT,QAAQ,CAAC,EAAEA,QAAQ,EAAE,EAAE;IACjE;IACA;IACA,IAAIR,KAAK,CAACkB,MAAM,CAACV,QAAQ,CAAC,GAAGR,KAAK,CAACmB,SAAS,GAAG,CAAC,EAAE;MAAE;IAAU;;IAE9D;IACA,IAAInB,KAAK,CAACkB,MAAM,CAACV,QAAQ,CAAC,GAAG,CAAC,EAAE;MAAE;IAAU;;IAE5C;IACAL,SAAS,GAAG,KAAK;IACjB,KAAKC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGI,eAAe,CAACW,MAAM,EAAEhB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAClD,IAAIK,eAAe,CAACL,CAAC,CAAC,CAACJ,KAAK,EAAEQ,QAAQ,EAAEM,OAAO,EAAE,IAAI,CAAC,EAAE;QACtDX,SAAS,GAAG,IAAI;QAChB;MACF;IACF;IACA,IAAIA,SAAS,EAAE;MAAE;IAAO;EAC1B;EAEAD,OAAO,GAAGF,KAAK,CAACqB,QAAQ,CAACpB,SAAS,EAAEO,QAAQ,EAAER,KAAK,CAACmB,SAAS,EAAE,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC;EAE5EtB,KAAK,CAACuB,IAAI,GAAGf,QAAQ;EAErBF,KAAK,GAAYN,KAAK,CAACwB,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC;EACrDlB,KAAK,CAACmB,GAAG,GAAQ,CAAExB,SAAS,EAAED,KAAK,CAACuB,IAAI,CAAE;EAE1CjB,KAAK,GAAYN,KAAK,CAACwB,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5ClB,KAAK,CAACJ,OAAO,GAAIA,OAAO;EACxBI,KAAK,CAACmB,GAAG,GAAQ,CAAExB,SAAS,EAAED,KAAK,CAACuB,IAAI,CAAE;EAC1CjB,KAAK,CAACoB,QAAQ,GAAG,EAAE;EAEnBpB,KAAK,GAAYN,KAAK,CAACwB,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;EAEvDxB,KAAK,CAACgB,UAAU,GAAGT,aAAa;EAEhC,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}