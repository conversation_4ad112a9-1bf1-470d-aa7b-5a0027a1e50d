# Smart Dashboard Component

A comprehensive dashboard component that provides intelligent analytics and insights for restaurant business data.

## Features

### Summary Cards
- **Total Purchase Amount**: Displays the total amount spent on purchases
- **Total Orders**: Shows the number of orders placed
- **Average Order Value**: Calculates the average value per order
- **Top Vendor**: Highlights the vendor with the highest purchase amount

### Interactive Charts
1. **Purchase Trends (Line Chart)**: Shows purchase trends over the last 30 days
2. **Top Vendors (Horizontal Bar Chart)**: Displays top vendors by purchase amount
3. **Category Spending (Doughnut Chart)**: Shows spending distribution across categories
4. **Top Items by Quantity (Bar Chart)**: Displays most purchased items by quantity

### Smart Filters
- **Location Filter**: Filter data by restaurant location with search functionality
- **Date Range Filter**: Select custom date ranges for analysis
- **Base Date Selection**: Choose between different date criteria

## Usage

### Navigation
The Smart Dashboard is accessible from the main navigation menu under "Smart Dashboard" with an analytics icon.

### API Integration
The component integrates with the backend smart dashboard API:
- **Endpoint**: `/api/smart-dashboard/smart_ask`
- **Method**: POST
- **Authentication**: Bearer token required

### Sample Request
```json
{
  "tenant_id": "your-tenant-id",
  "filters": {
    "locations": ["location-id"],
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "baseDate": "deliveryDate"
  },
  "user_query": "",
  "use_default_charts": true
}
```

### Sample Response
```json
{
  "status": "success",
  "data": {
    "summary_items": [
      {
        "icon": "attach_money",
        "value": "₹34,766",
        "label": "Total Purchase Amount",
        "data_type": "currency"
      }
    ],
    "charts": [
      {
        "id": "purchase-trends",
        "title": "Purchase Trends (Last 30 Days)",
        "type": "line",
        "data": {
          "labels": ["Day 1", "Day 2"],
          "datasets": [{
            "label": "Purchase Amount",
            "data": [1000, 1500],
            "backgroundColor": ["#ff6b35"],
            "borderColor": ["#ff6b35"]
          }]
        }
      }
    ]
  }
}
```

## Styling

The component uses a consistent color scheme:
- **Primary Orange**: #ff6b35
- **Light Orange**: #ffa66f
- **Medium Orange**: #ff8b4d
- **Peach**: #ff9966
- **Light Peach**: #ffd1b3

### Responsive Design
- **Desktop**: Full grid layout with sidebar
- **Tablet**: Stacked charts, collapsible sidebar
- **Mobile**: Single column layout

## Dependencies

- **ng2-charts**: For chart rendering
- **chart.js**: Chart library
- **Angular Material**: UI components
- **ngx-mat-select-search**: Searchable select dropdown

## Error Handling

The component includes robust error handling:
- Fallback to sample data when API fails
- Loading states with spinners
- Empty state messages
- Retry functionality

## Customization

### Adding New Chart Types
1. Update the `ChartDataModel` interface
2. Add chart options configuration
3. Update the template with new chart canvas
4. Implement data processing logic

### Modifying Summary Cards
1. Update the `SummaryCard` interface
2. Modify the `createDefaultSummaryCards()` method
3. Adjust styling in SCSS file

## Performance Considerations

- Uses OnPush change detection strategy
- Implements proper subscription cleanup
- Lazy loading for chart components
- Optimized grid layouts for different screen sizes

## Testing

The component includes sample data for testing purposes when the API is unavailable. This ensures the UI can be previewed and tested independently of backend services.
