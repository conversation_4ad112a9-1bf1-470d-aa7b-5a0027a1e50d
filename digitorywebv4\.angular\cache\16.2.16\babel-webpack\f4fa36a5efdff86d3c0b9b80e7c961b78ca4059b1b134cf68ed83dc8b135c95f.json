{"ast": null, "code": "// Proceess '\\n'\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\nmodule.exports = function newline(state, silent) {\n  var pmax,\n    max,\n    ws,\n    pos = state.pos;\n  if (state.src.charCodeAt(pos) !== 0x0A /* \\n */) {\n    return false;\n  }\n  pmax = state.pending.length - 1;\n  max = state.posMax;\n\n  // '  \\n' -> hardbreak\n  // Lookup in pending chars is bad practice! Don't copy to other rules!\n  // Pending string is stored in concat mode, indexed lookups will cause\n  // convertion to flat mode.\n  if (!silent) {\n    if (pmax >= 0 && state.pending.charCodeAt(pmax) === 0x20) {\n      if (pmax >= 1 && state.pending.charCodeAt(pmax - 1) === 0x20) {\n        // Find whitespaces tail of pending chars.\n        ws = pmax - 1;\n        while (ws >= 1 && state.pending.charCodeAt(ws - 1) === 0x20) ws--;\n        state.pending = state.pending.slice(0, ws);\n        state.push('hardbreak', 'br', 0);\n      } else {\n        state.pending = state.pending.slice(0, -1);\n        state.push('softbreak', 'br', 0);\n      }\n    } else {\n      state.push('softbreak', 'br', 0);\n    }\n  }\n  pos++;\n\n  // skip heading spaces for next line\n  while (pos < max && isSpace(state.src.charCodeAt(pos))) {\n    pos++;\n  }\n  state.pos = pos;\n  return true;\n};", "map": {"version": 3, "names": ["isSpace", "require", "module", "exports", "newline", "state", "silent", "pmax", "max", "ws", "pos", "src", "charCodeAt", "pending", "length", "posMax", "slice", "push"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_inline/newline.js"], "sourcesContent": ["// Proceess '\\n'\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\nmodule.exports = function newline(state, silent) {\n  var pmax, max, ws, pos = state.pos;\n\n  if (state.src.charCodeAt(pos) !== 0x0A/* \\n */) { return false; }\n\n  pmax = state.pending.length - 1;\n  max = state.posMax;\n\n  // '  \\n' -> hardbreak\n  // Lookup in pending chars is bad practice! Don't copy to other rules!\n  // Pending string is stored in concat mode, indexed lookups will cause\n  // convertion to flat mode.\n  if (!silent) {\n    if (pmax >= 0 && state.pending.charCodeAt(pmax) === 0x20) {\n      if (pmax >= 1 && state.pending.charCodeAt(pmax - 1) === 0x20) {\n        // Find whitespaces tail of pending chars.\n        ws = pmax - 1;\n        while (ws >= 1 && state.pending.charCodeAt(ws - 1) === 0x20) ws--;\n\n        state.pending = state.pending.slice(0, ws);\n        state.push('hardbreak', 'br', 0);\n      } else {\n        state.pending = state.pending.slice(0, -1);\n        state.push('softbreak', 'br', 0);\n      }\n\n    } else {\n      state.push('softbreak', 'br', 0);\n    }\n  }\n\n  pos++;\n\n  // skip heading spaces for next line\n  while (pos < max && isSpace(state.src.charCodeAt(pos))) { pos++; }\n\n  state.pos = pos;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACD,OAAO;AAGhDE,MAAM,CAACC,OAAO,GAAG,SAASC,OAAOA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC/C,IAAIC,IAAI;IAAEC,GAAG;IAAEC,EAAE;IAAEC,GAAG,GAAGL,KAAK,CAACK,GAAG;EAElC,IAAIL,KAAK,CAACM,GAAG,CAACC,UAAU,CAACF,GAAG,CAAC,KAAK,IAAI,WAAU;IAAE,OAAO,KAAK;EAAE;EAEhEH,IAAI,GAAGF,KAAK,CAACQ,OAAO,CAACC,MAAM,GAAG,CAAC;EAC/BN,GAAG,GAAGH,KAAK,CAACU,MAAM;;EAElB;EACA;EACA;EACA;EACA,IAAI,CAACT,MAAM,EAAE;IACX,IAAIC,IAAI,IAAI,CAAC,IAAIF,KAAK,CAACQ,OAAO,CAACD,UAAU,CAACL,IAAI,CAAC,KAAK,IAAI,EAAE;MACxD,IAAIA,IAAI,IAAI,CAAC,IAAIF,KAAK,CAACQ,OAAO,CAACD,UAAU,CAACL,IAAI,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;QAC5D;QACAE,EAAE,GAAGF,IAAI,GAAG,CAAC;QACb,OAAOE,EAAE,IAAI,CAAC,IAAIJ,KAAK,CAACQ,OAAO,CAACD,UAAU,CAACH,EAAE,GAAG,CAAC,CAAC,KAAK,IAAI,EAAEA,EAAE,EAAE;QAEjEJ,KAAK,CAACQ,OAAO,GAAGR,KAAK,CAACQ,OAAO,CAACG,KAAK,CAAC,CAAC,EAAEP,EAAE,CAAC;QAC1CJ,KAAK,CAACY,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;MAClC,CAAC,MAAM;QACLZ,KAAK,CAACQ,OAAO,GAAGR,KAAK,CAACQ,OAAO,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1CX,KAAK,CAACY,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;MAClC;IAEF,CAAC,MAAM;MACLZ,KAAK,CAACY,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IAClC;EACF;EAEAP,GAAG,EAAE;;EAEL;EACA,OAAOA,GAAG,GAAGF,GAAG,IAAIR,OAAO,CAACK,KAAK,CAACM,GAAG,CAACC,UAAU,CAACF,GAAG,CAAC,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAE;EAEjEL,KAAK,CAACK,GAAG,GAAGA,GAAG;EACf,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}