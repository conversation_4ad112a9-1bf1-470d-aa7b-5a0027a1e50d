{"ast": null, "code": "// Core state object\n//\n'use strict';\n\nvar Token = require('../token');\nfunction StateCore(src, md, env) {\n  this.src = src;\n  this.env = env;\n  this.tokens = [];\n  this.inlineMode = false;\n  this.md = md; // link to parser instance\n}\n\n// re-export Token class to use in core rules\nStateCore.prototype.Token = Token;\nmodule.exports = StateCore;", "map": {"version": 3, "names": ["Token", "require", "StateCore", "src", "md", "env", "tokens", "inlineMode", "prototype", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_core/state_core.js"], "sourcesContent": ["// Core state object\n//\n'use strict';\n\nvar Token = require('../token');\n\n\nfunction StateCore(src, md, env) {\n  this.src = src;\n  this.env = env;\n  this.tokens = [];\n  this.inlineMode = false;\n  this.md = md; // link to parser instance\n}\n\n// re-export Token class to use in core rules\nStateCore.prototype.Token = Token;\n\n\nmodule.exports = StateCore;\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAU,CAAC;AAG/B,SAASC,SAASA,CAACC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAE;EAC/B,IAAI,CAACF,GAAG,GAAGA,GAAG;EACd,IAAI,CAACE,GAAG,GAAGA,GAAG;EACd,IAAI,CAACC,MAAM,GAAG,EAAE;EAChB,IAAI,CAACC,UAAU,GAAG,KAAK;EACvB,IAAI,CAACH,EAAE,GAAGA,EAAE,CAAC,CAAC;AAChB;;AAEA;AACAF,SAAS,CAACM,SAAS,CAACR,KAAK,GAAGA,KAAK;AAGjCS,MAAM,CAACC,OAAO,GAAGR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}