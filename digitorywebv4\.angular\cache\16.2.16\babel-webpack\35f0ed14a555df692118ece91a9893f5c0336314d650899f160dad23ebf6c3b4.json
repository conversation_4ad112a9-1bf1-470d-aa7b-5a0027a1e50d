{"ast": null, "code": "import { parseM<PERSON>is, isUndefined, untruncate<PERSON>ear, signedOffset, hasOwnProperty } from \"./util.js\";\nimport Formatter from \"./formatter.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport DateTime from \"../datetime.js\";\nimport { digitRegex, parseDigits } from \"./digits.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\nconst MISSING_FTP = \"missing Intl.DateTimeFormat.formatToParts support\";\nfunction intUnit(regex, post = i => i) {\n  return {\n    regex,\n    deser: ([s]) => post(parseDigits(s))\n  };\n}\nconst NBSP = String.fromCharCode(160);\nconst spaceOrNBSP = `[ ${NBSP}]`;\nconst spaceOrNBSPRegExp = new RegExp(spaceOrNBSP, \"g\");\nfunction fixListRegex(s) {\n  // make dots optional and also make them literal\n  // make space and non breakable space characters interchangeable\n  return s.replace(/\\./g, \"\\\\.?\").replace(spaceOrNBSPRegExp, spaceOrNBSP);\n}\nfunction stripInsensitivities(s) {\n  return s.replace(/\\./g, \"\") // ignore dots that were made optional\n  .replace(spaceOrNBSPRegExp, \" \") // interchange space and nbsp\n  .toLowerCase();\n}\nfunction oneOf(strings, startIndex) {\n  if (strings === null) {\n    return null;\n  } else {\n    return {\n      regex: RegExp(strings.map(fixListRegex).join(\"|\")),\n      deser: ([s]) => strings.findIndex(i => stripInsensitivities(s) === stripInsensitivities(i)) + startIndex\n    };\n  }\n}\nfunction offset(regex, groups) {\n  return {\n    regex,\n    deser: ([, h, m]) => signedOffset(h, m),\n    groups\n  };\n}\nfunction simple(regex) {\n  return {\n    regex,\n    deser: ([s]) => s\n  };\n}\nfunction escapeToken(value) {\n  return value.replace(/[\\-\\[\\]{}()*+?.,\\\\\\^$|#\\s]/g, \"\\\\$&\");\n}\n\n/**\n * @param token\n * @param {Locale} loc\n */\nfunction unitForToken(token, loc) {\n  const one = digitRegex(loc),\n    two = digitRegex(loc, \"{2}\"),\n    three = digitRegex(loc, \"{3}\"),\n    four = digitRegex(loc, \"{4}\"),\n    six = digitRegex(loc, \"{6}\"),\n    oneOrTwo = digitRegex(loc, \"{1,2}\"),\n    oneToThree = digitRegex(loc, \"{1,3}\"),\n    oneToSix = digitRegex(loc, \"{1,6}\"),\n    oneToNine = digitRegex(loc, \"{1,9}\"),\n    twoToFour = digitRegex(loc, \"{2,4}\"),\n    fourToSix = digitRegex(loc, \"{4,6}\"),\n    literal = t => ({\n      regex: RegExp(escapeToken(t.val)),\n      deser: ([s]) => s,\n      literal: true\n    }),\n    unitate = t => {\n      if (token.literal) {\n        return literal(t);\n      }\n      switch (t.val) {\n        // era\n        case \"G\":\n          return oneOf(loc.eras(\"short\"), 0);\n        case \"GG\":\n          return oneOf(loc.eras(\"long\"), 0);\n        // years\n        case \"y\":\n          return intUnit(oneToSix);\n        case \"yy\":\n          return intUnit(twoToFour, untruncateYear);\n        case \"yyyy\":\n          return intUnit(four);\n        case \"yyyyy\":\n          return intUnit(fourToSix);\n        case \"yyyyyy\":\n          return intUnit(six);\n        // months\n        case \"M\":\n          return intUnit(oneOrTwo);\n        case \"MM\":\n          return intUnit(two);\n        case \"MMM\":\n          return oneOf(loc.months(\"short\", true), 1);\n        case \"MMMM\":\n          return oneOf(loc.months(\"long\", true), 1);\n        case \"L\":\n          return intUnit(oneOrTwo);\n        case \"LL\":\n          return intUnit(two);\n        case \"LLL\":\n          return oneOf(loc.months(\"short\", false), 1);\n        case \"LLLL\":\n          return oneOf(loc.months(\"long\", false), 1);\n        // dates\n        case \"d\":\n          return intUnit(oneOrTwo);\n        case \"dd\":\n          return intUnit(two);\n        // ordinals\n        case \"o\":\n          return intUnit(oneToThree);\n        case \"ooo\":\n          return intUnit(three);\n        // time\n        case \"HH\":\n          return intUnit(two);\n        case \"H\":\n          return intUnit(oneOrTwo);\n        case \"hh\":\n          return intUnit(two);\n        case \"h\":\n          return intUnit(oneOrTwo);\n        case \"mm\":\n          return intUnit(two);\n        case \"m\":\n          return intUnit(oneOrTwo);\n        case \"q\":\n          return intUnit(oneOrTwo);\n        case \"qq\":\n          return intUnit(two);\n        case \"s\":\n          return intUnit(oneOrTwo);\n        case \"ss\":\n          return intUnit(two);\n        case \"S\":\n          return intUnit(oneToThree);\n        case \"SSS\":\n          return intUnit(three);\n        case \"u\":\n          return simple(oneToNine);\n        case \"uu\":\n          return simple(oneOrTwo);\n        case \"uuu\":\n          return intUnit(one);\n        // meridiem\n        case \"a\":\n          return oneOf(loc.meridiems(), 0);\n        // weekYear (k)\n        case \"kkkk\":\n          return intUnit(four);\n        case \"kk\":\n          return intUnit(twoToFour, untruncateYear);\n        // weekNumber (W)\n        case \"W\":\n          return intUnit(oneOrTwo);\n        case \"WW\":\n          return intUnit(two);\n        // weekdays\n        case \"E\":\n        case \"c\":\n          return intUnit(one);\n        case \"EEE\":\n          return oneOf(loc.weekdays(\"short\", false), 1);\n        case \"EEEE\":\n          return oneOf(loc.weekdays(\"long\", false), 1);\n        case \"ccc\":\n          return oneOf(loc.weekdays(\"short\", true), 1);\n        case \"cccc\":\n          return oneOf(loc.weekdays(\"long\", true), 1);\n        // offset/zone\n        case \"Z\":\n        case \"ZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(?::(${two.source}))?`), 2);\n        case \"ZZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(${two.source})?`), 2);\n        // we don't support ZZZZ (PST) or ZZZZZ (Pacific Standard Time) in parsing\n        // because we don't have any way to figure out what they are\n        case \"z\":\n          return simple(/[a-z_+-/]{1,256}?/i);\n        // this special-case \"token\" represents a place where a macro-token expanded into a white-space literal\n        // in this case we accept any non-newline white-space\n        case \" \":\n          return simple(/[^\\S\\n\\r]/);\n        default:\n          return literal(t);\n      }\n    };\n  const unit = unitate(token) || {\n    invalidReason: MISSING_FTP\n  };\n  unit.token = token;\n  return unit;\n}\nconst partTypeStyleToTokenVal = {\n  year: {\n    \"2-digit\": \"yy\",\n    numeric: \"yyyyy\"\n  },\n  month: {\n    numeric: \"M\",\n    \"2-digit\": \"MM\",\n    short: \"MMM\",\n    long: \"MMMM\"\n  },\n  day: {\n    numeric: \"d\",\n    \"2-digit\": \"dd\"\n  },\n  weekday: {\n    short: \"EEE\",\n    long: \"EEEE\"\n  },\n  dayperiod: \"a\",\n  dayPeriod: \"a\",\n  hour12: {\n    numeric: \"h\",\n    \"2-digit\": \"hh\"\n  },\n  hour24: {\n    numeric: \"H\",\n    \"2-digit\": \"HH\"\n  },\n  minute: {\n    numeric: \"m\",\n    \"2-digit\": \"mm\"\n  },\n  second: {\n    numeric: \"s\",\n    \"2-digit\": \"ss\"\n  },\n  timeZoneName: {\n    long: \"ZZZZZ\",\n    short: \"ZZZ\"\n  }\n};\nfunction tokenForPart(part, formatOpts, resolvedOpts) {\n  const {\n    type,\n    value\n  } = part;\n  if (type === \"literal\") {\n    const isSpace = /^\\s+$/.test(value);\n    return {\n      literal: !isSpace,\n      val: isSpace ? \" \" : value\n    };\n  }\n  const style = formatOpts[type];\n\n  // The user might have explicitly specified hour12 or hourCycle\n  // if so, respect their decision\n  // if not, refer back to the resolvedOpts, which are based on the locale\n  let actualType = type;\n  if (type === \"hour\") {\n    if (formatOpts.hour12 != null) {\n      actualType = formatOpts.hour12 ? \"hour12\" : \"hour24\";\n    } else if (formatOpts.hourCycle != null) {\n      if (formatOpts.hourCycle === \"h11\" || formatOpts.hourCycle === \"h12\") {\n        actualType = \"hour12\";\n      } else {\n        actualType = \"hour24\";\n      }\n    } else {\n      // tokens only differentiate between 24 hours or not,\n      // so we do not need to check hourCycle here, which is less supported anyways\n      actualType = resolvedOpts.hour12 ? \"hour12\" : \"hour24\";\n    }\n  }\n  let val = partTypeStyleToTokenVal[actualType];\n  if (typeof val === \"object\") {\n    val = val[style];\n  }\n  if (val) {\n    return {\n      literal: false,\n      val\n    };\n  }\n  return undefined;\n}\nfunction buildRegex(units) {\n  const re = units.map(u => u.regex).reduce((f, r) => `${f}(${r.source})`, \"\");\n  return [`^${re}$`, units];\n}\nfunction match(input, regex, handlers) {\n  const matches = input.match(regex);\n  if (matches) {\n    const all = {};\n    let matchIndex = 1;\n    for (const i in handlers) {\n      if (hasOwnProperty(handlers, i)) {\n        const h = handlers[i],\n          groups = h.groups ? h.groups + 1 : 1;\n        if (!h.literal && h.token) {\n          all[h.token.val[0]] = h.deser(matches.slice(matchIndex, matchIndex + groups));\n        }\n        matchIndex += groups;\n      }\n    }\n    return [matches, all];\n  } else {\n    return [matches, {}];\n  }\n}\nfunction dateTimeFromMatches(matches) {\n  const toField = token => {\n    switch (token) {\n      case \"S\":\n        return \"millisecond\";\n      case \"s\":\n        return \"second\";\n      case \"m\":\n        return \"minute\";\n      case \"h\":\n      case \"H\":\n        return \"hour\";\n      case \"d\":\n        return \"day\";\n      case \"o\":\n        return \"ordinal\";\n      case \"L\":\n      case \"M\":\n        return \"month\";\n      case \"y\":\n        return \"year\";\n      case \"E\":\n      case \"c\":\n        return \"weekday\";\n      case \"W\":\n        return \"weekNumber\";\n      case \"k\":\n        return \"weekYear\";\n      case \"q\":\n        return \"quarter\";\n      default:\n        return null;\n    }\n  };\n  let zone = null;\n  let specificOffset;\n  if (!isUndefined(matches.z)) {\n    zone = IANAZone.create(matches.z);\n  }\n  if (!isUndefined(matches.Z)) {\n    if (!zone) {\n      zone = new FixedOffsetZone(matches.Z);\n    }\n    specificOffset = matches.Z;\n  }\n  if (!isUndefined(matches.q)) {\n    matches.M = (matches.q - 1) * 3 + 1;\n  }\n  if (!isUndefined(matches.h)) {\n    if (matches.h < 12 && matches.a === 1) {\n      matches.h += 12;\n    } else if (matches.h === 12 && matches.a === 0) {\n      matches.h = 0;\n    }\n  }\n  if (matches.G === 0 && matches.y) {\n    matches.y = -matches.y;\n  }\n  if (!isUndefined(matches.u)) {\n    matches.S = parseMillis(matches.u);\n  }\n  const vals = Object.keys(matches).reduce((r, k) => {\n    const f = toField(k);\n    if (f) {\n      r[f] = matches[k];\n    }\n    return r;\n  }, {});\n  return [vals, zone, specificOffset];\n}\nlet dummyDateTimeCache = null;\nfunction getDummyDateTime() {\n  if (!dummyDateTimeCache) {\n    dummyDateTimeCache = DateTime.fromMillis(1555555555555);\n  }\n  return dummyDateTimeCache;\n}\nfunction maybeExpandMacroToken(token, locale) {\n  if (token.literal) {\n    return token;\n  }\n  const formatOpts = Formatter.macroTokenToFormatOpts(token.val);\n  const tokens = formatOptsToTokens(formatOpts, locale);\n  if (tokens == null || tokens.includes(undefined)) {\n    return token;\n  }\n  return tokens;\n}\nexport function expandMacroTokens(tokens, locale) {\n  return Array.prototype.concat(...tokens.map(t => maybeExpandMacroToken(t, locale)));\n}\n\n/**\n * @private\n */\n\nexport class TokenParser {\n  constructor(locale, format) {\n    this.locale = locale;\n    this.format = format;\n    this.tokens = expandMacroTokens(Formatter.parseFormat(format), locale);\n    this.units = this.tokens.map(t => unitForToken(t, locale));\n    this.disqualifyingUnit = this.units.find(t => t.invalidReason);\n    if (!this.disqualifyingUnit) {\n      const [regexString, handlers] = buildRegex(this.units);\n      this.regex = RegExp(regexString, \"i\");\n      this.handlers = handlers;\n    }\n  }\n  explainFromTokens(input) {\n    if (!this.isValid) {\n      return {\n        input,\n        tokens: this.tokens,\n        invalidReason: this.invalidReason\n      };\n    } else {\n      const [rawMatches, matches] = match(input, this.regex, this.handlers),\n        [result, zone, specificOffset] = matches ? dateTimeFromMatches(matches) : [null, null, undefined];\n      if (hasOwnProperty(matches, \"a\") && hasOwnProperty(matches, \"H\")) {\n        throw new ConflictingSpecificationError(\"Can't include meridiem when specifying 24-hour format\");\n      }\n      return {\n        input,\n        tokens: this.tokens,\n        regex: this.regex,\n        rawMatches,\n        matches,\n        result,\n        zone,\n        specificOffset\n      };\n    }\n  }\n  get isValid() {\n    return !this.disqualifyingUnit;\n  }\n  get invalidReason() {\n    return this.disqualifyingUnit ? this.disqualifyingUnit.invalidReason : null;\n  }\n}\nexport function explainFromTokens(locale, input, format) {\n  const parser = new TokenParser(locale, format);\n  return parser.explainFromTokens(input);\n}\nexport function parseFromTokens(locale, input, format) {\n  const {\n    result,\n    zone,\n    specificOffset,\n    invalidReason\n  } = explainFromTokens(locale, input, format);\n  return [result, zone, specificOffset, invalidReason];\n}\nexport function formatOptsToTokens(formatOpts, locale) {\n  if (!formatOpts) {\n    return null;\n  }\n  const formatter = Formatter.create(locale, formatOpts);\n  const df = formatter.dtFormatter(getDummyDateTime());\n  const parts = df.formatToParts();\n  const resolvedOpts = df.resolvedOptions();\n  return parts.map(p => tokenForPart(p, formatOpts, resolvedOpts));\n}", "map": {"version": 3, "names": ["parse<PERSON><PERSON><PERSON>", "isUndefined", "untruncateYear", "signedOffset", "hasOwnProperty", "<PERSON><PERSON><PERSON>", "FixedOffsetZone", "IANAZone", "DateTime", "digitRegex", "parseDigits", "ConflictingSpecificationError", "MISSING_FTP", "intUnit", "regex", "post", "i", "deser", "s", "NBSP", "String", "fromCharCode", "spaceOrNBSP", "spaceOrNBSPRegExp", "RegExp", "fixListRegex", "replace", "stripInsensitivities", "toLowerCase", "oneOf", "strings", "startIndex", "map", "join", "findIndex", "offset", "groups", "h", "m", "simple", "escapeToken", "value", "unitForToken", "token", "loc", "one", "two", "three", "four", "six", "oneOrTwo", "oneToThree", "oneToSix", "oneToNine", "twoToFour", "fourToSix", "literal", "t", "val", "unitate", "eras", "months", "meridiems", "weekdays", "source", "unit", "invalidReason", "partTypeStyleToTokenVal", "year", "numeric", "month", "short", "long", "day", "weekday", "dayperiod", "<PERSON><PERSON><PERSON><PERSON>", "hour12", "hour24", "minute", "second", "timeZoneName", "tokenForPart", "part", "formatOpts", "resolvedOpts", "type", "isSpace", "test", "style", "actualType", "hourCycle", "undefined", "buildRegex", "units", "re", "u", "reduce", "f", "r", "match", "input", "handlers", "matches", "all", "matchIndex", "slice", "dateTimeFromMatches", "to<PERSON>ield", "zone", "specificOffset", "z", "create", "Z", "q", "M", "a", "G", "y", "S", "vals", "Object", "keys", "k", "dummyDateTimeCache", "getDummyDateTime", "fromMillis", "maybeExpandMacroToken", "locale", "macroTokenToFormatOpts", "tokens", "formatOptsToTokens", "includes", "expandMacroTokens", "Array", "prototype", "concat", "Token<PERSON><PERSON><PERSON>", "constructor", "format", "parseFormat", "disqualifying<PERSON>nit", "find", "regexString", "explainFromTokens", "<PERSON><PERSON><PERSON><PERSON>", "rawMatches", "result", "parser", "parseFromTokens", "formatter", "df", "dt<PERSON><PERSON><PERSON><PERSON>", "parts", "formatToParts", "resolvedOptions", "p"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/luxon/src/impl/tokenParser.js"], "sourcesContent": ["import { parseM<PERSON>is, isUndefined, untruncate<PERSON>ear, signedOffset, hasOwnProperty } from \"./util.js\";\nimport Formatter from \"./formatter.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport DateTime from \"../datetime.js\";\nimport { digitRegex, parseDigits } from \"./digits.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst MISSING_FTP = \"missing Intl.DateTimeFormat.formatToParts support\";\n\nfunction intUnit(regex, post = (i) => i) {\n  return { regex, deser: ([s]) => post(parseDigits(s)) };\n}\n\nconst NBSP = String.fromCharCode(160);\nconst spaceOrNBSP = `[ ${NBSP}]`;\nconst spaceOrNBSPRegExp = new RegExp(spaceOrNBSP, \"g\");\n\nfunction fixListRegex(s) {\n  // make dots optional and also make them literal\n  // make space and non breakable space characters interchangeable\n  return s.replace(/\\./g, \"\\\\.?\").replace(spaceOrNBSPRegExp, spaceOrNBSP);\n}\n\nfunction stripInsensitivities(s) {\n  return s\n    .replace(/\\./g, \"\") // ignore dots that were made optional\n    .replace(spaceOrNBSPRegExp, \" \") // interchange space and nbsp\n    .toLowerCase();\n}\n\nfunction oneOf(strings, startIndex) {\n  if (strings === null) {\n    return null;\n  } else {\n    return {\n      regex: RegExp(strings.map(fixListRegex).join(\"|\")),\n      deser: ([s]) =>\n        strings.findIndex((i) => stripInsensitivities(s) === stripInsensitivities(i)) + startIndex,\n    };\n  }\n}\n\nfunction offset(regex, groups) {\n  return { regex, deser: ([, h, m]) => signedOffset(h, m), groups };\n}\n\nfunction simple(regex) {\n  return { regex, deser: ([s]) => s };\n}\n\nfunction escapeToken(value) {\n  return value.replace(/[\\-\\[\\]{}()*+?.,\\\\\\^$|#\\s]/g, \"\\\\$&\");\n}\n\n/**\n * @param token\n * @param {Locale} loc\n */\nfunction unitForToken(token, loc) {\n  const one = digitRegex(loc),\n    two = digitRegex(loc, \"{2}\"),\n    three = digitRegex(loc, \"{3}\"),\n    four = digitRegex(loc, \"{4}\"),\n    six = digitRegex(loc, \"{6}\"),\n    oneOrTwo = digitRegex(loc, \"{1,2}\"),\n    oneToThree = digitRegex(loc, \"{1,3}\"),\n    oneToSix = digitRegex(loc, \"{1,6}\"),\n    oneToNine = digitRegex(loc, \"{1,9}\"),\n    twoToFour = digitRegex(loc, \"{2,4}\"),\n    fourToSix = digitRegex(loc, \"{4,6}\"),\n    literal = (t) => ({ regex: RegExp(escapeToken(t.val)), deser: ([s]) => s, literal: true }),\n    unitate = (t) => {\n      if (token.literal) {\n        return literal(t);\n      }\n      switch (t.val) {\n        // era\n        case \"G\":\n          return oneOf(loc.eras(\"short\"), 0);\n        case \"GG\":\n          return oneOf(loc.eras(\"long\"), 0);\n        // years\n        case \"y\":\n          return intUnit(oneToSix);\n        case \"yy\":\n          return intUnit(twoToFour, untruncateYear);\n        case \"yyyy\":\n          return intUnit(four);\n        case \"yyyyy\":\n          return intUnit(fourToSix);\n        case \"yyyyyy\":\n          return intUnit(six);\n        // months\n        case \"M\":\n          return intUnit(oneOrTwo);\n        case \"MM\":\n          return intUnit(two);\n        case \"MMM\":\n          return oneOf(loc.months(\"short\", true), 1);\n        case \"MMMM\":\n          return oneOf(loc.months(\"long\", true), 1);\n        case \"L\":\n          return intUnit(oneOrTwo);\n        case \"LL\":\n          return intUnit(two);\n        case \"LLL\":\n          return oneOf(loc.months(\"short\", false), 1);\n        case \"LLLL\":\n          return oneOf(loc.months(\"long\", false), 1);\n        // dates\n        case \"d\":\n          return intUnit(oneOrTwo);\n        case \"dd\":\n          return intUnit(two);\n        // ordinals\n        case \"o\":\n          return intUnit(oneToThree);\n        case \"ooo\":\n          return intUnit(three);\n        // time\n        case \"HH\":\n          return intUnit(two);\n        case \"H\":\n          return intUnit(oneOrTwo);\n        case \"hh\":\n          return intUnit(two);\n        case \"h\":\n          return intUnit(oneOrTwo);\n        case \"mm\":\n          return intUnit(two);\n        case \"m\":\n          return intUnit(oneOrTwo);\n        case \"q\":\n          return intUnit(oneOrTwo);\n        case \"qq\":\n          return intUnit(two);\n        case \"s\":\n          return intUnit(oneOrTwo);\n        case \"ss\":\n          return intUnit(two);\n        case \"S\":\n          return intUnit(oneToThree);\n        case \"SSS\":\n          return intUnit(three);\n        case \"u\":\n          return simple(oneToNine);\n        case \"uu\":\n          return simple(oneOrTwo);\n        case \"uuu\":\n          return intUnit(one);\n        // meridiem\n        case \"a\":\n          return oneOf(loc.meridiems(), 0);\n        // weekYear (k)\n        case \"kkkk\":\n          return intUnit(four);\n        case \"kk\":\n          return intUnit(twoToFour, untruncateYear);\n        // weekNumber (W)\n        case \"W\":\n          return intUnit(oneOrTwo);\n        case \"WW\":\n          return intUnit(two);\n        // weekdays\n        case \"E\":\n        case \"c\":\n          return intUnit(one);\n        case \"EEE\":\n          return oneOf(loc.weekdays(\"short\", false), 1);\n        case \"EEEE\":\n          return oneOf(loc.weekdays(\"long\", false), 1);\n        case \"ccc\":\n          return oneOf(loc.weekdays(\"short\", true), 1);\n        case \"cccc\":\n          return oneOf(loc.weekdays(\"long\", true), 1);\n        // offset/zone\n        case \"Z\":\n        case \"ZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(?::(${two.source}))?`), 2);\n        case \"ZZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(${two.source})?`), 2);\n        // we don't support ZZZZ (PST) or ZZZZZ (Pacific Standard Time) in parsing\n        // because we don't have any way to figure out what they are\n        case \"z\":\n          return simple(/[a-z_+-/]{1,256}?/i);\n        // this special-case \"token\" represents a place where a macro-token expanded into a white-space literal\n        // in this case we accept any non-newline white-space\n        case \" \":\n          return simple(/[^\\S\\n\\r]/);\n        default:\n          return literal(t);\n      }\n    };\n\n  const unit = unitate(token) || {\n    invalidReason: MISSING_FTP,\n  };\n\n  unit.token = token;\n\n  return unit;\n}\n\nconst partTypeStyleToTokenVal = {\n  year: {\n    \"2-digit\": \"yy\",\n    numeric: \"yyyyy\",\n  },\n  month: {\n    numeric: \"M\",\n    \"2-digit\": \"MM\",\n    short: \"MMM\",\n    long: \"MMMM\",\n  },\n  day: {\n    numeric: \"d\",\n    \"2-digit\": \"dd\",\n  },\n  weekday: {\n    short: \"EEE\",\n    long: \"EEEE\",\n  },\n  dayperiod: \"a\",\n  dayPeriod: \"a\",\n  hour12: {\n    numeric: \"h\",\n    \"2-digit\": \"hh\",\n  },\n  hour24: {\n    numeric: \"H\",\n    \"2-digit\": \"HH\",\n  },\n  minute: {\n    numeric: \"m\",\n    \"2-digit\": \"mm\",\n  },\n  second: {\n    numeric: \"s\",\n    \"2-digit\": \"ss\",\n  },\n  timeZoneName: {\n    long: \"ZZZZZ\",\n    short: \"ZZZ\",\n  },\n};\n\nfunction tokenForPart(part, formatOpts, resolvedOpts) {\n  const { type, value } = part;\n\n  if (type === \"literal\") {\n    const isSpace = /^\\s+$/.test(value);\n    return {\n      literal: !isSpace,\n      val: isSpace ? \" \" : value,\n    };\n  }\n\n  const style = formatOpts[type];\n\n  // The user might have explicitly specified hour12 or hourCycle\n  // if so, respect their decision\n  // if not, refer back to the resolvedOpts, which are based on the locale\n  let actualType = type;\n  if (type === \"hour\") {\n    if (formatOpts.hour12 != null) {\n      actualType = formatOpts.hour12 ? \"hour12\" : \"hour24\";\n    } else if (formatOpts.hourCycle != null) {\n      if (formatOpts.hourCycle === \"h11\" || formatOpts.hourCycle === \"h12\") {\n        actualType = \"hour12\";\n      } else {\n        actualType = \"hour24\";\n      }\n    } else {\n      // tokens only differentiate between 24 hours or not,\n      // so we do not need to check hourCycle here, which is less supported anyways\n      actualType = resolvedOpts.hour12 ? \"hour12\" : \"hour24\";\n    }\n  }\n  let val = partTypeStyleToTokenVal[actualType];\n  if (typeof val === \"object\") {\n    val = val[style];\n  }\n\n  if (val) {\n    return {\n      literal: false,\n      val,\n    };\n  }\n\n  return undefined;\n}\n\nfunction buildRegex(units) {\n  const re = units.map((u) => u.regex).reduce((f, r) => `${f}(${r.source})`, \"\");\n  return [`^${re}$`, units];\n}\n\nfunction match(input, regex, handlers) {\n  const matches = input.match(regex);\n\n  if (matches) {\n    const all = {};\n    let matchIndex = 1;\n    for (const i in handlers) {\n      if (hasOwnProperty(handlers, i)) {\n        const h = handlers[i],\n          groups = h.groups ? h.groups + 1 : 1;\n        if (!h.literal && h.token) {\n          all[h.token.val[0]] = h.deser(matches.slice(matchIndex, matchIndex + groups));\n        }\n        matchIndex += groups;\n      }\n    }\n    return [matches, all];\n  } else {\n    return [matches, {}];\n  }\n}\n\nfunction dateTimeFromMatches(matches) {\n  const toField = (token) => {\n    switch (token) {\n      case \"S\":\n        return \"millisecond\";\n      case \"s\":\n        return \"second\";\n      case \"m\":\n        return \"minute\";\n      case \"h\":\n      case \"H\":\n        return \"hour\";\n      case \"d\":\n        return \"day\";\n      case \"o\":\n        return \"ordinal\";\n      case \"L\":\n      case \"M\":\n        return \"month\";\n      case \"y\":\n        return \"year\";\n      case \"E\":\n      case \"c\":\n        return \"weekday\";\n      case \"W\":\n        return \"weekNumber\";\n      case \"k\":\n        return \"weekYear\";\n      case \"q\":\n        return \"quarter\";\n      default:\n        return null;\n    }\n  };\n\n  let zone = null;\n  let specificOffset;\n  if (!isUndefined(matches.z)) {\n    zone = IANAZone.create(matches.z);\n  }\n\n  if (!isUndefined(matches.Z)) {\n    if (!zone) {\n      zone = new FixedOffsetZone(matches.Z);\n    }\n    specificOffset = matches.Z;\n  }\n\n  if (!isUndefined(matches.q)) {\n    matches.M = (matches.q - 1) * 3 + 1;\n  }\n\n  if (!isUndefined(matches.h)) {\n    if (matches.h < 12 && matches.a === 1) {\n      matches.h += 12;\n    } else if (matches.h === 12 && matches.a === 0) {\n      matches.h = 0;\n    }\n  }\n\n  if (matches.G === 0 && matches.y) {\n    matches.y = -matches.y;\n  }\n\n  if (!isUndefined(matches.u)) {\n    matches.S = parseMillis(matches.u);\n  }\n\n  const vals = Object.keys(matches).reduce((r, k) => {\n    const f = toField(k);\n    if (f) {\n      r[f] = matches[k];\n    }\n\n    return r;\n  }, {});\n\n  return [vals, zone, specificOffset];\n}\n\nlet dummyDateTimeCache = null;\n\nfunction getDummyDateTime() {\n  if (!dummyDateTimeCache) {\n    dummyDateTimeCache = DateTime.fromMillis(1555555555555);\n  }\n\n  return dummyDateTimeCache;\n}\n\nfunction maybeExpandMacroToken(token, locale) {\n  if (token.literal) {\n    return token;\n  }\n\n  const formatOpts = Formatter.macroTokenToFormatOpts(token.val);\n  const tokens = formatOptsToTokens(formatOpts, locale);\n\n  if (tokens == null || tokens.includes(undefined)) {\n    return token;\n  }\n\n  return tokens;\n}\n\nexport function expandMacroTokens(tokens, locale) {\n  return Array.prototype.concat(...tokens.map((t) => maybeExpandMacroToken(t, locale)));\n}\n\n/**\n * @private\n */\n\nexport class TokenParser {\n  constructor(locale, format) {\n    this.locale = locale;\n    this.format = format;\n    this.tokens = expandMacroTokens(Formatter.parseFormat(format), locale);\n    this.units = this.tokens.map((t) => unitForToken(t, locale));\n    this.disqualifyingUnit = this.units.find((t) => t.invalidReason);\n\n    if (!this.disqualifyingUnit) {\n      const [regexString, handlers] = buildRegex(this.units);\n      this.regex = RegExp(regexString, \"i\");\n      this.handlers = handlers;\n    }\n  }\n\n  explainFromTokens(input) {\n    if (!this.isValid) {\n      return { input, tokens: this.tokens, invalidReason: this.invalidReason };\n    } else {\n      const [rawMatches, matches] = match(input, this.regex, this.handlers),\n        [result, zone, specificOffset] = matches\n          ? dateTimeFromMatches(matches)\n          : [null, null, undefined];\n      if (hasOwnProperty(matches, \"a\") && hasOwnProperty(matches, \"H\")) {\n        throw new ConflictingSpecificationError(\n          \"Can't include meridiem when specifying 24-hour format\"\n        );\n      }\n      return {\n        input,\n        tokens: this.tokens,\n        regex: this.regex,\n        rawMatches,\n        matches,\n        result,\n        zone,\n        specificOffset,\n      };\n    }\n  }\n\n  get isValid() {\n    return !this.disqualifyingUnit;\n  }\n\n  get invalidReason() {\n    return this.disqualifyingUnit ? this.disqualifyingUnit.invalidReason : null;\n  }\n}\n\nexport function explainFromTokens(locale, input, format) {\n  const parser = new TokenParser(locale, format);\n  return parser.explainFromTokens(input);\n}\n\nexport function parseFromTokens(locale, input, format) {\n  const { result, zone, specificOffset, invalidReason } = explainFromTokens(locale, input, format);\n  return [result, zone, specificOffset, invalidReason];\n}\n\nexport function formatOptsToTokens(formatOpts, locale) {\n  if (!formatOpts) {\n    return null;\n  }\n\n  const formatter = Formatter.create(locale, formatOpts);\n  const df = formatter.dtFormatter(getDummyDateTime());\n  const parts = df.formatToParts();\n  const resolvedOpts = df.resolvedOptions();\n  return parts.map((p) => tokenForPart(p, formatOpts, resolvedOpts));\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,YAAY,EAAEC,cAAc,QAAQ,WAAW;AAClG,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAASC,UAAU,EAAEC,WAAW,QAAQ,aAAa;AACrD,SAASC,6BAA6B,QAAQ,cAAc;AAE5D,MAAMC,WAAW,GAAG,mDAAmD;AAEvE,SAASC,OAAOA,CAACC,KAAK,EAAEC,IAAI,GAAIC,CAAC,IAAKA,CAAC,EAAE;EACvC,OAAO;IAAEF,KAAK;IAAEG,KAAK,EAAEA,CAAC,CAACC,CAAC,CAAC,KAAKH,IAAI,CAACL,WAAW,CAACQ,CAAC,CAAC;EAAE,CAAC;AACxD;AAEA,MAAMC,IAAI,GAAGC,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC;AACrC,MAAMC,WAAW,GAAI,KAAIH,IAAK,GAAE;AAChC,MAAMI,iBAAiB,GAAG,IAAIC,MAAM,CAACF,WAAW,EAAE,GAAG,CAAC;AAEtD,SAASG,YAAYA,CAACP,CAAC,EAAE;EACvB;EACA;EACA,OAAOA,CAAC,CAACQ,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAACA,OAAO,CAACH,iBAAiB,EAAED,WAAW,CAAC;AACzE;AAEA,SAASK,oBAAoBA,CAACT,CAAC,EAAE;EAC/B,OAAOA,CAAC,CACLQ,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;EAAA,CACnBA,OAAO,CAACH,iBAAiB,EAAE,GAAG,CAAC,CAAC;EAAA,CAChCK,WAAW,CAAC,CAAC;AAClB;AAEA,SAASC,KAAKA,CAACC,OAAO,EAAEC,UAAU,EAAE;EAClC,IAAID,OAAO,KAAK,IAAI,EAAE;IACpB,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAO;MACLhB,KAAK,EAAEU,MAAM,CAACM,OAAO,CAACE,GAAG,CAACP,YAAY,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC,CAAC;MAClDhB,KAAK,EAAEA,CAAC,CAACC,CAAC,CAAC,KACTY,OAAO,CAACI,SAAS,CAAElB,CAAC,IAAKW,oBAAoB,CAACT,CAAC,CAAC,KAAKS,oBAAoB,CAACX,CAAC,CAAC,CAAC,GAAGe;IACpF,CAAC;EACH;AACF;AAEA,SAASI,MAAMA,CAACrB,KAAK,EAAEsB,MAAM,EAAE;EAC7B,OAAO;IAAEtB,KAAK;IAAEG,KAAK,EAAEA,CAAC,GAAGoB,CAAC,EAAEC,CAAC,CAAC,KAAKnC,YAAY,CAACkC,CAAC,EAAEC,CAAC,CAAC;IAAEF;EAAO,CAAC;AACnE;AAEA,SAASG,MAAMA,CAACzB,KAAK,EAAE;EACrB,OAAO;IAAEA,KAAK;IAAEG,KAAK,EAAEA,CAAC,CAACC,CAAC,CAAC,KAAKA;EAAE,CAAC;AACrC;AAEA,SAASsB,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAOA,KAAK,CAACf,OAAO,CAAC,6BAA6B,EAAE,MAAM,CAAC;AAC7D;;AAEA;AACA;AACA;AACA;AACA,SAASgB,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAChC,MAAMC,GAAG,GAAGpC,UAAU,CAACmC,GAAG,CAAC;IACzBE,GAAG,GAAGrC,UAAU,CAACmC,GAAG,EAAE,KAAK,CAAC;IAC5BG,KAAK,GAAGtC,UAAU,CAACmC,GAAG,EAAE,KAAK,CAAC;IAC9BI,IAAI,GAAGvC,UAAU,CAACmC,GAAG,EAAE,KAAK,CAAC;IAC7BK,GAAG,GAAGxC,UAAU,CAACmC,GAAG,EAAE,KAAK,CAAC;IAC5BM,QAAQ,GAAGzC,UAAU,CAACmC,GAAG,EAAE,OAAO,CAAC;IACnCO,UAAU,GAAG1C,UAAU,CAACmC,GAAG,EAAE,OAAO,CAAC;IACrCQ,QAAQ,GAAG3C,UAAU,CAACmC,GAAG,EAAE,OAAO,CAAC;IACnCS,SAAS,GAAG5C,UAAU,CAACmC,GAAG,EAAE,OAAO,CAAC;IACpCU,SAAS,GAAG7C,UAAU,CAACmC,GAAG,EAAE,OAAO,CAAC;IACpCW,SAAS,GAAG9C,UAAU,CAACmC,GAAG,EAAE,OAAO,CAAC;IACpCY,OAAO,GAAIC,CAAC,KAAM;MAAE3C,KAAK,EAAEU,MAAM,CAACgB,WAAW,CAACiB,CAAC,CAACC,GAAG,CAAC,CAAC;MAAEzC,KAAK,EAAEA,CAAC,CAACC,CAAC,CAAC,KAAKA,CAAC;MAAEsC,OAAO,EAAE;IAAK,CAAC,CAAC;IAC1FG,OAAO,GAAIF,CAAC,IAAK;MACf,IAAId,KAAK,CAACa,OAAO,EAAE;QACjB,OAAOA,OAAO,CAACC,CAAC,CAAC;MACnB;MACA,QAAQA,CAAC,CAACC,GAAG;QACX;QACA,KAAK,GAAG;UACN,OAAO7B,KAAK,CAACe,GAAG,CAACgB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACpC,KAAK,IAAI;UACP,OAAO/B,KAAK,CAACe,GAAG,CAACgB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACnC;QACA,KAAK,GAAG;UACN,OAAO/C,OAAO,CAACuC,QAAQ,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOvC,OAAO,CAACyC,SAAS,EAAEpD,cAAc,CAAC;QAC3C,KAAK,MAAM;UACT,OAAOW,OAAO,CAACmC,IAAI,CAAC;QACtB,KAAK,OAAO;UACV,OAAOnC,OAAO,CAAC0C,SAAS,CAAC;QAC3B,KAAK,QAAQ;UACX,OAAO1C,OAAO,CAACoC,GAAG,CAAC;QACrB;QACA,KAAK,GAAG;UACN,OAAOpC,OAAO,CAACqC,QAAQ,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOrC,OAAO,CAACiC,GAAG,CAAC;QACrB,KAAK,KAAK;UACR,OAAOjB,KAAK,CAACe,GAAG,CAACiB,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC5C,KAAK,MAAM;UACT,OAAOhC,KAAK,CAACe,GAAG,CAACiB,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3C,KAAK,GAAG;UACN,OAAOhD,OAAO,CAACqC,QAAQ,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOrC,OAAO,CAACiC,GAAG,CAAC;QACrB,KAAK,KAAK;UACR,OAAOjB,KAAK,CAACe,GAAG,CAACiB,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QAC7C,KAAK,MAAM;UACT,OAAOhC,KAAK,CAACe,GAAG,CAACiB,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5C;QACA,KAAK,GAAG;UACN,OAAOhD,OAAO,CAACqC,QAAQ,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOrC,OAAO,CAACiC,GAAG,CAAC;QACrB;QACA,KAAK,GAAG;UACN,OAAOjC,OAAO,CAACsC,UAAU,CAAC;QAC5B,KAAK,KAAK;UACR,OAAOtC,OAAO,CAACkC,KAAK,CAAC;QACvB;QACA,KAAK,IAAI;UACP,OAAOlC,OAAO,CAACiC,GAAG,CAAC;QACrB,KAAK,GAAG;UACN,OAAOjC,OAAO,CAACqC,QAAQ,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOrC,OAAO,CAACiC,GAAG,CAAC;QACrB,KAAK,GAAG;UACN,OAAOjC,OAAO,CAACqC,QAAQ,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOrC,OAAO,CAACiC,GAAG,CAAC;QACrB,KAAK,GAAG;UACN,OAAOjC,OAAO,CAACqC,QAAQ,CAAC;QAC1B,KAAK,GAAG;UACN,OAAOrC,OAAO,CAACqC,QAAQ,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOrC,OAAO,CAACiC,GAAG,CAAC;QACrB,KAAK,GAAG;UACN,OAAOjC,OAAO,CAACqC,QAAQ,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOrC,OAAO,CAACiC,GAAG,CAAC;QACrB,KAAK,GAAG;UACN,OAAOjC,OAAO,CAACsC,UAAU,CAAC;QAC5B,KAAK,KAAK;UACR,OAAOtC,OAAO,CAACkC,KAAK,CAAC;QACvB,KAAK,GAAG;UACN,OAAOR,MAAM,CAACc,SAAS,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOd,MAAM,CAACW,QAAQ,CAAC;QACzB,KAAK,KAAK;UACR,OAAOrC,OAAO,CAACgC,GAAG,CAAC;QACrB;QACA,KAAK,GAAG;UACN,OAAOhB,KAAK,CAACe,GAAG,CAACkB,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QAClC;QACA,KAAK,MAAM;UACT,OAAOjD,OAAO,CAACmC,IAAI,CAAC;QACtB,KAAK,IAAI;UACP,OAAOnC,OAAO,CAACyC,SAAS,EAAEpD,cAAc,CAAC;QAC3C;QACA,KAAK,GAAG;UACN,OAAOW,OAAO,CAACqC,QAAQ,CAAC;QAC1B,KAAK,IAAI;UACP,OAAOrC,OAAO,CAACiC,GAAG,CAAC;QACrB;QACA,KAAK,GAAG;QACR,KAAK,GAAG;UACN,OAAOjC,OAAO,CAACgC,GAAG,CAAC;QACrB,KAAK,KAAK;UACR,OAAOhB,KAAK,CAACe,GAAG,CAACmB,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/C,KAAK,MAAM;UACT,OAAOlC,KAAK,CAACe,GAAG,CAACmB,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QAC9C,KAAK,KAAK;UACR,OAAOlC,KAAK,CAACe,GAAG,CAACmB,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9C,KAAK,MAAM;UACT,OAAOlC,KAAK,CAACe,GAAG,CAACmB,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7C;QACA,KAAK,GAAG;QACR,KAAK,IAAI;UACP,OAAO5B,MAAM,CAAC,IAAIX,MAAM,CAAE,QAAO0B,QAAQ,CAACc,MAAO,SAAQlB,GAAG,CAACkB,MAAO,KAAI,CAAC,EAAE,CAAC,CAAC;QAC/E,KAAK,KAAK;UACR,OAAO7B,MAAM,CAAC,IAAIX,MAAM,CAAE,QAAO0B,QAAQ,CAACc,MAAO,KAAIlB,GAAG,CAACkB,MAAO,IAAG,CAAC,EAAE,CAAC,CAAC;QAC1E;QACA;QACA,KAAK,GAAG;UACN,OAAOzB,MAAM,CAAC,oBAAoB,CAAC;QACrC;QACA;QACA,KAAK,GAAG;UACN,OAAOA,MAAM,CAAC,WAAW,CAAC;QAC5B;UACE,OAAOiB,OAAO,CAACC,CAAC,CAAC;MACrB;IACF,CAAC;EAEH,MAAMQ,IAAI,GAAGN,OAAO,CAAChB,KAAK,CAAC,IAAI;IAC7BuB,aAAa,EAAEtD;EACjB,CAAC;EAEDqD,IAAI,CAACtB,KAAK,GAAGA,KAAK;EAElB,OAAOsB,IAAI;AACb;AAEA,MAAME,uBAAuB,GAAG;EAC9BC,IAAI,EAAE;IACJ,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE;EACX,CAAC;EACDC,KAAK,EAAE;IACLD,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE,IAAI;IACfE,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE;EACR,CAAC;EACDC,GAAG,EAAE;IACHJ,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE;EACb,CAAC;EACDK,OAAO,EAAE;IACPH,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE;EACR,CAAC;EACDG,SAAS,EAAE,GAAG;EACdC,SAAS,EAAE,GAAG;EACdC,MAAM,EAAE;IACNR,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE;EACb,CAAC;EACDS,MAAM,EAAE;IACNT,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE;EACb,CAAC;EACDU,MAAM,EAAE;IACNV,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE;EACb,CAAC;EACDW,MAAM,EAAE;IACNX,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE;EACb,CAAC;EACDY,YAAY,EAAE;IACZT,IAAI,EAAE,OAAO;IACbD,KAAK,EAAE;EACT;AACF,CAAC;AAED,SAASW,YAAYA,CAACC,IAAI,EAAEC,UAAU,EAAEC,YAAY,EAAE;EACpD,MAAM;IAAEC,IAAI;IAAE7C;EAAM,CAAC,GAAG0C,IAAI;EAE5B,IAAIG,IAAI,KAAK,SAAS,EAAE;IACtB,MAAMC,OAAO,GAAG,OAAO,CAACC,IAAI,CAAC/C,KAAK,CAAC;IACnC,OAAO;MACLe,OAAO,EAAE,CAAC+B,OAAO;MACjB7B,GAAG,EAAE6B,OAAO,GAAG,GAAG,GAAG9C;IACvB,CAAC;EACH;EAEA,MAAMgD,KAAK,GAAGL,UAAU,CAACE,IAAI,CAAC;;EAE9B;EACA;EACA;EACA,IAAII,UAAU,GAAGJ,IAAI;EACrB,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,IAAIF,UAAU,CAACP,MAAM,IAAI,IAAI,EAAE;MAC7Ba,UAAU,GAAGN,UAAU,CAACP,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACtD,CAAC,MAAM,IAAIO,UAAU,CAACO,SAAS,IAAI,IAAI,EAAE;MACvC,IAAIP,UAAU,CAACO,SAAS,KAAK,KAAK,IAAIP,UAAU,CAACO,SAAS,KAAK,KAAK,EAAE;QACpED,UAAU,GAAG,QAAQ;MACvB,CAAC,MAAM;QACLA,UAAU,GAAG,QAAQ;MACvB;IACF,CAAC,MAAM;MACL;MACA;MACAA,UAAU,GAAGL,YAAY,CAACR,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACxD;EACF;EACA,IAAInB,GAAG,GAAGS,uBAAuB,CAACuB,UAAU,CAAC;EAC7C,IAAI,OAAOhC,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAGA,GAAG,CAAC+B,KAAK,CAAC;EAClB;EAEA,IAAI/B,GAAG,EAAE;IACP,OAAO;MACLF,OAAO,EAAE,KAAK;MACdE;IACF,CAAC;EACH;EAEA,OAAOkC,SAAS;AAClB;AAEA,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,MAAMC,EAAE,GAAGD,KAAK,CAAC9D,GAAG,CAAEgE,CAAC,IAAKA,CAAC,CAAClF,KAAK,CAAC,CAACmF,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAM,GAAED,CAAE,IAAGC,CAAC,CAACnC,MAAO,GAAE,EAAE,EAAE,CAAC;EAC9E,OAAO,CAAE,IAAG+B,EAAG,GAAE,EAAED,KAAK,CAAC;AAC3B;AAEA,SAASM,KAAKA,CAACC,KAAK,EAAEvF,KAAK,EAAEwF,QAAQ,EAAE;EACrC,MAAMC,OAAO,GAAGF,KAAK,CAACD,KAAK,CAACtF,KAAK,CAAC;EAElC,IAAIyF,OAAO,EAAE;IACX,MAAMC,GAAG,GAAG,CAAC,CAAC;IACd,IAAIC,UAAU,GAAG,CAAC;IAClB,KAAK,MAAMzF,CAAC,IAAIsF,QAAQ,EAAE;MACxB,IAAIlG,cAAc,CAACkG,QAAQ,EAAEtF,CAAC,CAAC,EAAE;QAC/B,MAAMqB,CAAC,GAAGiE,QAAQ,CAACtF,CAAC,CAAC;UACnBoB,MAAM,GAAGC,CAAC,CAACD,MAAM,GAAGC,CAAC,CAACD,MAAM,GAAG,CAAC,GAAG,CAAC;QACtC,IAAI,CAACC,CAAC,CAACmB,OAAO,IAAInB,CAAC,CAACM,KAAK,EAAE;UACzB6D,GAAG,CAACnE,CAAC,CAACM,KAAK,CAACe,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGrB,CAAC,CAACpB,KAAK,CAACsF,OAAO,CAACG,KAAK,CAACD,UAAU,EAAEA,UAAU,GAAGrE,MAAM,CAAC,CAAC;QAC/E;QACAqE,UAAU,IAAIrE,MAAM;MACtB;IACF;IACA,OAAO,CAACmE,OAAO,EAAEC,GAAG,CAAC;EACvB,CAAC,MAAM;IACL,OAAO,CAACD,OAAO,EAAE,CAAC,CAAC,CAAC;EACtB;AACF;AAEA,SAASI,mBAAmBA,CAACJ,OAAO,EAAE;EACpC,MAAMK,OAAO,GAAIjE,KAAK,IAAK;IACzB,QAAQA,KAAK;MACX,KAAK,GAAG;QACN,OAAO,aAAa;MACtB,KAAK,GAAG;QACN,OAAO,QAAQ;MACjB,KAAK,GAAG;QACN,OAAO,QAAQ;MACjB,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,MAAM;MACf,KAAK,GAAG;QACN,OAAO,KAAK;MACd,KAAK,GAAG;QACN,OAAO,SAAS;MAClB,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,OAAO;MAChB,KAAK,GAAG;QACN,OAAO,MAAM;MACf,KAAK,GAAG;MACR,KAAK,GAAG;QACN,OAAO,SAAS;MAClB,KAAK,GAAG;QACN,OAAO,YAAY;MACrB,KAAK,GAAG;QACN,OAAO,UAAU;MACnB,KAAK,GAAG;QACN,OAAO,SAAS;MAClB;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,IAAIkE,IAAI,GAAG,IAAI;EACf,IAAIC,cAAc;EAClB,IAAI,CAAC7G,WAAW,CAACsG,OAAO,CAACQ,CAAC,CAAC,EAAE;IAC3BF,IAAI,GAAGtG,QAAQ,CAACyG,MAAM,CAACT,OAAO,CAACQ,CAAC,CAAC;EACnC;EAEA,IAAI,CAAC9G,WAAW,CAACsG,OAAO,CAACU,CAAC,CAAC,EAAE;IAC3B,IAAI,CAACJ,IAAI,EAAE;MACTA,IAAI,GAAG,IAAIvG,eAAe,CAACiG,OAAO,CAACU,CAAC,CAAC;IACvC;IACAH,cAAc,GAAGP,OAAO,CAACU,CAAC;EAC5B;EAEA,IAAI,CAAChH,WAAW,CAACsG,OAAO,CAACW,CAAC,CAAC,EAAE;IAC3BX,OAAO,CAACY,CAAC,GAAG,CAACZ,OAAO,CAACW,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;EACrC;EAEA,IAAI,CAACjH,WAAW,CAACsG,OAAO,CAAClE,CAAC,CAAC,EAAE;IAC3B,IAAIkE,OAAO,CAAClE,CAAC,GAAG,EAAE,IAAIkE,OAAO,CAACa,CAAC,KAAK,CAAC,EAAE;MACrCb,OAAO,CAAClE,CAAC,IAAI,EAAE;IACjB,CAAC,MAAM,IAAIkE,OAAO,CAAClE,CAAC,KAAK,EAAE,IAAIkE,OAAO,CAACa,CAAC,KAAK,CAAC,EAAE;MAC9Cb,OAAO,CAAClE,CAAC,GAAG,CAAC;IACf;EACF;EAEA,IAAIkE,OAAO,CAACc,CAAC,KAAK,CAAC,IAAId,OAAO,CAACe,CAAC,EAAE;IAChCf,OAAO,CAACe,CAAC,GAAG,CAACf,OAAO,CAACe,CAAC;EACxB;EAEA,IAAI,CAACrH,WAAW,CAACsG,OAAO,CAACP,CAAC,CAAC,EAAE;IAC3BO,OAAO,CAACgB,CAAC,GAAGvH,WAAW,CAACuG,OAAO,CAACP,CAAC,CAAC;EACpC;EAEA,MAAMwB,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACnB,OAAO,CAAC,CAACN,MAAM,CAAC,CAACE,CAAC,EAAEwB,CAAC,KAAK;IACjD,MAAMzB,CAAC,GAAGU,OAAO,CAACe,CAAC,CAAC;IACpB,IAAIzB,CAAC,EAAE;MACLC,CAAC,CAACD,CAAC,CAAC,GAAGK,OAAO,CAACoB,CAAC,CAAC;IACnB;IAEA,OAAOxB,CAAC;EACV,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,OAAO,CAACqB,IAAI,EAAEX,IAAI,EAAEC,cAAc,CAAC;AACrC;AAEA,IAAIc,kBAAkB,GAAG,IAAI;AAE7B,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,IAAI,CAACD,kBAAkB,EAAE;IACvBA,kBAAkB,GAAGpH,QAAQ,CAACsH,UAAU,CAAC,aAAa,CAAC;EACzD;EAEA,OAAOF,kBAAkB;AAC3B;AAEA,SAASG,qBAAqBA,CAACpF,KAAK,EAAEqF,MAAM,EAAE;EAC5C,IAAIrF,KAAK,CAACa,OAAO,EAAE;IACjB,OAAOb,KAAK;EACd;EAEA,MAAMyC,UAAU,GAAG/E,SAAS,CAAC4H,sBAAsB,CAACtF,KAAK,CAACe,GAAG,CAAC;EAC9D,MAAMwE,MAAM,GAAGC,kBAAkB,CAAC/C,UAAU,EAAE4C,MAAM,CAAC;EAErD,IAAIE,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACE,QAAQ,CAACxC,SAAS,CAAC,EAAE;IAChD,OAAOjD,KAAK;EACd;EAEA,OAAOuF,MAAM;AACf;AAEA,OAAO,SAASG,iBAAiBA,CAACH,MAAM,EAAEF,MAAM,EAAE;EAChD,OAAOM,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,GAAGN,MAAM,CAAClG,GAAG,CAAEyB,CAAC,IAAKsE,qBAAqB,CAACtE,CAAC,EAAEuE,MAAM,CAAC,CAAC,CAAC;AACvF;;AAEA;AACA;AACA;;AAEA,OAAO,MAAMS,WAAW,CAAC;EACvBC,WAAWA,CAACV,MAAM,EAAEW,MAAM,EAAE;IAC1B,IAAI,CAACX,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACW,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACT,MAAM,GAAGG,iBAAiB,CAAChI,SAAS,CAACuI,WAAW,CAACD,MAAM,CAAC,EAAEX,MAAM,CAAC;IACtE,IAAI,CAAClC,KAAK,GAAG,IAAI,CAACoC,MAAM,CAAClG,GAAG,CAAEyB,CAAC,IAAKf,YAAY,CAACe,CAAC,EAAEuE,MAAM,CAAC,CAAC;IAC5D,IAAI,CAACa,iBAAiB,GAAG,IAAI,CAAC/C,KAAK,CAACgD,IAAI,CAAErF,CAAC,IAAKA,CAAC,CAACS,aAAa,CAAC;IAEhE,IAAI,CAAC,IAAI,CAAC2E,iBAAiB,EAAE;MAC3B,MAAM,CAACE,WAAW,EAAEzC,QAAQ,CAAC,GAAGT,UAAU,CAAC,IAAI,CAACC,KAAK,CAAC;MACtD,IAAI,CAAChF,KAAK,GAAGU,MAAM,CAACuH,WAAW,EAAE,GAAG,CAAC;MACrC,IAAI,CAACzC,QAAQ,GAAGA,QAAQ;IAC1B;EACF;EAEA0C,iBAAiBA,CAAC3C,KAAK,EAAE;IACvB,IAAI,CAAC,IAAI,CAAC4C,OAAO,EAAE;MACjB,OAAO;QAAE5C,KAAK;QAAE6B,MAAM,EAAE,IAAI,CAACA,MAAM;QAAEhE,aAAa,EAAE,IAAI,CAACA;MAAc,CAAC;IAC1E,CAAC,MAAM;MACL,MAAM,CAACgF,UAAU,EAAE3C,OAAO,CAAC,GAAGH,KAAK,CAACC,KAAK,EAAE,IAAI,CAACvF,KAAK,EAAE,IAAI,CAACwF,QAAQ,CAAC;QACnE,CAAC6C,MAAM,EAAEtC,IAAI,EAAEC,cAAc,CAAC,GAAGP,OAAO,GACpCI,mBAAmB,CAACJ,OAAO,CAAC,GAC5B,CAAC,IAAI,EAAE,IAAI,EAAEX,SAAS,CAAC;MAC7B,IAAIxF,cAAc,CAACmG,OAAO,EAAE,GAAG,CAAC,IAAInG,cAAc,CAACmG,OAAO,EAAE,GAAG,CAAC,EAAE;QAChE,MAAM,IAAI5F,6BAA6B,CACrC,uDACF,CAAC;MACH;MACA,OAAO;QACL0F,KAAK;QACL6B,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBpH,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBoI,UAAU;QACV3C,OAAO;QACP4C,MAAM;QACNtC,IAAI;QACJC;MACF,CAAC;IACH;EACF;EAEA,IAAImC,OAAOA,CAAA,EAAG;IACZ,OAAO,CAAC,IAAI,CAACJ,iBAAiB;EAChC;EAEA,IAAI3E,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC2E,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC3E,aAAa,GAAG,IAAI;EAC7E;AACF;AAEA,OAAO,SAAS8E,iBAAiBA,CAAChB,MAAM,EAAE3B,KAAK,EAAEsC,MAAM,EAAE;EACvD,MAAMS,MAAM,GAAG,IAAIX,WAAW,CAACT,MAAM,EAAEW,MAAM,CAAC;EAC9C,OAAOS,MAAM,CAACJ,iBAAiB,CAAC3C,KAAK,CAAC;AACxC;AAEA,OAAO,SAASgD,eAAeA,CAACrB,MAAM,EAAE3B,KAAK,EAAEsC,MAAM,EAAE;EACrD,MAAM;IAAEQ,MAAM;IAAEtC,IAAI;IAAEC,cAAc;IAAE5C;EAAc,CAAC,GAAG8E,iBAAiB,CAAChB,MAAM,EAAE3B,KAAK,EAAEsC,MAAM,CAAC;EAChG,OAAO,CAACQ,MAAM,EAAEtC,IAAI,EAAEC,cAAc,EAAE5C,aAAa,CAAC;AACtD;AAEA,OAAO,SAASiE,kBAAkBA,CAAC/C,UAAU,EAAE4C,MAAM,EAAE;EACrD,IAAI,CAAC5C,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,MAAMkE,SAAS,GAAGjJ,SAAS,CAAC2G,MAAM,CAACgB,MAAM,EAAE5C,UAAU,CAAC;EACtD,MAAMmE,EAAE,GAAGD,SAAS,CAACE,WAAW,CAAC3B,gBAAgB,CAAC,CAAC,CAAC;EACpD,MAAM4B,KAAK,GAAGF,EAAE,CAACG,aAAa,CAAC,CAAC;EAChC,MAAMrE,YAAY,GAAGkE,EAAE,CAACI,eAAe,CAAC,CAAC;EACzC,OAAOF,KAAK,CAACzH,GAAG,CAAE4H,CAAC,IAAK1E,YAAY,CAAC0E,CAAC,EAAExE,UAAU,EAAEC,YAAY,CAAC,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}