{"ast": null, "code": "import { of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nclass SmartDashboardService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.engineUrl;\n  }\n  /**\n   * Get dashboard configuration\n   */\n  getDashboardConfig() {\n    return this.http.get(`${this.baseUrl}api/smart-dashboard/config`).pipe(catchError(error => {\n      console.warn('Dashboard config API not available:', error.status);\n      // Return default config\n      return of({\n        status: 'success',\n        data: {\n          chart_colors: this.getChartColors(),\n          chart_types: {\n            bar: 'Bar Chart',\n            line: 'Line Chart',\n            doughnut: 'Doughnut Chart',\n            pie: 'Pie Chart'\n          },\n          currency: {\n            code: 'INR',\n            symbol: '₹'\n          },\n          dashboard_types: ['purchase', 'grn']\n        }\n      });\n    }));\n  }\n  /**\n   * Get smart dashboard data with filters and optional query\n   */\n  getSmartDashboardData(request) {\n    return this.http.post(`${this.baseUrl}api/smart-dashboard/smart_ask`, request).pipe(catchError(error => {\n      console.warn('Smart dashboard API not available:', error.status, error.message);\n      // Return empty response to let component handle with sample data\n      return of({\n        status: 'error',\n        data: {\n          charts: [],\n          summary_items: []\n        }\n      });\n    }));\n  }\n  /**\n   * Get default dashboard data for a tenant\n   */\n  getDefaultDashboardData(tenantId, filters) {\n    const request = {\n      tenant_id: tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true\n    };\n    return this.getSmartDashboardData(request);\n  }\n  /**\n   * Query dashboard with natural language\n   */\n  queryDashboard(tenantId, query, filters) {\n    const request = {\n      tenant_id: tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false\n    };\n    return this.getSmartDashboardData(request);\n  }\n  /**\n   * Format currency value\n   */\n  formatCurrency(value, currency = '₹') {\n    if (value >= 10000000) {\n      // 1 crore\n      return `${currency}${(value / 10000000).toFixed(2)}Cr`;\n    } else if (value >= 100000) {\n      // 1 lakh\n      return `${currency}${(value / 100000).toFixed(2)}L`;\n    } else if (value >= 1000) {\n      // 1 thousand\n      return `${currency}${(value / 1000).toFixed(2)}K`;\n    } else {\n      return `${currency}${value.toFixed(2)}`;\n    }\n  }\n  /**\n   * Format number with Indian numbering system\n   */\n  formatNumber(value) {\n    if (value >= 10000000) {\n      // 1 crore\n      return `${(value / 10000000).toFixed(2)}Cr`;\n    } else if (value >= 100000) {\n      // 1 lakh\n      return `${(value / 100000).toFixed(2)}L`;\n    } else if (value >= 1000) {\n      // 1 thousand\n      return `${(value / 1000).toFixed(2)}K`;\n    } else {\n      return value.toString();\n    }\n  }\n  /**\n   * Get chart colors from configuration\n   */\n  getChartColors() {\n    return ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3', '#e55a2b', '#ff7f50', '#ffa500', '#ff8c00', '#ff6347' // Tomato\n    ];\n  }\n  /**\n   * Process chart data for ngx-charts\n   */\n  processChartData(chart) {\n    const colors = this.getChartColors();\n    switch (chart.type) {\n      case 'line':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map((dataset, index) => ({\n            ...dataset,\n            borderColor: colors[index % colors.length],\n            backgroundColor: colors[index % colors.length] + '20',\n            fill: false,\n            tension: 0.1\n          }))\n        };\n      case 'bar':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map(dataset => ({\n            ...dataset,\n            backgroundColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderWidth: 1\n          }))\n        };\n      case 'doughnut':\n      case 'pie':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map(dataset => ({\n            ...dataset,\n            backgroundColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderWidth: 2\n          }))\n        };\n      default:\n        return chart.data;\n    }\n  }\n  /**\n   * Get icon for summary card based on data type\n   */\n  getSummaryCardIcon(dataType, label) {\n    const iconMap = {\n      'currency': 'attach_money',\n      'number': 'numbers',\n      'percentage': 'percent',\n      'vendor': 'store'\n    };\n    // Check label for specific icons\n    if (label.toLowerCase().includes('order')) {\n      return 'shopping_cart';\n    } else if (label.toLowerCase().includes('amount') || label.toLowerCase().includes('value')) {\n      return 'attach_money';\n    } else if (label.toLowerCase().includes('vendor') || label.toLowerCase().includes('supplier')) {\n      return 'store';\n    } else if (label.toLowerCase().includes('average')) {\n      return 'trending_up';\n    }\n    return iconMap[dataType] || 'analytics';\n  }\n  /**\n   * Validate date range\n   */\n  validateDateRange(startDate, endDate) {\n    if (!startDate || !endDate) {\n      return false;\n    }\n    // End date should be after start date\n    if (endDate <= startDate) {\n      return false;\n    }\n    // Date range should not be more than 1 year\n    const oneYear = 365 * 24 * 60 * 60 * 1000;\n    if (endDate.getTime() - startDate.getTime() > oneYear) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Get default date range (last 30 days)\n   */\n  getDefaultDateRange() {\n    const endDate = new Date();\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - 30);\n    return {\n      startDate,\n      endDate\n    };\n  }\n  static {\n    this.ɵfac = function SmartDashboardService_Factory(t) {\n      return new (t || SmartDashboardService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SmartDashboardService,\n      factory: SmartDashboardService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { SmartDashboardService };", "map": {"version": 3, "names": ["of", "catchError", "environment", "SmartDashboardService", "constructor", "http", "baseUrl", "engineUrl", "getDashboardConfig", "get", "pipe", "error", "console", "warn", "status", "data", "chart_colors", "getChartColors", "chart_types", "bar", "line", "doughnut", "pie", "currency", "code", "symbol", "dashboard_types", "getSmartDashboardData", "request", "post", "message", "charts", "summary_items", "getDefaultDashboardData", "tenantId", "filters", "tenant_id", "user_query", "use_default_charts", "queryDashboard", "query", "formatCurrency", "value", "toFixed", "formatNumber", "toString", "processChartData", "chart", "colors", "type", "datasets", "map", "dataset", "index", "borderColor", "length", "backgroundColor", "fill", "tension", "labels", "_", "i", "borderWidth", "getSummaryCardIcon", "dataType", "label", "iconMap", "toLowerCase", "includes", "validateDateRange", "startDate", "endDate", "oneYear", "getTime", "getDefaultDateRange", "Date", "setDate", "getDate", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\services\\smart-dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface DashboardFilters {\n  locations: string[];\n  startDate: string;\n  endDate: string;\n  baseDate: string;\n}\n\nexport interface SmartDashboardRequest {\n  tenant_id: string;\n  filters: DashboardFilters;\n  user_query: string;\n  use_default_charts: boolean;\n}\n\nexport interface SummaryItem {\n  icon: string;\n  value: string;\n  label: string;\n  data_type: string;\n}\n\nexport interface ChartDataset {\n  label: string;\n  data: number[];\n  backgroundColor: string[];\n  borderColor: string[];\n}\n\nexport interface ChartData {\n  labels: string[];\n  datasets: ChartDataset[];\n}\n\nexport interface Chart {\n  id: string;\n  title: string;\n  type: string;\n  data: ChartData;\n}\n\nexport interface DashboardResponse {\n  charts: Chart[];\n  summary_items: SummaryItem[];\n}\n\nexport interface SmartDashboardApiResponse {\n  status: string;\n  data: DashboardResponse;\n}\n\nexport interface DashboardConfig {\n  chart_colors: string[];\n  chart_types: { [key: string]: string };\n  currency: {\n    code: string;\n    symbol: string;\n  };\n  dashboard_types: string[];\n}\n\nexport interface DashboardConfigResponse {\n  status: string;\n  data: DashboardConfig;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SmartDashboardService {\n  private baseUrl: string = environment.engineUrl;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Get dashboard configuration\n   */\n  getDashboardConfig(): Observable<DashboardConfigResponse> {\n    return this.http.get<DashboardConfigResponse>(`${this.baseUrl}api/smart-dashboard/config`)\n      .pipe(\n        catchError((error: HttpErrorResponse) => {\n          console.warn('Dashboard config API not available:', error.status);\n          // Return default config\n          return of({\n            status: 'success',\n            data: {\n              chart_colors: this.getChartColors(),\n              chart_types: { bar: 'Bar Chart', line: 'Line Chart', doughnut: 'Doughnut Chart', pie: 'Pie Chart' },\n              currency: { code: 'INR', symbol: '₹' },\n              dashboard_types: ['purchase', 'grn']\n            }\n          });\n        })\n      );\n  }\n\n  /**\n   * Get smart dashboard data with filters and optional query\n   */\n  getSmartDashboardData(request: SmartDashboardRequest): Observable<SmartDashboardApiResponse> {\n    return this.http.post<SmartDashboardApiResponse>(`${this.baseUrl}api/smart-dashboard/smart_ask`, request)\n      .pipe(\n        catchError((error: HttpErrorResponse) => {\n          console.warn('Smart dashboard API not available:', error.status, error.message);\n\n          // Return empty response to let component handle with sample data\n          return of({\n            status: 'error',\n            data: {\n              charts: [],\n              summary_items: []\n            }\n          });\n        })\n      );\n  }\n\n  /**\n   * Get default dashboard data for a tenant\n   */\n  getDefaultDashboardData(tenantId: string, filters: DashboardFilters): Observable<SmartDashboardApiResponse> {\n    const request: SmartDashboardRequest = {\n      tenant_id: tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true\n    };\n\n    return this.getSmartDashboardData(request);\n  }\n\n  /**\n   * Query dashboard with natural language\n   */\n  queryDashboard(tenantId: string, query: string, filters: DashboardFilters): Observable<SmartDashboardApiResponse> {\n    const request: SmartDashboardRequest = {\n      tenant_id: tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false\n    };\n\n    return this.getSmartDashboardData(request);\n  }\n\n  /**\n   * Format currency value\n   */\n  formatCurrency(value: number, currency: string = '₹'): string {\n    if (value >= 10000000) { // 1 crore\n      return `${currency}${(value / 10000000).toFixed(2)}Cr`;\n    } else if (value >= 100000) { // 1 lakh\n      return `${currency}${(value / 100000).toFixed(2)}L`;\n    } else if (value >= 1000) { // 1 thousand\n      return `${currency}${(value / 1000).toFixed(2)}K`;\n    } else {\n      return `${currency}${value.toFixed(2)}`;\n    }\n  }\n\n  /**\n   * Format number with Indian numbering system\n   */\n  formatNumber(value: number): string {\n    if (value >= 10000000) { // 1 crore\n      return `${(value / 10000000).toFixed(2)}Cr`;\n    } else if (value >= 100000) { // 1 lakh\n      return `${(value / 100000).toFixed(2)}L`;\n    } else if (value >= 1000) { // 1 thousand\n      return `${(value / 1000).toFixed(2)}K`;\n    } else {\n      return value.toString();\n    }\n  }\n\n  /**\n   * Get chart colors from configuration\n   */\n  getChartColors(): string[] {\n    return [\n      '#ff6b35', // Orange\n      '#ffa66f', // Light Orange\n      '#ff8b4d', // Medium Orange\n      '#ff9966', // Peach\n      '#ffd1b3', // Light Peach\n      '#e55a2b', // Dark Orange\n      '#ff7f50', // Coral\n      '#ffa500', // Orange\n      '#ff8c00', // Dark Orange\n      '#ff6347'  // Tomato\n    ];\n  }\n\n  /**\n   * Process chart data for ngx-charts\n   */\n  processChartData(chart: Chart): any {\n    const colors = this.getChartColors();\n    \n    switch (chart.type) {\n      case 'line':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map((dataset, index) => ({\n            ...dataset,\n            borderColor: colors[index % colors.length],\n            backgroundColor: colors[index % colors.length] + '20', // 20% opacity\n            fill: false,\n            tension: 0.1\n          }))\n        };\n\n      case 'bar':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map((dataset) => ({\n            ...dataset,\n            backgroundColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderWidth: 1\n          }))\n        };\n\n      case 'doughnut':\n      case 'pie':\n        return {\n          ...chart.data,\n          datasets: chart.data.datasets.map(dataset => ({\n            ...dataset,\n            backgroundColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderColor: chart.data.labels.map((_, i) => colors[i % colors.length]),\n            borderWidth: 2\n          }))\n        };\n\n      default:\n        return chart.data;\n    }\n  }\n\n  /**\n   * Get icon for summary card based on data type\n   */\n  getSummaryCardIcon(dataType: string, label: string): string {\n    const iconMap: { [key: string]: string } = {\n      'currency': 'attach_money',\n      'number': 'numbers',\n      'percentage': 'percent',\n      'vendor': 'store'\n    };\n\n    // Check label for specific icons\n    if (label.toLowerCase().includes('order')) {\n      return 'shopping_cart';\n    } else if (label.toLowerCase().includes('amount') || label.toLowerCase().includes('value')) {\n      return 'attach_money';\n    } else if (label.toLowerCase().includes('vendor') || label.toLowerCase().includes('supplier')) {\n      return 'store';\n    } else if (label.toLowerCase().includes('average')) {\n      return 'trending_up';\n    }\n\n    return iconMap[dataType] || 'analytics';\n  }\n\n  /**\n   * Validate date range\n   */\n  validateDateRange(startDate: Date, endDate: Date): boolean {\n    if (!startDate || !endDate) {\n      return false;\n    }\n\n    // End date should be after start date\n    if (endDate <= startDate) {\n      return false;\n    }\n\n    // Date range should not be more than 1 year\n    const oneYear = 365 * 24 * 60 * 60 * 1000;\n    if (endDate.getTime() - startDate.getTime() > oneYear) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Get default date range (last 30 days)\n   */\n  getDefaultDateRange(): { startDate: Date; endDate: Date } {\n    const endDate = new Date();\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - 30);\n\n    return { startDate, endDate };\n  }\n}\n"], "mappings": "AAEA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,WAAW,QAAQ,gCAAgC;;;AAmE5D,MAGaC,qBAAqB;EAGhCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,OAAO,GAAWJ,WAAW,CAACK,SAAS;EAER;EAEvC;;;EAGAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAA0B,GAAG,IAAI,CAACH,OAAO,4BAA4B,CAAC,CACvFI,IAAI,CACHT,UAAU,CAAEU,KAAwB,IAAI;MACtCC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEF,KAAK,CAACG,MAAM,CAAC;MACjE;MACA,OAAOd,EAAE,CAAC;QACRc,MAAM,EAAE,SAAS;QACjBC,IAAI,EAAE;UACJC,YAAY,EAAE,IAAI,CAACC,cAAc,EAAE;UACnCC,WAAW,EAAE;YAAEC,GAAG,EAAE,WAAW;YAAEC,IAAI,EAAE,YAAY;YAAEC,QAAQ,EAAE,gBAAgB;YAAEC,GAAG,EAAE;UAAW,CAAE;UACnGC,QAAQ,EAAE;YAAEC,IAAI,EAAE,KAAK;YAAEC,MAAM,EAAE;UAAG,CAAE;UACtCC,eAAe,EAAE,CAAC,UAAU,EAAE,KAAK;;OAEtC,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAC,qBAAqBA,CAACC,OAA8B;IAClD,OAAO,IAAI,CAACvB,IAAI,CAACwB,IAAI,CAA4B,GAAG,IAAI,CAACvB,OAAO,+BAA+B,EAAEsB,OAAO,CAAC,CACtGlB,IAAI,CACHT,UAAU,CAAEU,KAAwB,IAAI;MACtCC,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEF,KAAK,CAACG,MAAM,EAAEH,KAAK,CAACmB,OAAO,CAAC;MAE/E;MACA,OAAO9B,EAAE,CAAC;QACRc,MAAM,EAAE,OAAO;QACfC,IAAI,EAAE;UACJgB,MAAM,EAAE,EAAE;UACVC,aAAa,EAAE;;OAElB,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAC,uBAAuBA,CAACC,QAAgB,EAAEC,OAAyB;IACjE,MAAMP,OAAO,GAA0B;MACrCQ,SAAS,EAAEF,QAAQ;MACnBC,OAAO,EAAEA,OAAO;MAChBE,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE;KACrB;IAED,OAAO,IAAI,CAACX,qBAAqB,CAACC,OAAO,CAAC;EAC5C;EAEA;;;EAGAW,cAAcA,CAACL,QAAgB,EAAEM,KAAa,EAAEL,OAAyB;IACvE,MAAMP,OAAO,GAA0B;MACrCQ,SAAS,EAAEF,QAAQ;MACnBC,OAAO,EAAEA,OAAO;MAChBE,UAAU,EAAEG,KAAK;MACjBF,kBAAkB,EAAE;KACrB;IAED,OAAO,IAAI,CAACX,qBAAqB,CAACC,OAAO,CAAC;EAC5C;EAEA;;;EAGAa,cAAcA,CAACC,KAAa,EAAEnB,QAAA,GAAmB,GAAG;IAClD,IAAImB,KAAK,IAAI,QAAQ,EAAE;MAAE;MACvB,OAAO,GAAGnB,QAAQ,GAAG,CAACmB,KAAK,GAAG,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;KACvD,MAAM,IAAID,KAAK,IAAI,MAAM,EAAE;MAAE;MAC5B,OAAO,GAAGnB,QAAQ,GAAG,CAACmB,KAAK,GAAG,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;KACpD,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MAAE;MAC1B,OAAO,GAAGnB,QAAQ,GAAG,CAACmB,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;KAClD,MAAM;MACL,OAAO,GAAGpB,QAAQ,GAAGmB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE;;EAE3C;EAEA;;;EAGAC,YAAYA,CAACF,KAAa;IACxB,IAAIA,KAAK,IAAI,QAAQ,EAAE;MAAE;MACvB,OAAO,GAAG,CAACA,KAAK,GAAG,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;KAC5C,MAAM,IAAID,KAAK,IAAI,MAAM,EAAE;MAAE;MAC5B,OAAO,GAAG,CAACA,KAAK,GAAG,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;KACzC,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MAAE;MAC1B,OAAO,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;KACvC,MAAM;MACL,OAAOD,KAAK,CAACG,QAAQ,EAAE;;EAE3B;EAEA;;;EAGA5B,cAAcA,CAAA;IACZ,OAAO,CACL,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CAAE;IAAA,CACZ;EACH;EAEA;;;EAGA6B,gBAAgBA,CAACC,KAAY;IAC3B,MAAMC,MAAM,GAAG,IAAI,CAAC/B,cAAc,EAAE;IAEpC,QAAQ8B,KAAK,CAACE,IAAI;MAChB,KAAK,MAAM;QACT,OAAO;UACL,GAAGF,KAAK,CAAChC,IAAI;UACbmC,QAAQ,EAAEH,KAAK,CAAChC,IAAI,CAACmC,QAAQ,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,MAAM;YACrD,GAAGD,OAAO;YACVE,WAAW,EAAEN,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACO,MAAM,CAAC;YAC1CC,eAAe,EAAER,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACO,MAAM,CAAC,GAAG,IAAI;YACrDE,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE;WACV,CAAC;SACH;MAEH,KAAK,KAAK;QACR,OAAO;UACL,GAAGX,KAAK,CAAChC,IAAI;UACbmC,QAAQ,EAAEH,KAAK,CAAChC,IAAI,CAACmC,QAAQ,CAACC,GAAG,CAAEC,OAAO,KAAM;YAC9C,GAAGA,OAAO;YACVI,eAAe,EAAET,KAAK,CAAChC,IAAI,CAAC4C,MAAM,CAACR,GAAG,CAAC,CAACS,CAAC,EAAEC,CAAC,KAAKb,MAAM,CAACa,CAAC,GAAGb,MAAM,CAACO,MAAM,CAAC,CAAC;YAC3ED,WAAW,EAAEP,KAAK,CAAChC,IAAI,CAAC4C,MAAM,CAACR,GAAG,CAAC,CAACS,CAAC,EAAEC,CAAC,KAAKb,MAAM,CAACa,CAAC,GAAGb,MAAM,CAACO,MAAM,CAAC,CAAC;YACvEO,WAAW,EAAE;WACd,CAAC;SACH;MAEH,KAAK,UAAU;MACf,KAAK,KAAK;QACR,OAAO;UACL,GAAGf,KAAK,CAAChC,IAAI;UACbmC,QAAQ,EAAEH,KAAK,CAAChC,IAAI,CAACmC,QAAQ,CAACC,GAAG,CAACC,OAAO,KAAK;YAC5C,GAAGA,OAAO;YACVI,eAAe,EAAET,KAAK,CAAChC,IAAI,CAAC4C,MAAM,CAACR,GAAG,CAAC,CAACS,CAAC,EAAEC,CAAC,KAAKb,MAAM,CAACa,CAAC,GAAGb,MAAM,CAACO,MAAM,CAAC,CAAC;YAC3ED,WAAW,EAAEP,KAAK,CAAChC,IAAI,CAAC4C,MAAM,CAACR,GAAG,CAAC,CAACS,CAAC,EAAEC,CAAC,KAAKb,MAAM,CAACa,CAAC,GAAGb,MAAM,CAACO,MAAM,CAAC,CAAC;YACvEO,WAAW,EAAE;WACd,CAAC;SACH;MAEH;QACE,OAAOf,KAAK,CAAChC,IAAI;;EAEvB;EAEA;;;EAGAgD,kBAAkBA,CAACC,QAAgB,EAAEC,KAAa;IAChD,MAAMC,OAAO,GAA8B;MACzC,UAAU,EAAE,cAAc;MAC1B,QAAQ,EAAE,SAAS;MACnB,YAAY,EAAE,SAAS;MACvB,QAAQ,EAAE;KACX;IAED;IACA,IAAID,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzC,OAAO,eAAe;KACvB,MAAM,IAAIH,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIH,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC1F,OAAO,cAAc;KACtB,MAAM,IAAIH,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIH,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC7F,OAAO,OAAO;KACf,MAAM,IAAIH,KAAK,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;MAClD,OAAO,aAAa;;IAGtB,OAAOF,OAAO,CAACF,QAAQ,CAAC,IAAI,WAAW;EACzC;EAEA;;;EAGAK,iBAAiBA,CAACC,SAAe,EAAEC,OAAa;IAC9C,IAAI,CAACD,SAAS,IAAI,CAACC,OAAO,EAAE;MAC1B,OAAO,KAAK;;IAGd;IACA,IAAIA,OAAO,IAAID,SAAS,EAAE;MACxB,OAAO,KAAK;;IAGd;IACA,MAAME,OAAO,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IACzC,IAAID,OAAO,CAACE,OAAO,EAAE,GAAGH,SAAS,CAACG,OAAO,EAAE,GAAGD,OAAO,EAAE;MACrD,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA;;;EAGAE,mBAAmBA,CAAA;IACjB,MAAMH,OAAO,GAAG,IAAII,IAAI,EAAE;IAC1B,MAAML,SAAS,GAAG,IAAIK,IAAI,EAAE;IAC5BL,SAAS,CAACM,OAAO,CAACN,SAAS,CAACO,OAAO,EAAE,GAAG,EAAE,CAAC;IAE3C,OAAO;MAAEP,SAAS;MAAEC;IAAO,CAAE;EAC/B;;;uBAnOWpE,qBAAqB,EAAA2E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArB9E,qBAAqB;MAAA+E,OAAA,EAArB/E,qBAAqB,CAAAgF,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA;;SAEPjF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}