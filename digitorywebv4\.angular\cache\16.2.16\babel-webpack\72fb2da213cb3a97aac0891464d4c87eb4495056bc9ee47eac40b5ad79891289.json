{"ast": null, "code": "// Token class\n\n'use strict';\n\n/**\n * class Token\n **/\n\n/**\n * new Token(type, tag, nesting)\n *\n * Create new token and fill passed properties.\n **/\nfunction Token(type, tag, nesting) {\n  /**\n   * Token#type -> String\n   *\n   * Type of the token (string, e.g. \"paragraph_open\")\n   **/\n  this.type = type;\n\n  /**\n   * Token#tag -> String\n   *\n   * html tag name, e.g. \"p\"\n   **/\n  this.tag = tag;\n\n  /**\n   * Token#attrs -> Array\n   *\n   * Html attributes. Format: `[ [ name1, value1 ], [ name2, value2 ] ]`\n   **/\n  this.attrs = null;\n\n  /**\n   * Token#map -> Array\n   *\n   * Source map info. Format: `[ line_begin, line_end ]`\n   **/\n  this.map = null;\n\n  /**\n   * Token#nesting -> Number\n   *\n   * Level change (number in {-1, 0, 1} set), where:\n   *\n   * -  `1` means the tag is opening\n   * -  `0` means the tag is self-closing\n   * - `-1` means the tag is closing\n   **/\n  this.nesting = nesting;\n\n  /**\n   * Token#level -> Number\n   *\n   * nesting level, the same as `state.level`\n   **/\n  this.level = 0;\n\n  /**\n   * Token#children -> Array\n   *\n   * An array of child nodes (inline and img tokens)\n   **/\n  this.children = null;\n\n  /**\n   * Token#content -> String\n   *\n   * In a case of self-closing tag (code, html, fence, etc.),\n   * it has contents of this tag.\n   **/\n  this.content = '';\n\n  /**\n   * Token#markup -> String\n   *\n   * '*' or '_' for emphasis, fence string for fence, etc.\n   **/\n  this.markup = '';\n\n  /**\n   * Token#info -> String\n   *\n   * Additional information:\n   *\n   * - Info string for \"fence\" tokens\n   * - The value \"auto\" for autolink \"link_open\" and \"link_close\" tokens\n   * - The string value of the item marker for ordered-list \"list_item_open\" tokens\n   **/\n  this.info = '';\n\n  /**\n   * Token#meta -> Object\n   *\n   * A place for plugins to store an arbitrary data\n   **/\n  this.meta = null;\n\n  /**\n   * Token#block -> Boolean\n   *\n   * True for block-level tokens, false for inline tokens.\n   * Used in renderer to calculate line breaks\n   **/\n  this.block = false;\n\n  /**\n   * Token#hidden -> Boolean\n   *\n   * If it's true, ignore this element when rendering. Used for tight lists\n   * to hide paragraphs.\n   **/\n  this.hidden = false;\n}\n\n/**\n * Token.attrIndex(name) -> Number\n *\n * Search attribute index by name.\n **/\nToken.prototype.attrIndex = function attrIndex(name) {\n  var attrs, i, len;\n  if (!this.attrs) {\n    return -1;\n  }\n  attrs = this.attrs;\n  for (i = 0, len = attrs.length; i < len; i++) {\n    if (attrs[i][0] === name) {\n      return i;\n    }\n  }\n  return -1;\n};\n\n/**\n * Token.attrPush(attrData)\n *\n * Add `[ name, value ]` attribute to list. Init attrs if necessary\n **/\nToken.prototype.attrPush = function attrPush(attrData) {\n  if (this.attrs) {\n    this.attrs.push(attrData);\n  } else {\n    this.attrs = [attrData];\n  }\n};\n\n/**\n * Token.attrSet(name, value)\n *\n * Set `name` attribute to `value`. Override old value if exists.\n **/\nToken.prototype.attrSet = function attrSet(name, value) {\n  var idx = this.attrIndex(name),\n    attrData = [name, value];\n  if (idx < 0) {\n    this.attrPush(attrData);\n  } else {\n    this.attrs[idx] = attrData;\n  }\n};\n\n/**\n * Token.attrGet(name)\n *\n * Get the value of attribute `name`, or null if it does not exist.\n **/\nToken.prototype.attrGet = function attrGet(name) {\n  var idx = this.attrIndex(name),\n    value = null;\n  if (idx >= 0) {\n    value = this.attrs[idx][1];\n  }\n  return value;\n};\n\n/**\n * Token.attrJoin(name, value)\n *\n * Join value to existing attribute via space. Or create new attribute if not\n * exists. Useful to operate with token classes.\n **/\nToken.prototype.attrJoin = function attrJoin(name, value) {\n  var idx = this.attrIndex(name);\n  if (idx < 0) {\n    this.attrPush([name, value]);\n  } else {\n    this.attrs[idx][1] = this.attrs[idx][1] + ' ' + value;\n  }\n};\nmodule.exports = Token;", "map": {"version": 3, "names": ["Token", "type", "tag", "nesting", "attrs", "map", "level", "children", "content", "markup", "info", "meta", "block", "hidden", "prototype", "attrIndex", "name", "i", "len", "length", "attrPush", "attrData", "push", "attrSet", "value", "idx", "attrGet", "attr<PERSON><PERSON>n", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/token.js"], "sourcesContent": ["// Token class\n\n'use strict';\n\n\n/**\n * class Token\n **/\n\n/**\n * new Token(type, tag, nesting)\n *\n * Create new token and fill passed properties.\n **/\nfunction Token(type, tag, nesting) {\n  /**\n   * Token#type -> String\n   *\n   * Type of the token (string, e.g. \"paragraph_open\")\n   **/\n  this.type     = type;\n\n  /**\n   * Token#tag -> String\n   *\n   * html tag name, e.g. \"p\"\n   **/\n  this.tag      = tag;\n\n  /**\n   * Token#attrs -> Array\n   *\n   * Html attributes. Format: `[ [ name1, value1 ], [ name2, value2 ] ]`\n   **/\n  this.attrs    = null;\n\n  /**\n   * Token#map -> Array\n   *\n   * Source map info. Format: `[ line_begin, line_end ]`\n   **/\n  this.map      = null;\n\n  /**\n   * Token#nesting -> Number\n   *\n   * Level change (number in {-1, 0, 1} set), where:\n   *\n   * -  `1` means the tag is opening\n   * -  `0` means the tag is self-closing\n   * - `-1` means the tag is closing\n   **/\n  this.nesting  = nesting;\n\n  /**\n   * Token#level -> Number\n   *\n   * nesting level, the same as `state.level`\n   **/\n  this.level    = 0;\n\n  /**\n   * Token#children -> Array\n   *\n   * An array of child nodes (inline and img tokens)\n   **/\n  this.children = null;\n\n  /**\n   * Token#content -> String\n   *\n   * In a case of self-closing tag (code, html, fence, etc.),\n   * it has contents of this tag.\n   **/\n  this.content  = '';\n\n  /**\n   * Token#markup -> String\n   *\n   * '*' or '_' for emphasis, fence string for fence, etc.\n   **/\n  this.markup   = '';\n\n  /**\n   * Token#info -> String\n   *\n   * Additional information:\n   *\n   * - Info string for \"fence\" tokens\n   * - The value \"auto\" for autolink \"link_open\" and \"link_close\" tokens\n   * - The string value of the item marker for ordered-list \"list_item_open\" tokens\n   **/\n  this.info     = '';\n\n  /**\n   * Token#meta -> Object\n   *\n   * A place for plugins to store an arbitrary data\n   **/\n  this.meta     = null;\n\n  /**\n   * Token#block -> Boolean\n   *\n   * True for block-level tokens, false for inline tokens.\n   * Used in renderer to calculate line breaks\n   **/\n  this.block    = false;\n\n  /**\n   * Token#hidden -> Boolean\n   *\n   * If it's true, ignore this element when rendering. Used for tight lists\n   * to hide paragraphs.\n   **/\n  this.hidden   = false;\n}\n\n\n/**\n * Token.attrIndex(name) -> Number\n *\n * Search attribute index by name.\n **/\nToken.prototype.attrIndex = function attrIndex(name) {\n  var attrs, i, len;\n\n  if (!this.attrs) { return -1; }\n\n  attrs = this.attrs;\n\n  for (i = 0, len = attrs.length; i < len; i++) {\n    if (attrs[i][0] === name) { return i; }\n  }\n  return -1;\n};\n\n\n/**\n * Token.attrPush(attrData)\n *\n * Add `[ name, value ]` attribute to list. Init attrs if necessary\n **/\nToken.prototype.attrPush = function attrPush(attrData) {\n  if (this.attrs) {\n    this.attrs.push(attrData);\n  } else {\n    this.attrs = [ attrData ];\n  }\n};\n\n\n/**\n * Token.attrSet(name, value)\n *\n * Set `name` attribute to `value`. Override old value if exists.\n **/\nToken.prototype.attrSet = function attrSet(name, value) {\n  var idx = this.attrIndex(name),\n      attrData = [ name, value ];\n\n  if (idx < 0) {\n    this.attrPush(attrData);\n  } else {\n    this.attrs[idx] = attrData;\n  }\n};\n\n\n/**\n * Token.attrGet(name)\n *\n * Get the value of attribute `name`, or null if it does not exist.\n **/\nToken.prototype.attrGet = function attrGet(name) {\n  var idx = this.attrIndex(name), value = null;\n  if (idx >= 0) {\n    value = this.attrs[idx][1];\n  }\n  return value;\n};\n\n\n/**\n * Token.attrJoin(name, value)\n *\n * Join value to existing attribute via space. Or create new attribute if not\n * exists. Useful to operate with token classes.\n **/\nToken.prototype.attrJoin = function attrJoin(name, value) {\n  var idx = this.attrIndex(name);\n\n  if (idx < 0) {\n    this.attrPush([ name, value ]);\n  } else {\n    this.attrs[idx][1] = this.attrs[idx][1] + ' ' + value;\n  }\n};\n\n\nmodule.exports = Token;\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAKA,CAACC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAE;EACjC;AACF;AACA;AACA;AACA;EACE,IAAI,CAACF,IAAI,GAAOA,IAAI;;EAEpB;AACF;AACA;AACA;AACA;EACE,IAAI,CAACC,GAAG,GAAQA,GAAG;;EAEnB;AACF;AACA;AACA;AACA;EACE,IAAI,CAACE,KAAK,GAAM,IAAI;;EAEpB;AACF;AACA;AACA;AACA;EACE,IAAI,CAACC,GAAG,GAAQ,IAAI;;EAEpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAACF,OAAO,GAAIA,OAAO;;EAEvB;AACF;AACA;AACA;AACA;EACE,IAAI,CAACG,KAAK,GAAM,CAAC;;EAEjB;AACF;AACA;AACA;AACA;EACE,IAAI,CAACC,QAAQ,GAAG,IAAI;;EAEpB;AACF;AACA;AACA;AACA;AACA;EACE,IAAI,CAACC,OAAO,GAAI,EAAE;;EAElB;AACF;AACA;AACA;AACA;EACE,IAAI,CAACC,MAAM,GAAK,EAAE;;EAElB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAACC,IAAI,GAAO,EAAE;;EAElB;AACF;AACA;AACA;AACA;EACE,IAAI,CAACC,IAAI,GAAO,IAAI;;EAEpB;AACF;AACA;AACA;AACA;AACA;EACE,IAAI,CAACC,KAAK,GAAM,KAAK;;EAErB;AACF;AACA;AACA;AACA;AACA;EACE,IAAI,CAACC,MAAM,GAAK,KAAK;AACvB;;AAGA;AACA;AACA;AACA;AACA;AACAb,KAAK,CAACc,SAAS,CAACC,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAE;EACnD,IAAIZ,KAAK,EAAEa,CAAC,EAAEC,GAAG;EAEjB,IAAI,CAAC,IAAI,CAACd,KAAK,EAAE;IAAE,OAAO,CAAC,CAAC;EAAE;EAE9BA,KAAK,GAAG,IAAI,CAACA,KAAK;EAElB,KAAKa,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGd,KAAK,CAACe,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC5C,IAAIb,KAAK,CAACa,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKD,IAAI,EAAE;MAAE,OAAOC,CAAC;IAAE;EACxC;EACA,OAAO,CAAC,CAAC;AACX,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACAjB,KAAK,CAACc,SAAS,CAACM,QAAQ,GAAG,SAASA,QAAQA,CAACC,QAAQ,EAAE;EACrD,IAAI,IAAI,CAACjB,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,CAACkB,IAAI,CAACD,QAAQ,CAAC;EAC3B,CAAC,MAAM;IACL,IAAI,CAACjB,KAAK,GAAG,CAAEiB,QAAQ,CAAE;EAC3B;AACF,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACArB,KAAK,CAACc,SAAS,CAACS,OAAO,GAAG,SAASA,OAAOA,CAACP,IAAI,EAAEQ,KAAK,EAAE;EACtD,IAAIC,GAAG,GAAG,IAAI,CAACV,SAAS,CAACC,IAAI,CAAC;IAC1BK,QAAQ,GAAG,CAAEL,IAAI,EAAEQ,KAAK,CAAE;EAE9B,IAAIC,GAAG,GAAG,CAAC,EAAE;IACX,IAAI,CAACL,QAAQ,CAACC,QAAQ,CAAC;EACzB,CAAC,MAAM;IACL,IAAI,CAACjB,KAAK,CAACqB,GAAG,CAAC,GAAGJ,QAAQ;EAC5B;AACF,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACArB,KAAK,CAACc,SAAS,CAACY,OAAO,GAAG,SAASA,OAAOA,CAACV,IAAI,EAAE;EAC/C,IAAIS,GAAG,GAAG,IAAI,CAACV,SAAS,CAACC,IAAI,CAAC;IAAEQ,KAAK,GAAG,IAAI;EAC5C,IAAIC,GAAG,IAAI,CAAC,EAAE;IACZD,KAAK,GAAG,IAAI,CAACpB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5B;EACA,OAAOD,KAAK;AACd,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACAxB,KAAK,CAACc,SAAS,CAACa,QAAQ,GAAG,SAASA,QAAQA,CAACX,IAAI,EAAEQ,KAAK,EAAE;EACxD,IAAIC,GAAG,GAAG,IAAI,CAACV,SAAS,CAACC,IAAI,CAAC;EAE9B,IAAIS,GAAG,GAAG,CAAC,EAAE;IACX,IAAI,CAACL,QAAQ,CAAC,CAAEJ,IAAI,EAAEQ,KAAK,CAAE,CAAC;EAChC,CAAC,MAAM;IACL,IAAI,CAACpB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACrB,KAAK,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGD,KAAK;EACvD;AACF,CAAC;AAGDI,MAAM,CAACC,OAAO,GAAG7B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}