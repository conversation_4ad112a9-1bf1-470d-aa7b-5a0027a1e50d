{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nclass SessionCacheService {\n  constructor() {\n    this.inventoryItems = new BehaviorSubject({});\n  }\n  setInvItems(items) {\n    this.inventoryItems.next(items);\n  }\n  getInvItems() {\n    return this.inventoryItems;\n  }\n  static {\n    this.ɵfac = function SessionCacheService_Factory(t) {\n      return new (t || SessionCacheService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SessionCacheService,\n      factory: SessionCacheService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport { SessionCacheService };", "map": {"version": 3, "names": ["BehaviorSubject", "SessionCacheService", "constructor", "inventoryItems", "setInvItems", "items", "next", "getInvItems", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\services\\session-cache.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SessionCacheService {\n  private inventoryItems = new BehaviorSubject<any>({});\n\n  setInvItems(items) {\n    this.inventoryItems.next(items) ;\n  }\n\n  getInvItems() {\n    return this.inventoryItems\n  }\n\n\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAEtC,MAGaC,mBAAmB;EAHhCC,YAAA;IAIU,KAAAC,cAAc,GAAG,IAAIH,eAAe,CAAM,EAAE,CAAC;;EAErDI,WAAWA,CAACC,KAAK;IACf,IAAI,CAACF,cAAc,CAACG,IAAI,CAACD,KAAK,CAAC;EACjC;EAEAE,WAAWA,CAAA;IACT,OAAO,IAAI,CAACJ,cAAc;EAC5B;;;uBATWF,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAAO,OAAA,EAAnBP,mBAAmB,CAAAQ,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA;;SAEPT,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}