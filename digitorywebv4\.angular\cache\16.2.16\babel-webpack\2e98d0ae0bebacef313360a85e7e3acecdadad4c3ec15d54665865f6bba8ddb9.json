{"ast": null, "code": "// Replace link-like texts with link nodes.\n//\n// Currently restricted by `md.validateLink()` to http/https/ftp\n//\n'use strict';\n\nvar arrayReplaceAt = require('../common/utils').arrayReplaceAt;\nfunction isLinkOpen(str) {\n  return /^<a[>\\s]/i.test(str);\n}\nfunction isLinkClose(str) {\n  return /^<\\/a\\s*>/i.test(str);\n}\nmodule.exports = function linkify(state) {\n  var i,\n    j,\n    l,\n    tokens,\n    token,\n    currentToken,\n    nodes,\n    ln,\n    text,\n    pos,\n    lastPos,\n    level,\n    htmlLinkLevel,\n    url,\n    fullUrl,\n    urlText,\n    blockTokens = state.tokens,\n    links;\n  if (!state.md.options.linkify) {\n    return;\n  }\n  for (j = 0, l = blockTokens.length; j < l; j++) {\n    if (blockTokens[j].type !== 'inline' || !state.md.linkify.pretest(blockTokens[j].content)) {\n      continue;\n    }\n    tokens = blockTokens[j].children;\n    htmlLinkLevel = 0;\n\n    // We scan from the end, to keep position when new tags added.\n    // Use reversed logic in links start/end match\n    for (i = tokens.length - 1; i >= 0; i--) {\n      currentToken = tokens[i];\n\n      // Skip content of markdown links\n      if (currentToken.type === 'link_close') {\n        i--;\n        while (tokens[i].level !== currentToken.level && tokens[i].type !== 'link_open') {\n          i--;\n        }\n        continue;\n      }\n\n      // Skip content of html tag links\n      if (currentToken.type === 'html_inline') {\n        if (isLinkOpen(currentToken.content) && htmlLinkLevel > 0) {\n          htmlLinkLevel--;\n        }\n        if (isLinkClose(currentToken.content)) {\n          htmlLinkLevel++;\n        }\n      }\n      if (htmlLinkLevel > 0) {\n        continue;\n      }\n      if (currentToken.type === 'text' && state.md.linkify.test(currentToken.content)) {\n        text = currentToken.content;\n        links = state.md.linkify.match(text);\n\n        // Now split string to nodes\n        nodes = [];\n        level = currentToken.level;\n        lastPos = 0;\n\n        // forbid escape sequence at the start of the string,\n        // this avoids http\\://example.com/ from being linkified as\n        // http:<a href=\"//example.com/\">//example.com/</a>\n        if (links.length > 0 && links[0].index === 0 && i > 0 && tokens[i - 1].type === 'text_special') {\n          links = links.slice(1);\n        }\n        for (ln = 0; ln < links.length; ln++) {\n          url = links[ln].url;\n          fullUrl = state.md.normalizeLink(url);\n          if (!state.md.validateLink(fullUrl)) {\n            continue;\n          }\n          urlText = links[ln].text;\n\n          // Linkifier might send raw hostnames like \"example.com\", where url\n          // starts with domain name. So we prepend http:// in those cases,\n          // and remove it afterwards.\n          //\n          if (!links[ln].schema) {\n            urlText = state.md.normalizeLinkText('http://' + urlText).replace(/^http:\\/\\//, '');\n          } else if (links[ln].schema === 'mailto:' && !/^mailto:/i.test(urlText)) {\n            urlText = state.md.normalizeLinkText('mailto:' + urlText).replace(/^mailto:/, '');\n          } else {\n            urlText = state.md.normalizeLinkText(urlText);\n          }\n          pos = links[ln].index;\n          if (pos > lastPos) {\n            token = new state.Token('text', '', 0);\n            token.content = text.slice(lastPos, pos);\n            token.level = level;\n            nodes.push(token);\n          }\n          token = new state.Token('link_open', 'a', 1);\n          token.attrs = [['href', fullUrl]];\n          token.level = level++;\n          token.markup = 'linkify';\n          token.info = 'auto';\n          nodes.push(token);\n          token = new state.Token('text', '', 0);\n          token.content = urlText;\n          token.level = level;\n          nodes.push(token);\n          token = new state.Token('link_close', 'a', -1);\n          token.level = --level;\n          token.markup = 'linkify';\n          token.info = 'auto';\n          nodes.push(token);\n          lastPos = links[ln].lastIndex;\n        }\n        if (lastPos < text.length) {\n          token = new state.Token('text', '', 0);\n          token.content = text.slice(lastPos);\n          token.level = level;\n          nodes.push(token);\n        }\n\n        // replace current node\n        blockTokens[j].children = tokens = arrayReplaceAt(tokens, i, nodes);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["arrayReplaceAt", "require", "isLinkOpen", "str", "test", "isLinkClose", "module", "exports", "linkify", "state", "i", "j", "l", "tokens", "token", "currentToken", "nodes", "ln", "text", "pos", "lastPos", "level", "htmlLinkLevel", "url", "fullUrl", "urlText", "blockTokens", "links", "md", "options", "length", "type", "pretest", "content", "children", "match", "index", "slice", "normalizeLink", "validateLink", "schema", "normalizeLinkText", "replace", "Token", "push", "attrs", "markup", "info", "lastIndex"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_core/linkify.js"], "sourcesContent": ["// Replace link-like texts with link nodes.\n//\n// Currently restricted by `md.validateLink()` to http/https/ftp\n//\n'use strict';\n\n\nvar arrayReplaceAt = require('../common/utils').arrayReplaceAt;\n\n\nfunction isLinkOpen(str) {\n  return /^<a[>\\s]/i.test(str);\n}\nfunction isLinkClose(str) {\n  return /^<\\/a\\s*>/i.test(str);\n}\n\n\nmodule.exports = function linkify(state) {\n  var i, j, l, tokens, token, currentToken, nodes, ln, text, pos, lastPos,\n      level, htmlLinkLevel, url, fullUrl, urlText,\n      blockTokens = state.tokens,\n      links;\n\n  if (!state.md.options.linkify) { return; }\n\n  for (j = 0, l = blockTokens.length; j < l; j++) {\n    if (blockTokens[j].type !== 'inline' ||\n        !state.md.linkify.pretest(blockTokens[j].content)) {\n      continue;\n    }\n\n    tokens = blockTokens[j].children;\n\n    htmlLinkLevel = 0;\n\n    // We scan from the end, to keep position when new tags added.\n    // Use reversed logic in links start/end match\n    for (i = tokens.length - 1; i >= 0; i--) {\n      currentToken = tokens[i];\n\n      // Skip content of markdown links\n      if (currentToken.type === 'link_close') {\n        i--;\n        while (tokens[i].level !== currentToken.level && tokens[i].type !== 'link_open') {\n          i--;\n        }\n        continue;\n      }\n\n      // Skip content of html tag links\n      if (currentToken.type === 'html_inline') {\n        if (isLinkOpen(currentToken.content) && htmlLinkLevel > 0) {\n          htmlLinkLevel--;\n        }\n        if (isLinkClose(currentToken.content)) {\n          htmlLinkLevel++;\n        }\n      }\n      if (htmlLinkLevel > 0) { continue; }\n\n      if (currentToken.type === 'text' && state.md.linkify.test(currentToken.content)) {\n\n        text = currentToken.content;\n        links = state.md.linkify.match(text);\n\n        // Now split string to nodes\n        nodes = [];\n        level = currentToken.level;\n        lastPos = 0;\n\n        // forbid escape sequence at the start of the string,\n        // this avoids http\\://example.com/ from being linkified as\n        // http:<a href=\"//example.com/\">//example.com/</a>\n        if (links.length > 0 &&\n            links[0].index === 0 &&\n            i > 0 &&\n            tokens[i - 1].type === 'text_special') {\n          links = links.slice(1);\n        }\n\n        for (ln = 0; ln < links.length; ln++) {\n          url = links[ln].url;\n          fullUrl = state.md.normalizeLink(url);\n          if (!state.md.validateLink(fullUrl)) { continue; }\n\n          urlText = links[ln].text;\n\n          // Linkifier might send raw hostnames like \"example.com\", where url\n          // starts with domain name. So we prepend http:// in those cases,\n          // and remove it afterwards.\n          //\n          if (!links[ln].schema) {\n            urlText = state.md.normalizeLinkText('http://' + urlText).replace(/^http:\\/\\//, '');\n          } else if (links[ln].schema === 'mailto:' && !/^mailto:/i.test(urlText)) {\n            urlText = state.md.normalizeLinkText('mailto:' + urlText).replace(/^mailto:/, '');\n          } else {\n            urlText = state.md.normalizeLinkText(urlText);\n          }\n\n          pos = links[ln].index;\n\n          if (pos > lastPos) {\n            token         = new state.Token('text', '', 0);\n            token.content = text.slice(lastPos, pos);\n            token.level   = level;\n            nodes.push(token);\n          }\n\n          token         = new state.Token('link_open', 'a', 1);\n          token.attrs   = [ [ 'href', fullUrl ] ];\n          token.level   = level++;\n          token.markup  = 'linkify';\n          token.info    = 'auto';\n          nodes.push(token);\n\n          token         = new state.Token('text', '', 0);\n          token.content = urlText;\n          token.level   = level;\n          nodes.push(token);\n\n          token         = new state.Token('link_close', 'a', -1);\n          token.level   = --level;\n          token.markup  = 'linkify';\n          token.info    = 'auto';\n          nodes.push(token);\n\n          lastPos = links[ln].lastIndex;\n        }\n        if (lastPos < text.length) {\n          token         = new state.Token('text', '', 0);\n          token.content = text.slice(lastPos);\n          token.level   = level;\n          nodes.push(token);\n        }\n\n        // replace current node\n        blockTokens[j].children = tokens = arrayReplaceAt(tokens, i, nodes);\n      }\n    }\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,YAAY;;AAGZ,IAAIA,cAAc,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACD,cAAc;AAG9D,SAASE,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAO,WAAW,CAACC,IAAI,CAACD,GAAG,CAAC;AAC9B;AACA,SAASE,WAAWA,CAACF,GAAG,EAAE;EACxB,OAAO,YAAY,CAACC,IAAI,CAACD,GAAG,CAAC;AAC/B;AAGAG,MAAM,CAACC,OAAO,GAAG,SAASC,OAAOA,CAACC,KAAK,EAAE;EACvC,IAAIC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC,MAAM;IAAEC,KAAK;IAAEC,YAAY;IAAEC,KAAK;IAAEC,EAAE;IAAEC,IAAI;IAAEC,GAAG;IAAEC,OAAO;IACnEC,KAAK;IAAEC,aAAa;IAAEC,GAAG;IAAEC,OAAO;IAAEC,OAAO;IAC3CC,WAAW,GAAGjB,KAAK,CAACI,MAAM;IAC1Bc,KAAK;EAET,IAAI,CAAClB,KAAK,CAACmB,EAAE,CAACC,OAAO,CAACrB,OAAO,EAAE;IAAE;EAAQ;EAEzC,KAAKG,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGc,WAAW,CAACI,MAAM,EAAEnB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAIe,WAAW,CAACf,CAAC,CAAC,CAACoB,IAAI,KAAK,QAAQ,IAChC,CAACtB,KAAK,CAACmB,EAAE,CAACpB,OAAO,CAACwB,OAAO,CAACN,WAAW,CAACf,CAAC,CAAC,CAACsB,OAAO,CAAC,EAAE;MACrD;IACF;IAEApB,MAAM,GAAGa,WAAW,CAACf,CAAC,CAAC,CAACuB,QAAQ;IAEhCZ,aAAa,GAAG,CAAC;;IAEjB;IACA;IACA,KAAKZ,CAAC,GAAGG,MAAM,CAACiB,MAAM,GAAG,CAAC,EAAEpB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvCK,YAAY,GAAGF,MAAM,CAACH,CAAC,CAAC;;MAExB;MACA,IAAIK,YAAY,CAACgB,IAAI,KAAK,YAAY,EAAE;QACtCrB,CAAC,EAAE;QACH,OAAOG,MAAM,CAACH,CAAC,CAAC,CAACW,KAAK,KAAKN,YAAY,CAACM,KAAK,IAAIR,MAAM,CAACH,CAAC,CAAC,CAACqB,IAAI,KAAK,WAAW,EAAE;UAC/ErB,CAAC,EAAE;QACL;QACA;MACF;;MAEA;MACA,IAAIK,YAAY,CAACgB,IAAI,KAAK,aAAa,EAAE;QACvC,IAAI7B,UAAU,CAACa,YAAY,CAACkB,OAAO,CAAC,IAAIX,aAAa,GAAG,CAAC,EAAE;UACzDA,aAAa,EAAE;QACjB;QACA,IAAIjB,WAAW,CAACU,YAAY,CAACkB,OAAO,CAAC,EAAE;UACrCX,aAAa,EAAE;QACjB;MACF;MACA,IAAIA,aAAa,GAAG,CAAC,EAAE;QAAE;MAAU;MAEnC,IAAIP,YAAY,CAACgB,IAAI,KAAK,MAAM,IAAItB,KAAK,CAACmB,EAAE,CAACpB,OAAO,CAACJ,IAAI,CAACW,YAAY,CAACkB,OAAO,CAAC,EAAE;QAE/Ef,IAAI,GAAGH,YAAY,CAACkB,OAAO;QAC3BN,KAAK,GAAGlB,KAAK,CAACmB,EAAE,CAACpB,OAAO,CAAC2B,KAAK,CAACjB,IAAI,CAAC;;QAEpC;QACAF,KAAK,GAAG,EAAE;QACVK,KAAK,GAAGN,YAAY,CAACM,KAAK;QAC1BD,OAAO,GAAG,CAAC;;QAEX;QACA;QACA;QACA,IAAIO,KAAK,CAACG,MAAM,GAAG,CAAC,IAChBH,KAAK,CAAC,CAAC,CAAC,CAACS,KAAK,KAAK,CAAC,IACpB1B,CAAC,GAAG,CAAC,IACLG,MAAM,CAACH,CAAC,GAAG,CAAC,CAAC,CAACqB,IAAI,KAAK,cAAc,EAAE;UACzCJ,KAAK,GAAGA,KAAK,CAACU,KAAK,CAAC,CAAC,CAAC;QACxB;QAEA,KAAKpB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGU,KAAK,CAACG,MAAM,EAAEb,EAAE,EAAE,EAAE;UACpCM,GAAG,GAAGI,KAAK,CAACV,EAAE,CAAC,CAACM,GAAG;UACnBC,OAAO,GAAGf,KAAK,CAACmB,EAAE,CAACU,aAAa,CAACf,GAAG,CAAC;UACrC,IAAI,CAACd,KAAK,CAACmB,EAAE,CAACW,YAAY,CAACf,OAAO,CAAC,EAAE;YAAE;UAAU;UAEjDC,OAAO,GAAGE,KAAK,CAACV,EAAE,CAAC,CAACC,IAAI;;UAExB;UACA;UACA;UACA;UACA,IAAI,CAACS,KAAK,CAACV,EAAE,CAAC,CAACuB,MAAM,EAAE;YACrBf,OAAO,GAAGhB,KAAK,CAACmB,EAAE,CAACa,iBAAiB,CAAC,SAAS,GAAGhB,OAAO,CAAC,CAACiB,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;UACrF,CAAC,MAAM,IAAIf,KAAK,CAACV,EAAE,CAAC,CAACuB,MAAM,KAAK,SAAS,IAAI,CAAC,WAAW,CAACpC,IAAI,CAACqB,OAAO,CAAC,EAAE;YACvEA,OAAO,GAAGhB,KAAK,CAACmB,EAAE,CAACa,iBAAiB,CAAC,SAAS,GAAGhB,OAAO,CAAC,CAACiB,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;UACnF,CAAC,MAAM;YACLjB,OAAO,GAAGhB,KAAK,CAACmB,EAAE,CAACa,iBAAiB,CAAChB,OAAO,CAAC;UAC/C;UAEAN,GAAG,GAAGQ,KAAK,CAACV,EAAE,CAAC,CAACmB,KAAK;UAErB,IAAIjB,GAAG,GAAGC,OAAO,EAAE;YACjBN,KAAK,GAAW,IAAIL,KAAK,CAACkC,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9C7B,KAAK,CAACmB,OAAO,GAAGf,IAAI,CAACmB,KAAK,CAACjB,OAAO,EAAED,GAAG,CAAC;YACxCL,KAAK,CAACO,KAAK,GAAKA,KAAK;YACrBL,KAAK,CAAC4B,IAAI,CAAC9B,KAAK,CAAC;UACnB;UAEAA,KAAK,GAAW,IAAIL,KAAK,CAACkC,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;UACpD7B,KAAK,CAAC+B,KAAK,GAAK,CAAE,CAAE,MAAM,EAAErB,OAAO,CAAE,CAAE;UACvCV,KAAK,CAACO,KAAK,GAAKA,KAAK,EAAE;UACvBP,KAAK,CAACgC,MAAM,GAAI,SAAS;UACzBhC,KAAK,CAACiC,IAAI,GAAM,MAAM;UACtB/B,KAAK,CAAC4B,IAAI,CAAC9B,KAAK,CAAC;UAEjBA,KAAK,GAAW,IAAIL,KAAK,CAACkC,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;UAC9C7B,KAAK,CAACmB,OAAO,GAAGR,OAAO;UACvBX,KAAK,CAACO,KAAK,GAAKA,KAAK;UACrBL,KAAK,CAAC4B,IAAI,CAAC9B,KAAK,CAAC;UAEjBA,KAAK,GAAW,IAAIL,KAAK,CAACkC,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;UACtD7B,KAAK,CAACO,KAAK,GAAK,EAAEA,KAAK;UACvBP,KAAK,CAACgC,MAAM,GAAI,SAAS;UACzBhC,KAAK,CAACiC,IAAI,GAAM,MAAM;UACtB/B,KAAK,CAAC4B,IAAI,CAAC9B,KAAK,CAAC;UAEjBM,OAAO,GAAGO,KAAK,CAACV,EAAE,CAAC,CAAC+B,SAAS;QAC/B;QACA,IAAI5B,OAAO,GAAGF,IAAI,CAACY,MAAM,EAAE;UACzBhB,KAAK,GAAW,IAAIL,KAAK,CAACkC,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;UAC9C7B,KAAK,CAACmB,OAAO,GAAGf,IAAI,CAACmB,KAAK,CAACjB,OAAO,CAAC;UACnCN,KAAK,CAACO,KAAK,GAAKA,KAAK;UACrBL,KAAK,CAAC4B,IAAI,CAAC9B,KAAK,CAAC;QACnB;;QAEA;QACAY,WAAW,CAACf,CAAC,CAAC,CAACuB,QAAQ,GAAGrB,MAAM,GAAGb,cAAc,CAACa,MAAM,EAAEH,CAAC,EAAEM,KAAK,CAAC;MACrE;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}