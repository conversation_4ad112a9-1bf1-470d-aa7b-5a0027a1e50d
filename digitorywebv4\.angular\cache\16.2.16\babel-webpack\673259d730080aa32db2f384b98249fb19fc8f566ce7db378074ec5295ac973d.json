{"ast": null, "code": "// Commonmark default options\n\n'use strict';\n\nmodule.exports = {\n  options: {\n    html: true,\n    // Enable HTML tags in source\n    xhtmlOut: true,\n    // Use '/' to close single tags (<br />)\n    breaks: false,\n    // Convert '\\n' in paragraphs into <br>\n    langPrefix: 'language-',\n    // CSS language prefix for fenced blocks\n    linkify: false,\n    // autoconvert URL-like texts to links\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer: false,\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019',\n    /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n    maxNesting: 20 // Internal protection, recursion limit\n  },\n\n  components: {\n    core: {\n      rules: ['normalize', 'block', 'inline', 'text_join']\n    },\n    block: {\n      rules: ['blockquote', 'code', 'fence', 'heading', 'hr', 'html_block', 'lheading', 'list', 'reference', 'paragraph']\n    },\n    inline: {\n      rules: ['autolink', 'backticks', 'emphasis', 'entity', 'escape', 'html_inline', 'image', 'link', 'newline', 'text'],\n      rules2: ['balance_pairs', 'emphasis', 'fragments_join']\n    }\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "options", "html", "xhtmlOut", "breaks", "langPrefix", "linkify", "typographer", "quotes", "highlight", "maxNesting", "components", "core", "rules", "block", "inline", "rules2"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/presets/commonmark.js"], "sourcesContent": ["// Commonmark default options\n\n'use strict';\n\n\nmodule.exports = {\n  options: {\n    html:         true,         // Enable HTML tags in source\n    xhtmlOut:     true,         // Use '/' to close single tags (<br />)\n    breaks:       false,        // Convert '\\n' in paragraphs into <br>\n    langPrefix:   'language-',  // CSS language prefix for fenced blocks\n    linkify:      false,        // autoconvert URL-like texts to links\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer:  false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    maxNesting:   20            // Internal protection, recursion limit\n  },\n\n  components: {\n\n    core: {\n      rules: [\n        'normalize',\n        'block',\n        'inline',\n        'text_join'\n      ]\n    },\n\n    block: {\n      rules: [\n        'blockquote',\n        'code',\n        'fence',\n        'heading',\n        'hr',\n        'html_block',\n        'lheading',\n        'list',\n        'reference',\n        'paragraph'\n      ]\n    },\n\n    inline: {\n      rules: [\n        'autolink',\n        'backticks',\n        'emphasis',\n        'entity',\n        'escape',\n        'html_inline',\n        'image',\n        'link',\n        'newline',\n        'text'\n      ],\n      rules2: [\n        'balance_pairs',\n        'emphasis',\n        'fragments_join'\n      ]\n    }\n  }\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG;EACfC,OAAO,EAAE;IACPC,IAAI,EAAU,IAAI;IAAU;IAC5BC,QAAQ,EAAM,IAAI;IAAU;IAC5BC,MAAM,EAAQ,KAAK;IAAS;IAC5BC,UAAU,EAAI,WAAW;IAAG;IAC5BC,OAAO,EAAO,KAAK;IAAS;;IAE5B;IACAC,WAAW,EAAG,KAAK;IAEnB;IACA;IACA;IACA;IACA;IACAC,MAAM,EAAE,0BAA0B;IAAE;;IAEpC;IACA;IACA;IACA;IACA;IACA;IACAC,SAAS,EAAE,IAAI;IAEfC,UAAU,EAAI,EAAE,CAAY;EAC9B,CAAC;;EAEDC,UAAU,EAAE;IAEVC,IAAI,EAAE;MACJC,KAAK,EAAE,CACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,WAAW;IAEf,CAAC;IAEDC,KAAK,EAAE;MACLD,KAAK,EAAE,CACL,YAAY,EACZ,MAAM,EACN,OAAO,EACP,SAAS,EACT,IAAI,EACJ,YAAY,EACZ,UAAU,EACV,MAAM,EACN,WAAW,EACX,WAAW;IAEf,CAAC;IAEDE,MAAM,EAAE;MACNF,KAAK,EAAE,CACL,UAAU,EACV,WAAW,EACX,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,OAAO,EACP,MAAM,EACN,SAAS,EACT,MAAM,CACP;MACDG,MAAM,EAAE,CACN,eAAe,EACf,UAAU,EACV,gBAAgB;IAEpB;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}