{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport MarkdownIt from 'markdown-it';\nimport { first } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"src/app/services/inventory.service\";\nimport * as i3 from \"@angular/common\";\nfunction RecipeLlmComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 5);\n    i0.ɵɵelement(4, \"path\", 6)(5, \"path\", 7);\n    i0.ɵɵelementStart(6, \"circle\", 8);\n    i0.ɵɵelement(7, \"animate\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"p\", 10);\n    i0.ɵɵtext(9, \"Loading insights...\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction RecipeLlmComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.renderedMDHtml, i0.ɵɵsanitizeHtml);\n  }\n}\nclass RecipeLlmComponent {\n  constructor(sanitizer, api, cd) {\n    this.sanitizer = sanitizer;\n    this.api = api;\n    this.cd = cd;\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    let mapping = {\n      \"menu_master\": this.master,\n      \"menu_recipes\": this.child\n    };\n    this.api.recipeInsight(mapping).pipe(first()).subscribe({\n      next: res => {\n        this.md = new MarkdownIt();\n        this.md.set({\n          typographer: true,\n          linkify: true,\n          xhtmlOut: true,\n          html: false\n        }).enable(['smartquotes', 'replacements', 'image']);\n        const renderedMarkdown = this.md.render(res['data']);\n        this.renderedMDHtml = this.sanitizer.bypassSecurityTrustHtml(renderedMarkdown);\n        this.isLoading = false;\n        this.cd.detectChanges();\n      },\n      error: err => {\n        console.log(err);\n        this.isLoading = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function RecipeLlmComponent_Factory(t) {\n      return new (t || RecipeLlmComponent)(i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i2.InventoryService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RecipeLlmComponent,\n      selectors: [[\"app-recipe-llm\"]],\n      inputs: {\n        master: \"master\",\n        child: \"child\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"recipe-insights-container\", 4, \"ngIf\"], [\"class\", \"markdown-content\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"recipe-insights-container\"], [1, \"loading-container\"], [1, \"loading-content\"], [\"viewBox\", \"0 0 100 100\", \"width\", \"100\", \"height\", \"100\", 1, \"cooking-pot\"], [\"d\", \"M20,50 Q50,20 80,50 L80,80 Q50,100 20,80 Z\", \"fill\", \"#d4d4d4\"], [\"d\", \"M35,40 L65,40 L65,50 Q50,60 35,50 Z\", \"fill\", \"#ffffff\"], [\"cx\", \"50\", \"cy\", \"60\", \"r\", \"5\", \"fill\", \"#ff6b6b\"], [\"attributeName\", \"cy\", \"values\", \"60;55;60\", \"dur\", \"1s\", \"repeatCount\", \"indefinite\"], [1, \"mt-2\"], [1, \"markdown-content\", 3, \"innerHTML\"]],\n      template: function RecipeLlmComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, RecipeLlmComponent_div_0_Template, 10, 0, \"div\", 0);\n          i0.ɵɵtemplate(1, RecipeLlmComponent_div_1_Template, 1, 1, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf],\n      styles: [\".markdown-content[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  line-height: 1.2;\\n}\\n\\n.recipe-insights-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 40vh;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background-color: rgba(255, 255, 255, 0.9);\\n}\\n\\n.loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.cooking-pot[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  animation: _ngcontent-%COMP%_steam 2s ease-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_steam {\\n  0% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: translateY(-10px) scale(1.2);\\n    opacity: 0.7;\\n  }\\n  100% {\\n    transform: translateY(0) scale(1);\\n    opacity: 0.5;\\n  }\\n}\\n.content-container[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.list-group-item[_ngcontent-%COMP%] {\\n  transition: background-color 0.3s;\\n}\\n\\n.list-group-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}\nexport { RecipeLlmComponent };", "map": {"version": 3, "names": ["CommonModule", "MarkdownIt", "first", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵproperty", "ctx_r1", "renderedMDHtml", "ɵɵsanitizeHtml", "RecipeLlmComponent", "constructor", "sanitizer", "api", "cd", "isLoading", "ngOnInit", "mapping", "master", "child", "recipeInsight", "pipe", "subscribe", "next", "res", "md", "set", "typographer", "linkify", "xhtmlOut", "html", "enable", "renderedMarkdown", "render", "bypassSecurityTrustHtml", "detectChanges", "error", "err", "console", "log", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "i2", "InventoryService", "ChangeDetectorRef", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RecipeLlmComponent_Template", "rf", "ctx", "ɵɵtemplate", "RecipeLlmComponent_div_0_Template", "RecipeLlmComponent_div_1_Template", "ɵɵadvance", "i3", "NgIf", "styles", "changeDetection"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\recipe-management\\recipe-llm\\recipe-llm.component.ts", "C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\recipe-management\\recipe-llm\\recipe-llm.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport MarkdownIt from 'markdown-it';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { InventoryService } from 'src/app/services/inventory.service';\nimport { first } from 'rxjs';\n\n@Component({\n  selector: 'app-recipe-llm',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './recipe-llm.component.html',\n  styleUrls: ['./recipe-llm.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class RecipeLlmComponent implements OnInit {\n  public md: MarkdownIt;\n  @Input() master: any;\n  @Input() child: any;\n\n  public renderedMDHtml: SafeHtml;\n  public isLoading: boolean = true;\n\n  constructor(\n    private sanitizer: DomSanitizer,\n    private api: InventoryService,\n    private cd: ChangeDetectorRef,\n  ) { }\n\n  public ngOnInit(): void {\n    let mapping = {\n      \"menu_master\": this.master,\n      \"menu_recipes\": this.child,\n    }\n    this.api.recipeInsight(mapping).pipe(first()).subscribe({\n      next: (res) => {\n        this.md = new MarkdownIt();\n        this.md\n        .set({\n          typographer: true,\n          linkify: true,\n          xhtmlOut: true,\n          html: false,\n        })\n        .enable(['smartquotes', 'replacements', 'image']);\n        const renderedMarkdown = this.md.render(res['data']);\n        this.renderedMDHtml = this.sanitizer.bypassSecurityTrustHtml(renderedMarkdown);\n        this.isLoading = false;\n        this.cd.detectChanges();\n      },\n      error: (err) => { \n        console.log(err);\n        this.isLoading = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n}", "<div class=\"recipe-insights-container\" *ngIf=\"isLoading\">\n    <div class=\"loading-container\">\n        <div class=\"loading-content\">\n            <svg class=\"cooking-pot\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\n                <path d=\"M20,50 Q50,20 80,50 L80,80 Q50,100 20,80 Z\" fill=\"#d4d4d4\" />\n                <path d=\"M35,40 L65,40 L65,50 Q50,60 35,50 Z\" fill=\"#ffffff\" />\n                <circle cx=\"50\" cy=\"60\" r=\"5\" fill=\"#ff6b6b\">\n                    <animate attributeName=\"cy\" values=\"60;55;60\" dur=\"1s\" repeatCount=\"indefinite\" />\n                </circle>\n            </svg>\n            <p class=\"mt-2\">Loading insights...</p>\n        </div>\n    </div>\n</div>\n\n\n<div *ngIf=\"!isLoading\" [innerHTML]=\"renderedMDHtml\" class=\"markdown-content\"></div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,UAAU,MAAM,aAAa;AAGpC,SAASC,KAAK,QAAQ,MAAM;;;;;;;ICL5BC,EAAA,CAAAC,cAAA,aAAyD;IAG7CD,EAAA,CAAAE,cAAA,EAAwE;IAAxEF,EAAA,CAAAC,cAAA,aAAwE;IACpED,EAAA,CAAAG,SAAA,cAAsE;IAEtEH,EAAA,CAAAC,cAAA,gBAA6C;IACzCD,EAAA,CAAAG,SAAA,iBAAkF;IACtFH,EAAA,CAAAI,YAAA,EAAS;IAEbJ,EAAA,CAAAK,eAAA,EAAgB;IAAhBL,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAM,MAAA,0BAAmB;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;IAMnDJ,EAAA,CAAAG,SAAA,cAAoF;;;;IAA5DH,EAAA,CAAAO,UAAA,cAAAC,MAAA,CAAAC,cAAA,EAAAT,EAAA,CAAAU,cAAA,CAA4B;;;ADTpD,MAQaC,kBAAkB;EAQ7BC,YACUC,SAAuB,EACvBC,GAAqB,EACrBC,EAAqB;IAFrB,KAAAF,SAAS,GAATA,SAAS;IACT,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,EAAE,GAAFA,EAAE;IALL,KAAAC,SAAS,GAAY,IAAI;EAM5B;EAEGC,QAAQA,CAAA;IACb,IAAIC,OAAO,GAAG;MACZ,aAAa,EAAE,IAAI,CAACC,MAAM;MAC1B,cAAc,EAAE,IAAI,CAACC;KACtB;IACD,IAAI,CAACN,GAAG,CAACO,aAAa,CAACH,OAAO,CAAC,CAACI,IAAI,CAACvB,KAAK,EAAE,CAAC,CAACwB,SAAS,CAAC;MACtDC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACC,EAAE,GAAG,IAAI5B,UAAU,EAAE;QAC1B,IAAI,CAAC4B,EAAE,CACNC,GAAG,CAAC;UACHC,WAAW,EAAE,IAAI;UACjBC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE,IAAI;UACdC,IAAI,EAAE;SACP,CAAC,CACDC,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QACjD,MAAMC,gBAAgB,GAAG,IAAI,CAACP,EAAE,CAACQ,MAAM,CAACT,GAAG,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,CAAChB,cAAc,GAAG,IAAI,CAACI,SAAS,CAACsB,uBAAuB,CAACF,gBAAgB,CAAC;QAC9E,IAAI,CAACjB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACD,EAAE,CAACqB,aAAa,EAAE;MACzB,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;QAChB,IAAI,CAACtB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACD,EAAE,CAACqB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;;;uBAzCWzB,kBAAkB,EAAAX,EAAA,CAAAyC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA3C,EAAA,CAAAyC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA7C,EAAA,CAAAyC,iBAAA,CAAAzC,EAAA,CAAA8C,iBAAA;IAAA;EAAA;;;YAAlBnC,kBAAkB;MAAAoC,SAAA;MAAAC,MAAA;QAAA7B,MAAA;QAAAC,KAAA;MAAA;MAAA6B,UAAA;MAAAC,QAAA,GAAAlD,EAAA,CAAAmD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf/BzD,EAAA,CAAA2D,UAAA,IAAAC,iCAAA,kBAaM;UAGN5D,EAAA,CAAA2D,UAAA,IAAAE,iCAAA,iBAAoF;;;UAhB5C7D,EAAA,CAAAO,UAAA,SAAAmD,GAAA,CAAA1C,SAAA,CAAe;UAgBjDhB,EAAA,CAAA8D,SAAA,GAAgB;UAAhB9D,EAAA,CAAAO,UAAA,UAAAmD,GAAA,CAAA1C,SAAA,CAAgB;;;qBDNVnB,YAAY,EAAAkE,EAAA,CAAAC,IAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;EAAA;;SAKXvD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}