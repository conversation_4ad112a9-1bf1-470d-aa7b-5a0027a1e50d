{"ast": null, "code": "// Process escaped chars and hardbreaks\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\nvar ESCAPED = [];\nfor (var i = 0; i < 256; i++) {\n  ESCAPED.push(0);\n}\n'\\\\!\"#$%&\\'()*+,./:;<=>?@[]^_`{|}~-'.split('').forEach(function (ch) {\n  ESCAPED[ch.charCodeAt(0)] = 1;\n});\nmodule.exports = function escape(state, silent) {\n  var ch1,\n    ch2,\n    origStr,\n    escapedStr,\n    token,\n    pos = state.pos,\n    max = state.posMax;\n  if (state.src.charCodeAt(pos) !== 0x5C /* \\ */) return false;\n  pos++;\n\n  // '\\' at the end of the inline block\n  if (pos >= max) return false;\n  ch1 = state.src.charCodeAt(pos);\n  if (ch1 === 0x0A) {\n    if (!silent) {\n      state.push('hardbreak', 'br', 0);\n    }\n    pos++;\n    // skip leading whitespaces from next line\n    while (pos < max) {\n      ch1 = state.src.charCodeAt(pos);\n      if (!isSpace(ch1)) break;\n      pos++;\n    }\n    state.pos = pos;\n    return true;\n  }\n  escapedStr = state.src[pos];\n  if (ch1 >= 0xD800 && ch1 <= 0xDBFF && pos + 1 < max) {\n    ch2 = state.src.charCodeAt(pos + 1);\n    if (ch2 >= 0xDC00 && ch2 <= 0xDFFF) {\n      escapedStr += state.src[pos + 1];\n      pos++;\n    }\n  }\n  origStr = '\\\\' + escapedStr;\n  if (!silent) {\n    token = state.push('text_special', '', 0);\n    if (ch1 < 256 && ESCAPED[ch1] !== 0) {\n      token.content = escapedStr;\n    } else {\n      token.content = origStr;\n    }\n    token.markup = origStr;\n    token.info = 'escape';\n  }\n  state.pos = pos + 1;\n  return true;\n};", "map": {"version": 3, "names": ["isSpace", "require", "ESCAPED", "i", "push", "split", "for<PERSON>ach", "ch", "charCodeAt", "module", "exports", "escape", "state", "silent", "ch1", "ch2", "origStr", "escapedStr", "token", "pos", "max", "posMax", "src", "content", "markup", "info"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_inline/escape.js"], "sourcesContent": ["// Process escaped chars and hardbreaks\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\nvar ESCAPED = [];\n\nfor (var i = 0; i < 256; i++) { ESCAPED.push(0); }\n\n'\\\\!\"#$%&\\'()*+,./:;<=>?@[]^_`{|}~-'\n  .split('').forEach(function (ch) { ESCAPED[ch.charCodeAt(0)] = 1; });\n\n\nmodule.exports = function escape(state, silent) {\n  var ch1, ch2, origStr, escapedStr, token, pos = state.pos, max = state.posMax;\n\n  if (state.src.charCodeAt(pos) !== 0x5C/* \\ */) return false;\n  pos++;\n\n  // '\\' at the end of the inline block\n  if (pos >= max) return false;\n\n  ch1 = state.src.charCodeAt(pos);\n\n  if (ch1 === 0x0A) {\n    if (!silent) {\n      state.push('hardbreak', 'br', 0);\n    }\n\n    pos++;\n    // skip leading whitespaces from next line\n    while (pos < max) {\n      ch1 = state.src.charCodeAt(pos);\n      if (!isSpace(ch1)) break;\n      pos++;\n    }\n\n    state.pos = pos;\n    return true;\n  }\n\n  escapedStr = state.src[pos];\n\n  if (ch1 >= 0xD800 && ch1 <= 0xDBFF && pos + 1 < max) {\n    ch2 = state.src.charCodeAt(pos + 1);\n\n    if (ch2 >= 0xDC00 && ch2 <= 0xDFFF) {\n      escapedStr += state.src[pos + 1];\n      pos++;\n    }\n  }\n\n  origStr = '\\\\' + escapedStr;\n\n  if (!silent) {\n    token = state.push('text_special', '', 0);\n\n    if (ch1 < 256 && ESCAPED[ch1] !== 0) {\n      token.content = escapedStr;\n    } else {\n      token.content = origStr;\n    }\n\n    token.markup = origStr;\n    token.info   = 'escape';\n  }\n\n  state.pos = pos + 1;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACD,OAAO;AAEhD,IAAIE,OAAO,GAAG,EAAE;AAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;EAAED,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC;AAAE;AAEjD,oCAAoC,CACjCC,KAAK,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,UAAUC,EAAE,EAAE;EAAEL,OAAO,CAACK,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAAE,CAAC,CAAC;AAGtEC,MAAM,CAACC,OAAO,GAAG,SAASC,MAAMA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC9C,IAAIC,GAAG;IAAEC,GAAG;IAAEC,OAAO;IAAEC,UAAU;IAAEC,KAAK;IAAEC,GAAG,GAAGP,KAAK,CAACO,GAAG;IAAEC,GAAG,GAAGR,KAAK,CAACS,MAAM;EAE7E,IAAIT,KAAK,CAACU,GAAG,CAACd,UAAU,CAACW,GAAG,CAAC,KAAK,IAAI,UAAS,OAAO,KAAK;EAC3DA,GAAG,EAAE;;EAEL;EACA,IAAIA,GAAG,IAAIC,GAAG,EAAE,OAAO,KAAK;EAE5BN,GAAG,GAAGF,KAAK,CAACU,GAAG,CAACd,UAAU,CAACW,GAAG,CAAC;EAE/B,IAAIL,GAAG,KAAK,IAAI,EAAE;IAChB,IAAI,CAACD,MAAM,EAAE;MACXD,KAAK,CAACR,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IAClC;IAEAe,GAAG,EAAE;IACL;IACA,OAAOA,GAAG,GAAGC,GAAG,EAAE;MAChBN,GAAG,GAAGF,KAAK,CAACU,GAAG,CAACd,UAAU,CAACW,GAAG,CAAC;MAC/B,IAAI,CAACnB,OAAO,CAACc,GAAG,CAAC,EAAE;MACnBK,GAAG,EAAE;IACP;IAEAP,KAAK,CAACO,GAAG,GAAGA,GAAG;IACf,OAAO,IAAI;EACb;EAEAF,UAAU,GAAGL,KAAK,CAACU,GAAG,CAACH,GAAG,CAAC;EAE3B,IAAIL,GAAG,IAAI,MAAM,IAAIA,GAAG,IAAI,MAAM,IAAIK,GAAG,GAAG,CAAC,GAAGC,GAAG,EAAE;IACnDL,GAAG,GAAGH,KAAK,CAACU,GAAG,CAACd,UAAU,CAACW,GAAG,GAAG,CAAC,CAAC;IAEnC,IAAIJ,GAAG,IAAI,MAAM,IAAIA,GAAG,IAAI,MAAM,EAAE;MAClCE,UAAU,IAAIL,KAAK,CAACU,GAAG,CAACH,GAAG,GAAG,CAAC,CAAC;MAChCA,GAAG,EAAE;IACP;EACF;EAEAH,OAAO,GAAG,IAAI,GAAGC,UAAU;EAE3B,IAAI,CAACJ,MAAM,EAAE;IACXK,KAAK,GAAGN,KAAK,CAACR,IAAI,CAAC,cAAc,EAAE,EAAE,EAAE,CAAC,CAAC;IAEzC,IAAIU,GAAG,GAAG,GAAG,IAAIZ,OAAO,CAACY,GAAG,CAAC,KAAK,CAAC,EAAE;MACnCI,KAAK,CAACK,OAAO,GAAGN,UAAU;IAC5B,CAAC,MAAM;MACLC,KAAK,CAACK,OAAO,GAAGP,OAAO;IACzB;IAEAE,KAAK,CAACM,MAAM,GAAGR,OAAO;IACtBE,KAAK,CAACO,IAAI,GAAK,QAAQ;EACzB;EAEAb,KAAK,CAACO,GAAG,GAAGA,GAAG,GAAG,CAAC;EACnB,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}