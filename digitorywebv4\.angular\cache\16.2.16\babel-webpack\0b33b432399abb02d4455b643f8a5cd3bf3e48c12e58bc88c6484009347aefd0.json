{"ast": null, "code": "// Parse link destination\n//\n'use strict';\n\nvar unescapeAll = require('../common/utils').unescapeAll;\nmodule.exports = function parseLinkDestination(str, pos, max) {\n  var code,\n    level,\n    lines = 0,\n    start = pos,\n    result = {\n      ok: false,\n      pos: 0,\n      lines: 0,\n      str: ''\n    };\n  if (str.charCodeAt(pos) === 0x3C /* < */) {\n    pos++;\n    while (pos < max) {\n      code = str.charCodeAt(pos);\n      if (code === 0x0A /* \\n */) {\n        return result;\n      }\n      if (code === 0x3C /* < */) {\n        return result;\n      }\n      if (code === 0x3E /* > */) {\n        result.pos = pos + 1;\n        result.str = unescapeAll(str.slice(start + 1, pos));\n        result.ok = true;\n        return result;\n      }\n      if (code === 0x5C /* \\ */ && pos + 1 < max) {\n        pos += 2;\n        continue;\n      }\n      pos++;\n    }\n\n    // no closing '>'\n    return result;\n  }\n\n  // this should be ... } else { ... branch\n\n  level = 0;\n  while (pos < max) {\n    code = str.charCodeAt(pos);\n    if (code === 0x20) {\n      break;\n    }\n\n    // ascii control characters\n    if (code < 0x20 || code === 0x7F) {\n      break;\n    }\n    if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      if (str.charCodeAt(pos + 1) === 0x20) {\n        break;\n      }\n      pos += 2;\n      continue;\n    }\n    if (code === 0x28 /* ( */) {\n      level++;\n      if (level > 32) {\n        return result;\n      }\n    }\n    if (code === 0x29 /* ) */) {\n      if (level === 0) {\n        break;\n      }\n      level--;\n    }\n    pos++;\n  }\n  if (start === pos) {\n    return result;\n  }\n  if (level !== 0) {\n    return result;\n  }\n  result.str = unescapeAll(str.slice(start, pos));\n  result.lines = lines;\n  result.pos = pos;\n  result.ok = true;\n  return result;\n};", "map": {"version": 3, "names": ["unescapeAll", "require", "module", "exports", "parseLinkDestination", "str", "pos", "max", "code", "level", "lines", "start", "result", "ok", "charCodeAt", "slice"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/helpers/parse_link_destination.js"], "sourcesContent": ["// Parse link destination\n//\n'use strict';\n\n\nvar unescapeAll = require('../common/utils').unescapeAll;\n\n\nmodule.exports = function parseLinkDestination(str, pos, max) {\n  var code, level,\n      lines = 0,\n      start = pos,\n      result = {\n        ok: false,\n        pos: 0,\n        lines: 0,\n        str: ''\n      };\n\n  if (str.charCodeAt(pos) === 0x3C /* < */) {\n    pos++;\n    while (pos < max) {\n      code = str.charCodeAt(pos);\n      if (code === 0x0A /* \\n */) { return result; }\n      if (code === 0x3C /* < */) { return result; }\n      if (code === 0x3E /* > */) {\n        result.pos = pos + 1;\n        result.str = unescapeAll(str.slice(start + 1, pos));\n        result.ok = true;\n        return result;\n      }\n      if (code === 0x5C /* \\ */ && pos + 1 < max) {\n        pos += 2;\n        continue;\n      }\n\n      pos++;\n    }\n\n    // no closing '>'\n    return result;\n  }\n\n  // this should be ... } else { ... branch\n\n  level = 0;\n  while (pos < max) {\n    code = str.charCodeAt(pos);\n\n    if (code === 0x20) { break; }\n\n    // ascii control characters\n    if (code < 0x20 || code === 0x7F) { break; }\n\n    if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      if (str.charCodeAt(pos + 1) === 0x20) { break; }\n      pos += 2;\n      continue;\n    }\n\n    if (code === 0x28 /* ( */) {\n      level++;\n      if (level > 32) { return result; }\n    }\n\n    if (code === 0x29 /* ) */) {\n      if (level === 0) { break; }\n      level--;\n    }\n\n    pos++;\n  }\n\n  if (start === pos) { return result; }\n  if (level !== 0) { return result; }\n\n  result.str = unescapeAll(str.slice(start, pos));\n  result.lines = lines;\n  result.pos = pos;\n  result.ok = true;\n  return result;\n};\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAGZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACD,WAAW;AAGxDE,MAAM,CAACC,OAAO,GAAG,SAASC,oBAAoBA,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC5D,IAAIC,IAAI;IAAEC,KAAK;IACXC,KAAK,GAAG,CAAC;IACTC,KAAK,GAAGL,GAAG;IACXM,MAAM,GAAG;MACPC,EAAE,EAAE,KAAK;MACTP,GAAG,EAAE,CAAC;MACNI,KAAK,EAAE,CAAC;MACRL,GAAG,EAAE;IACP,CAAC;EAEL,IAAIA,GAAG,CAACS,UAAU,CAACR,GAAG,CAAC,KAAK,IAAI,CAAC,SAAS;IACxCA,GAAG,EAAE;IACL,OAAOA,GAAG,GAAGC,GAAG,EAAE;MAChBC,IAAI,GAAGH,GAAG,CAACS,UAAU,CAACR,GAAG,CAAC;MAC1B,IAAIE,IAAI,KAAK,IAAI,CAAC,UAAU;QAAE,OAAOI,MAAM;MAAE;MAC7C,IAAIJ,IAAI,KAAK,IAAI,CAAC,SAAS;QAAE,OAAOI,MAAM;MAAE;MAC5C,IAAIJ,IAAI,KAAK,IAAI,CAAC,SAAS;QACzBI,MAAM,CAACN,GAAG,GAAGA,GAAG,GAAG,CAAC;QACpBM,MAAM,CAACP,GAAG,GAAGL,WAAW,CAACK,GAAG,CAACU,KAAK,CAACJ,KAAK,GAAG,CAAC,EAAEL,GAAG,CAAC,CAAC;QACnDM,MAAM,CAACC,EAAE,GAAG,IAAI;QAChB,OAAOD,MAAM;MACf;MACA,IAAIJ,IAAI,KAAK,IAAI,CAAC,WAAWF,GAAG,GAAG,CAAC,GAAGC,GAAG,EAAE;QAC1CD,GAAG,IAAI,CAAC;QACR;MACF;MAEAA,GAAG,EAAE;IACP;;IAEA;IACA,OAAOM,MAAM;EACf;;EAEA;;EAEAH,KAAK,GAAG,CAAC;EACT,OAAOH,GAAG,GAAGC,GAAG,EAAE;IAChBC,IAAI,GAAGH,GAAG,CAACS,UAAU,CAACR,GAAG,CAAC;IAE1B,IAAIE,IAAI,KAAK,IAAI,EAAE;MAAE;IAAO;;IAE5B;IACA,IAAIA,IAAI,GAAG,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;MAAE;IAAO;IAE3C,IAAIA,IAAI,KAAK,IAAI,CAAC,WAAWF,GAAG,GAAG,CAAC,GAAGC,GAAG,EAAE;MAC1C,IAAIF,GAAG,CAACS,UAAU,CAACR,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;QAAE;MAAO;MAC/CA,GAAG,IAAI,CAAC;MACR;IACF;IAEA,IAAIE,IAAI,KAAK,IAAI,CAAC,SAAS;MACzBC,KAAK,EAAE;MACP,IAAIA,KAAK,GAAG,EAAE,EAAE;QAAE,OAAOG,MAAM;MAAE;IACnC;IAEA,IAAIJ,IAAI,KAAK,IAAI,CAAC,SAAS;MACzB,IAAIC,KAAK,KAAK,CAAC,EAAE;QAAE;MAAO;MAC1BA,KAAK,EAAE;IACT;IAEAH,GAAG,EAAE;EACP;EAEA,IAAIK,KAAK,KAAKL,GAAG,EAAE;IAAE,OAAOM,MAAM;EAAE;EACpC,IAAIH,KAAK,KAAK,CAAC,EAAE;IAAE,OAAOG,MAAM;EAAE;EAElCA,MAAM,CAACP,GAAG,GAAGL,WAAW,CAACK,GAAG,CAACU,KAAK,CAACJ,KAAK,EAAEL,GAAG,CAAC,CAAC;EAC/CM,MAAM,CAACF,KAAK,GAAGA,KAAK;EACpBE,MAAM,CAACN,GAAG,GAAGA,GAAG;EAChBM,MAAM,CAACC,EAAE,GAAG,IAAI;EAChB,OAAOD,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}