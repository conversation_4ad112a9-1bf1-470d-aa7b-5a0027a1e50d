{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/smart-dashboard.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"../../services/share-data.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/datepicker\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"ng2-charts\";\nimport * as i15 from \"ngx-mat-select-search\";\nimport * as i16 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const branch_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", branch_r5.restaurantIdOld);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", branch_r5.branchName, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵelement(1, \"mat-spinner\", 45);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SmartDashboardComponent_div_97_mat_card_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 53)(1, \"mat-card-content\")(2, \"div\", 54)(3, \"div\", 55)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 56)(7, \"div\", 57);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 58);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const card_r10 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", card_r10.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", card_r10.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r10.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(card_r10.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r10.label);\n  }\n}\nfunction SmartDashboardComponent_div_97_mat_card_4_canvas_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 63);\n  }\n  if (rf & 2) {\n    const chart_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"type\", \"line\")(\"data\", ctx_r13.getChartData(chart_r11))(\"options\", ctx_r13.lineChartOptions);\n  }\n}\nfunction SmartDashboardComponent_div_97_mat_card_4_canvas_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 63);\n  }\n  if (rf & 2) {\n    const chart_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"type\", \"bar\")(\"data\", ctx_r14.getChartData(chart_r11))(\"options\", ctx_r14.barChartOptions);\n  }\n}\nfunction SmartDashboardComponent_div_97_mat_card_4_canvas_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 63);\n  }\n  if (rf & 2) {\n    const chart_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"type\", \"doughnut\")(\"data\", ctx_r15.getChartData(chart_r11))(\"options\", ctx_r15.doughnutChartOptions);\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"full-width\": a0,\n    \"half-width\": a1,\n    \"third-width\": a2\n  };\n};\nfunction SmartDashboardComponent_div_97_mat_card_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 59)(1, \"mat-card-header\")(2, \"mat-card-title\", 60);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 61);\n    i0.ɵɵtemplate(6, SmartDashboardComponent_div_97_mat_card_4_canvas_6_Template, 1, 3, \"canvas\", 62);\n    i0.ɵɵtemplate(7, SmartDashboardComponent_div_97_mat_card_4_canvas_7_Template, 1, 3, \"canvas\", 62);\n    i0.ɵɵtemplate(8, SmartDashboardComponent_div_97_mat_card_4_canvas_8_Template, 1, 3, \"canvas\", 62);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const chart_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, chart_r11.type === \"line\" || chart_r11.title.toLowerCase().includes(\"trend\"), chart_r11.type !== \"line\" && !chart_r11.title.toLowerCase().includes(\"trend\"), chart_r11.type === \"doughnut\" || chart_r11.type === \"pie\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(chart_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-chart-type\", chart_r11.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chart_r11.type === \"line\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chart_r11.type === \"bar\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chart_r11.type === \"doughnut\" || chart_r11.type === \"pie\");\n  }\n}\nfunction SmartDashboardComponent_div_97_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-card\", 64)(2, \"mat-card-header\")(3, \"mat-card-title\", 60);\n    i0.ɵɵtext(4, \"Purchase Trends (Last 30 Days)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 61)(7, \"div\", 65)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"show_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"Purchase trends data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(12, \"mat-card\", 66)(13, \"mat-card-header\")(14, \"mat-card-title\", 60);\n    i0.ɵɵtext(15, \"Top Vendors by Purchase Amount\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"mat-card-content\")(17, \"div\", 61)(18, \"div\", 65)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\");\n    i0.ɵɵtext(22, \"Vendor data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"mat-card\", 66)(24, \"mat-card-header\")(25, \"mat-card-title\", 60);\n    i0.ɵɵtext(26, \"Category wise Spending Distribution\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"mat-card-content\")(28, \"div\", 61)(29, \"div\", 65)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"pie_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"Category spending data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"mat-card\", 64)(35, \"mat-card-header\")(36, \"mat-card-title\", 60);\n    i0.ɵɵtext(37, \"Top Items by Quantity Purchased\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"mat-card-content\")(39, \"div\", 61)(40, \"div\", 65)(41, \"mat-icon\");\n    i0.ɵɵtext(42, \"inventory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\");\n    i0.ɵɵtext(44, \"Top items data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SmartDashboardComponent_div_97_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"mat-icon\", 68);\n    i0.ɵɵtext(2, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Data Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Please select a location and date range to view dashboard data.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_97_div_6_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.loadDashboardData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Refresh Data \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SmartDashboardComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_97_mat_card_2_Template, 11, 7, \"mat-card\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtemplate(4, SmartDashboardComponent_div_97_mat_card_4_Template, 9, 10, \"mat-card\", 50);\n    i0.ɵɵtemplate(5, SmartDashboardComponent_div_97_ng_container_5_Template, 45, 0, \"ng-container\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SmartDashboardComponent_div_97_div_6_Template, 11, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.summaryCards);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.charts);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.charts.length === 0 && !ctx_r4.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isLoading && ctx_r4.summaryCards.length === 0);\n  }\n}\nclass SmartDashboardComponent {\n  constructor(smartDashboardService, authService, shareDataService, cdr) {\n    this.smartDashboardService = smartDashboardService;\n    this.authService = authService;\n    this.shareDataService = shareDataService;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.branches = [];\n    this.filteredBranches = [];\n    this.selectedLocation = null;\n    // Form controls\n    this.locationFilterCtrl = new FormControl('');\n    this.startDate = new FormControl(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 30 days ago\n    this.endDate = new FormControl(new Date());\n    this.searchQuery = new FormControl('');\n    this.baseDateCtrl = new FormControl('deliveryDate');\n    this.selectedDashboard = 'purchase';\n    // Dashboard data\n    this.summaryCards = [];\n    this.charts = [];\n    this.isLoading = false;\n    // Chart configurations\n    this.lineChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: true,\n          position: 'top',\n          labels: {\n            usePointStyle: true,\n            padding: 20\n          }\n        },\n        tooltip: {\n          mode: 'index',\n          intersect: false,\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          titleColor: '#fff',\n          bodyColor: '#fff',\n          borderColor: '#ff6b35',\n          borderWidth: 1\n        }\n      },\n      scales: {\n        x: {\n          display: true,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          },\n          title: {\n            display: false\n          }\n        },\n        y: {\n          display: true,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          },\n          title: {\n            display: false\n          },\n          beginAtZero: true,\n          ticks: {\n            callback: function (value) {\n              return '₹' + value.toLocaleString();\n            }\n          }\n        }\n      },\n      interaction: {\n        mode: 'nearest',\n        axis: 'x',\n        intersect: false\n      }\n    };\n    this.barChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      indexAxis: 'y',\n      plugins: {\n        legend: {\n          display: false\n        },\n        tooltip: {\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          titleColor: '#fff',\n          bodyColor: '#fff',\n          borderColor: '#ff6b35',\n          borderWidth: 1,\n          callbacks: {\n            label: function (context) {\n              return '₹' + context.parsed.x.toLocaleString();\n            }\n          }\n        }\n      },\n      scales: {\n        x: {\n          beginAtZero: true,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          },\n          title: {\n            display: false\n          },\n          ticks: {\n            callback: function (value) {\n              return '₹' + value.toLocaleString();\n            }\n          }\n        },\n        y: {\n          grid: {\n            display: false\n          },\n          title: {\n            display: false\n          }\n        }\n      }\n    };\n    this.doughnutChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: true,\n          position: 'right',\n          labels: {\n            usePointStyle: true,\n            padding: 15,\n            font: {\n              size: 12\n            }\n          }\n        },\n        tooltip: {\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          titleColor: '#fff',\n          bodyColor: '#fff',\n          borderColor: '#ff6b35',\n          borderWidth: 1,\n          callbacks: {\n            label: function (context) {\n              const label = context.label || '';\n              const value = context.parsed;\n              const total = context.dataset.data.reduce((a, b) => a + b, 0);\n              const percentage = (value / total * 100).toFixed(1);\n              return `${label}: ${percentage}%`;\n            }\n          }\n        }\n      },\n      cutout: '60%'\n    };\n    this.quantityBarChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          title: {\n            display: true,\n            text: 'Items'\n          }\n        },\n        y: {\n          beginAtZero: true,\n          title: {\n            display: true,\n            text: 'Quantity'\n          }\n        }\n      }\n    };\n    this.user = this.authService.getCurrentUser();\n  }\n  ngOnInit() {\n    this.initializeFilters();\n    this.loadBranches();\n    this.loadDashboardData();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeFilters() {\n    // Location filter\n    this.locationFilterCtrl.valueChanges.pipe(debounceTime(200), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(value => {\n      this.filterBranches(value || '');\n    });\n  }\n  loadBranches() {\n    this.shareDataService.selectedBranchesSource.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.branches = data || [];\n      this.filteredBranches = [...this.branches];\n      if (this.branches.length === 1) {\n        this.selectedLocation = this.branches[0].restaurantIdOld;\n      }\n    });\n  }\n  filterBranches(searchTerm) {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm));\n    }\n  }\n  loadDashboardData() {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          // Load sample data for demonstration\n          this.loadSampleData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading dashboard data:', error);\n        // Load sample data on error for demonstration\n        this.loadSampleData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  loadSampleData() {\n    // Sample data matching the UI shown in the image\n    const sampleData = {\n      summary_items: [{\n        icon: 'attach_money',\n        value: '₹34,766',\n        label: 'Total Purchase Amount',\n        data_type: 'currency'\n      }, {\n        icon: 'shopping_cart',\n        value: '271',\n        label: 'Total Orders',\n        data_type: 'number'\n      }, {\n        icon: 'trending_up',\n        value: '₹1,973',\n        label: 'Average Order Value',\n        data_type: 'currency'\n      }, {\n        icon: 'store',\n        value: 'Fresh Mart',\n        label: 'Top Vendor',\n        data_type: 'vendor'\n      }],\n      charts: [{\n        id: 'purchase-trends',\n        title: 'Purchase Trends (Last 30 Days)',\n        type: 'line',\n        data: {\n          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n          datasets: [{\n            label: 'Daily Purchase Amount',\n            data: [15000, 22000, 18000, 25000],\n            backgroundColor: ['#ff6b35'],\n            borderColor: ['#ff6b35']\n          }]\n        }\n      }, {\n        id: 'top-vendors',\n        title: 'Top Vendors by Purchase Amount',\n        type: 'bar',\n        data: {\n          labels: ['ABC Suppliers', 'XYZ Foods', 'Fresh Mart', 'Quality Grocers', 'Best Price Co'],\n          datasets: [{\n            label: 'Total Purchase Amount',\n            data: [180000, 150000, 140000, 120000, 100000],\n            backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n            borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n          }]\n        }\n      }, {\n        id: 'category-spending',\n        title: 'Category wise Spending Distribution',\n        type: 'doughnut',\n        data: {\n          labels: ['Vegetables', 'Meat', 'Dairy', 'Beverages', 'Dry Goods'],\n          datasets: [{\n            label: 'Spending Distribution',\n            data: [30, 25, 20, 15, 10],\n            backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n            borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n          }]\n        }\n      }]\n    };\n    this.processDashboardData(sampleData);\n  }\n  processDashboardData(data) {\n    // Process summary cards\n    this.summaryCards = data.summary_items?.map(item => ({\n      icon: this.smartDashboardService.getSummaryCardIcon(item.data_type, item.label),\n      value: item.value,\n      label: item.label,\n      color: this.getCardColor(item.data_type)\n    })) || [];\n    // If no summary cards from API, create default ones\n    if (this.summaryCards.length === 0) {\n      this.summaryCards = this.createDefaultSummaryCards();\n    }\n    // Process charts with enhanced data\n    this.charts = data.charts?.map(chart => ({\n      ...chart,\n      data: this.smartDashboardService.processChartData(chart)\n    })) || [];\n    // If no charts from API, create default message\n    if (this.charts.length === 0) {\n      console.log('No charts data received from API');\n    }\n  }\n  createDefaultSummaryCards() {\n    return [{\n      icon: 'attach_money',\n      value: '₹34,766',\n      label: 'Total Purchase Amount',\n      color: '#ff6b35'\n    }, {\n      icon: 'shopping_cart',\n      value: '271',\n      label: 'Total Orders',\n      color: '#ffa66f'\n    }, {\n      icon: 'trending_up',\n      value: '₹1,973',\n      label: 'Average Order Value',\n      color: '#ff8b4d'\n    }, {\n      icon: 'store',\n      value: 'Fresh Mart',\n      label: 'Top Vendor',\n      color: '#ff9966'\n    }];\n  }\n  getCardColor(dataType) {\n    const colorMap = {\n      'currency': '#ff6b35',\n      'number': '#ffa66f',\n      'percentage': '#ff8b4d',\n      'vendor': '#ff9966'\n    };\n    return colorMap[dataType] || '#ff6b35';\n  }\n  formatDate(date) {\n    return date.toISOString().split('T')[0];\n  }\n  onLocationChange() {\n    this.loadDashboardData();\n  }\n  onDateChange() {\n    this.loadDashboardData();\n  }\n  onSearchQuery() {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n  loadDashboardDataWithQuery(query) {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success' && response.data.charts.length > 0) {\n          this.processDashboardData(response.data);\n        } else {\n          // Query returned no results, show default data\n          console.log('Query returned no data, loading default dashboard');\n          this.loadDashboardData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.warn('Error with query, falling back to default dashboard:', error);\n        this.loadDashboardData();\n      }\n    });\n  }\n  getChartData(chart) {\n    return chart.data;\n  }\n  getChartType(chart) {\n    return chart.type;\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.SmartDashboardService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 98,\n      vars: 14,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"top-header-gradient\"], [1, \"dashboard-selection\"], [\"appearance\", \"outline\", 1, \"dashboard-dropdown\"], [3, \"value\", \"valueChange\"], [\"value\", \"purchase\"], [\"value\", \"sales\"], [\"value\", \"inventory\"], [1, \"main-layout\"], [1, \"left-sidebar\"], [1, \"filters-section\"], [1, \"filters-title\"], [1, \"filter-count\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [3, \"value\", \"valueChange\", \"selectionChange\"], [\"placeholderLabel\", \"Search locations...\", \"noEntriesFoundLabel\", \"No locations found\", 3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"formControl\", \"selectionChange\"], [\"value\", \"deliveryDate\"], [\"value\", \"orderDate\"], [\"value\", \"createdDate\"], [\"matInput\", \"\", 3, \"matDatepicker\", \"formControl\", \"dateChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"endPicker\", \"\"], [1, \"filter-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-filters-btn\", 3, \"click\"], [1, \"right-content\"], [1, \"search-header\"], [1, \"assistant-info\"], [1, \"assistant-icon\"], [1, \"assistant-text\"], [1, \"assistant-title\"], [1, \"assistant-status\"], [1, \"search-container\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Ask me about your business data\", 3, \"formControl\", \"keyup.enter\"], [\"matSuffix\", \"\", 1, \"search-icon\", 3, \"click\"], [1, \"dashboard-content-area\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"dashboard-grid\", 4, \"ngIf\"], [3, \"value\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"dashboard-grid\"], [1, \"summary-cards-row\"], [\"class\", \"summary-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"charts-grid\"], [\"class\", \"chart-card\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"summary-card\"], [1, \"card-content\"], [1, \"card-icon\"], [1, \"card-info\"], [1, \"card-value\"], [1, \"card-label\"], [1, \"chart-card\", 3, \"ngClass\"], [1, \"chart-title\"], [1, \"chart-container\"], [\"baseChart\", \"\", 3, \"type\", \"data\", \"options\", 4, \"ngIf\"], [\"baseChart\", \"\", 3, \"type\", \"data\", \"options\"], [1, \"chart-card\", \"full-width\"], [1, \"no-data-message\"], [1, \"chart-card\", \"half-width\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"mat-form-field\", 3)(4, \"mat-label\");\n          i0.ɵɵtext(5, \"Purchase Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"mat-select\", 4);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_6_listener($event) {\n            return ctx.selectedDashboard = $event;\n          });\n          i0.ɵɵelementStart(7, \"mat-option\", 5);\n          i0.ɵɵtext(8, \"Purchase Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"mat-option\", 6);\n          i0.ɵɵtext(10, \"Sales Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"mat-option\", 7);\n          i0.ɵɵtext(12, \"Inventory Dashboard\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"div\", 10)(16, \"h3\", 11)(17, \"mat-icon\");\n          i0.ɵɵtext(18, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \" Smart Filters \");\n          i0.ɵɵelementStart(20, \"span\", 12);\n          i0.ɵɵtext(21, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 13)(23, \"h4\", 14)(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \" Restaurants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"mat-form-field\", 15)(28, \"mat-label\");\n          i0.ɵɵtext(29, \"Select restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-select\", 16);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_30_listener($event) {\n            return ctx.selectedLocation = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_30_listener() {\n            return ctx.onLocationChange();\n          });\n          i0.ɵɵelementStart(31, \"mat-option\");\n          i0.ɵɵelement(32, \"ngx-mat-select-search\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(33, SmartDashboardComponent_mat_option_33_Template, 2, 2, \"mat-option\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"div\", 13)(35, \"h4\", 14)(36, \"mat-icon\");\n          i0.ɵɵtext(37, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(38, \" Base Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 15)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Select base date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"mat-select\", 19);\n          i0.ɵɵlistener(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_42_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementStart(43, \"mat-option\", 20);\n          i0.ɵɵtext(44, \"Delivery Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"mat-option\", 21);\n          i0.ɵɵtext(46, \"Order Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-option\", 22);\n          i0.ɵɵtext(48, \"Created Date\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(49, \"div\", 13)(50, \"h4\", 14)(51, \"mat-icon\");\n          i0.ɵɵtext(52, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" Start Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-form-field\", 15)(55, \"mat-label\");\n          i0.ɵɵtext(56, \"Start Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"input\", 23);\n          i0.ɵɵlistener(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_57_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"mat-datepicker-toggle\", 24)(59, \"mat-datepicker\", null, 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 13)(62, \"h4\", 14)(63, \"mat-icon\");\n          i0.ɵɵtext(64, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65, \" End Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"mat-form-field\", 15)(67, \"mat-label\");\n          i0.ɵɵtext(68, \"End Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"input\", 23);\n          i0.ɵɵlistener(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_69_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(70, \"mat-datepicker-toggle\", 24)(71, \"mat-datepicker\", null, 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"div\", 27)(74, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_74_listener() {\n            return ctx.loadDashboardData();\n          });\n          i0.ɵɵelementStart(75, \"mat-icon\");\n          i0.ɵɵtext(76, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(77, \" Reset filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(78, \"div\", 29)(79, \"div\", 30)(80, \"div\", 31)(81, \"mat-icon\", 32);\n          i0.ɵɵtext(82, \"smart_toy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"div\", 33)(84, \"span\", 34);\n          i0.ɵɵtext(85, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"span\", 35);\n          i0.ɵɵtext(87, \"Ready to analyze\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(88, \"div\", 36)(89, \"mat-form-field\", 37)(90, \"mat-label\");\n          i0.ɵɵtext(91, \"Ask me about your business data\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"input\", 38);\n          i0.ɵɵlistener(\"keyup.enter\", function SmartDashboardComponent_Template_input_keyup_enter_92_listener() {\n            return ctx.onSearchQuery();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"mat-icon\", 39);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_mat_icon_click_93_listener() {\n            return ctx.onSearchQuery();\n          });\n          i0.ɵɵtext(94, \"search\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(95, \"div\", 40);\n          i0.ɵɵtemplate(96, SmartDashboardComponent_div_96_Template, 4, 0, \"div\", 41);\n          i0.ɵɵtemplate(97, SmartDashboardComponent_div_97_Template, 7, 4, \"div\", 42);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(60);\n          const _r2 = i0.ɵɵreference(72);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"value\", ctx.selectedDashboard);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.locationFilterCtrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredBranches);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formControl\", ctx.baseDateCtrl);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"matDatepicker\", _r1)(\"formControl\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r1);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"matDatepicker\", _r2)(\"formControl\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r2);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"formControl\", ctx.searchQuery);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, MatCardModule, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, MatButtonModule, i6.MatButton, MatIconModule, i7.MatIcon, MatSelectModule, i8.MatFormField, i8.MatLabel, i8.MatSuffix, i9.MatSelect, i10.MatOption, MatFormFieldModule, MatInputModule, i11.MatInput, MatDatepickerModule, i12.MatDatepicker, i12.MatDatepickerInput, i12.MatDatepickerToggle, MatNativeDateModule, MatDividerModule, MatProgressSpinnerModule, i13.MatProgressSpinner, NgChartsModule, i14.BaseChartDirective, NgxMatSelectSearchModule, i15.MatSelectSearchComponent, ReactiveFormsModule, i16.DefaultValueAccessor, i16.NgControlStatus, i16.FormControlDirective, FormsModule],\n      styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 571:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"}\\\\\\\".\\\\n    \\u2577\\\\n551 \\u2502 }\\\\n    \\u2502  ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\smart-dashboard\\\\\\\\smart-dashboard.component.scss 551:2  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t})[_ngcontent-%COMP%];\\n\\n\\n\\n \\t\\n\\n \\t//[_ngcontent-%COMP%]   startup\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   Load[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   and[_ngcontent-%COMP%]   return[_ngcontent-%COMP%]   exports\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   This[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   doesn't[_ngcontent-%COMP%]   tell[_ngcontent-%COMP%]   about[_ngcontent-%COMP%]   it's[_ngcontent-%COMP%]   top-level[_ngcontent-%COMP%]   declarations[_ngcontent-%COMP%]   so[_ngcontent-%COMP%]   it[_ngcontent-%COMP%]   can't[_ngcontent-%COMP%]   be[_ngcontent-%COMP%]   inlined\\n\\n[_ngcontent-%COMP%]   var[_ngcontent-%COMP%]   __webpack_exports__[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] {};\\n\\n \\t__webpack_modules__[571]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatFormFieldModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatDividerModule", "MatProgressSpinnerModule", "NgChartsModule", "NgxMatSelectSearchModule", "FormControl", "ReactiveFormsModule", "FormsModule", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "branch_r5", "restaurantIdOld", "ɵɵadvance", "ɵɵtextInterpolate1", "branchName", "ɵɵelement", "ɵɵstyleProp", "card_r10", "color", "ɵɵtextInterpolate", "icon", "value", "label", "ctx_r13", "getChartData", "chart_r11", "lineChartOptions", "ctx_r14", "barChartOptions", "ctx_r15", "doughnutChartOptions", "ɵɵtemplate", "SmartDashboardComponent_div_97_mat_card_4_canvas_6_Template", "SmartDashboardComponent_div_97_mat_card_4_canvas_7_Template", "SmartDashboardComponent_div_97_mat_card_4_canvas_8_Template", "ɵɵpureFunction3", "_c0", "type", "title", "toLowerCase", "includes", "ɵɵattribute", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵlistener", "SmartDashboardComponent_div_97_div_6_Template_button_click_7_listener", "ɵɵrestoreView", "_r20", "ctx_r19", "ɵɵnextContext", "ɵɵresetView", "loadDashboardData", "SmartDashboardComponent_div_97_mat_card_2_Template", "SmartDashboardComponent_div_97_mat_card_4_Template", "SmartDashboardComponent_div_97_ng_container_5_Template", "SmartDashboardComponent_div_97_div_6_Template", "ctx_r4", "summaryCards", "charts", "length", "isLoading", "SmartDashboardComponent", "constructor", "smartDashboardService", "authService", "shareDataService", "cdr", "destroy$", "branches", "filteredBranches", "selectedLocation", "locationFilterCtrl", "startDate", "Date", "now", "endDate", "searchQuery", "baseDateCtrl", "selectedDashboard", "responsive", "maintainAspectRatio", "plugins", "legend", "display", "position", "labels", "usePointStyle", "padding", "tooltip", "mode", "intersect", "backgroundColor", "titleColor", "bodyColor", "borderColor", "borderWidth", "scales", "x", "grid", "y", "beginAtZero", "ticks", "callback", "toLocaleString", "interaction", "axis", "indexAxis", "callbacks", "context", "parsed", "font", "size", "total", "dataset", "data", "reduce", "a", "b", "percentage", "toFixed", "cutout", "quantityBarChartOptions", "text", "user", "getCurrentUser", "ngOnInit", "initializeFilters", "loadBranches", "ngOnDestroy", "next", "complete", "valueChanges", "pipe", "subscribe", "filterBranches", "selectedBranchesSource", "searchTerm", "normalizedSearchTerm", "replace", "filter", "branch", "filters", "locations", "formatDate", "baseDate", "request", "tenant_id", "tenantId", "user_query", "use_default_charts", "getSmartDashboardData", "response", "status", "processDashboardData", "loadSampleData", "detectChanges", "error", "console", "sampleData", "summary_items", "data_type", "id", "datasets", "map", "item", "getSummaryCardIcon", "getCardColor", "createDefaultSummaryCards", "chart", "processChartData", "log", "dataType", "colorMap", "date", "toISOString", "split", "onLocationChange", "onDateChange", "onSearchQuery", "query", "trim", "loadDashboardDataWithQuery", "warn", "getChartType", "ɵɵdirectiveInject", "i1", "SmartDashboardService", "i2", "AuthService", "i3", "ShareDataService", "ChangeDetectorRef", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "SmartDashboardComponent_Template_mat_select_valueChange_6_listener", "$event", "SmartDashboardComponent_Template_mat_select_valueChange_30_listener", "SmartDashboardComponent_Template_mat_select_selectionChange_30_listener", "SmartDashboardComponent_mat_option_33_Template", "SmartDashboardComponent_Template_mat_select_selectionChange_42_listener", "SmartDashboardComponent_Template_input_dateChange_57_listener", "SmartDashboardComponent_Template_input_dateChange_69_listener", "SmartDashboardComponent_Template_button_click_74_listener", "SmartDashboardComponent_Template_input_keyup_enter_92_listener", "SmartDashboardComponent_Template_mat_icon_click_93_listener", "SmartDashboardComponent_div_96_Template", "SmartDashboardComponent_div_97_Template", "_r1", "_r2", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i6", "MatButton", "i7", "MatIcon", "i8", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i9", "MatSelect", "i10", "MatOption", "i11", "MatInput", "i12", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i13", "MatProgressSpinner", "i14", "BaseChartDirective", "i15", "MatSelectSearchComponent", "i16", "DefaultValueAccessor", "NgControlStatus", "FormControlDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { ChartConfiguration, ChartData, ChartType } from 'chart.js';\n\nimport { SmartDashboardService } from '../../services/smart-dashboard.service';\nimport { AuthService } from '../../services/auth.service';\nimport { ShareDataService } from '../../services/share-data.service';\n\ninterface SummaryCard {\n  icon: string;\n  value: string;\n  label: string;\n  color: string;\n}\n\ninterface ChartDataModel {\n  id: string;\n  title: string;\n  type: string;\n  data: ChartData;\n}\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    NgChartsModule,\n    NgxMatSelectSearchModule,\n    ReactiveFormsModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  // User and branch data\n  user: any;\n  branches: any[] = [];\n  filteredBranches: any[] = [];\n  selectedLocation: string | null = null;\n  \n  // Form controls\n  locationFilterCtrl = new FormControl('');\n  startDate = new FormControl(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 30 days ago\n  endDate = new FormControl(new Date());\n  searchQuery = new FormControl('');\n  baseDateCtrl = new FormControl('deliveryDate');\n  selectedDashboard = 'purchase';\n  \n  // Dashboard data\n  summaryCards: SummaryCard[] = [];\n  charts: ChartDataModel[] = [];\n  isLoading = false;\n  \n  // Chart configurations\n  lineChartOptions: ChartConfiguration['options'] = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: true,\n        position: 'top',\n        labels: {\n          usePointStyle: true,\n          padding: 20\n        }\n      },\n      tooltip: {\n        mode: 'index',\n        intersect: false,\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: '#ff6b35',\n        borderWidth: 1\n      }\n    },\n    scales: {\n      x: {\n        display: true,\n        grid: {\n          display: true,\n          color: 'rgba(0,0,0,0.1)'\n        },\n        title: {\n          display: false\n        }\n      },\n      y: {\n        display: true,\n        grid: {\n          display: true,\n          color: 'rgba(0,0,0,0.1)'\n        },\n        title: {\n          display: false\n        },\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return '₹' + value.toLocaleString();\n          }\n        }\n      }\n    },\n    interaction: {\n      mode: 'nearest',\n      axis: 'x',\n      intersect: false\n    }\n  };\n\n  barChartOptions: ChartConfiguration['options'] = {\n    responsive: true,\n    maintainAspectRatio: false,\n    indexAxis: 'y' as const,\n    plugins: {\n      legend: {\n        display: false\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: '#ff6b35',\n        borderWidth: 1,\n        callbacks: {\n          label: function(context: any) {\n            return '₹' + context.parsed.x.toLocaleString();\n          }\n        }\n      }\n    },\n    scales: {\n      x: {\n        beginAtZero: true,\n        grid: {\n          display: true,\n          color: 'rgba(0,0,0,0.1)'\n        },\n        title: {\n          display: false\n        },\n        ticks: {\n          callback: function(value: any) {\n            return '₹' + value.toLocaleString();\n          }\n        }\n      },\n      y: {\n        grid: {\n          display: false\n        },\n        title: {\n          display: false\n        }\n      }\n    }\n  };\n\n  doughnutChartOptions: any = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: true,\n        position: 'right',\n        labels: {\n          usePointStyle: true,\n          padding: 15,\n          font: {\n            size: 12\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: '#ff6b35',\n        borderWidth: 1,\n        callbacks: {\n          label: function(context: any) {\n            const label = context.label || '';\n            const value = context.parsed;\n            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);\n            const percentage = ((value / total) * 100).toFixed(1);\n            return `${label}: ${percentage}%`;\n          }\n        }\n      }\n    },\n    cutout: '60%'\n  };\n\n  quantityBarChartOptions: ChartConfiguration['options'] = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        title: {\n          display: true,\n          text: 'Items'\n        }\n      },\n      y: {\n        beginAtZero: true,\n        title: {\n          display: true,\n          text: 'Quantity'\n        }\n      }\n    }\n  };\n\n  constructor(\n    private smartDashboardService: SmartDashboardService,\n    private authService: AuthService,\n    private shareDataService: ShareDataService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.user = this.authService.getCurrentUser();\n  }\n\n  ngOnInit(): void {\n    this.initializeFilters();\n    this.loadBranches();\n    this.loadDashboardData();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeFilters(): void {\n    // Location filter\n    this.locationFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(200),\n        distinctUntilChanged(),\n        takeUntil(this.destroy$)\n      )\n      .subscribe((value: string | null) => {\n        this.filterBranches(value || '');\n      });\n  }\n\n  private loadBranches(): void {\n    this.shareDataService.selectedBranchesSource\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.branches = data || [];\n        this.filteredBranches = [...this.branches];\n        \n        if (this.branches.length === 1) {\n          this.selectedLocation = this.branches[0].restaurantIdOld;\n        }\n      });\n  }\n\n  private filterBranches(searchTerm: string): void {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch =>\n        branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm)\n      );\n    }\n  }\n\n  loadDashboardData(): void {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            // Load sample data for demonstration\n            this.loadSampleData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: (error) => {\n          console.error('Error loading dashboard data:', error);\n          // Load sample data on error for demonstration\n          this.loadSampleData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  private loadSampleData(): void {\n    // Sample data matching the UI shown in the image\n    const sampleData = {\n      summary_items: [\n        { icon: 'attach_money', value: '₹34,766', label: 'Total Purchase Amount', data_type: 'currency' },\n        { icon: 'shopping_cart', value: '271', label: 'Total Orders', data_type: 'number' },\n        { icon: 'trending_up', value: '₹1,973', label: 'Average Order Value', data_type: 'currency' },\n        { icon: 'store', value: 'Fresh Mart', label: 'Top Vendor', data_type: 'vendor' }\n      ],\n      charts: [\n        {\n          id: 'purchase-trends',\n          title: 'Purchase Trends (Last 30 Days)',\n          type: 'line',\n          data: {\n            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n            datasets: [{\n              label: 'Daily Purchase Amount',\n              data: [15000, 22000, 18000, 25000],\n              backgroundColor: ['#ff6b35'],\n              borderColor: ['#ff6b35']\n            }]\n          }\n        },\n        {\n          id: 'top-vendors',\n          title: 'Top Vendors by Purchase Amount',\n          type: 'bar',\n          data: {\n            labels: ['ABC Suppliers', 'XYZ Foods', 'Fresh Mart', 'Quality Grocers', 'Best Price Co'],\n            datasets: [{\n              label: 'Total Purchase Amount',\n              data: [180000, 150000, 140000, 120000, 100000],\n              backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n              borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n            }]\n          }\n        },\n        {\n          id: 'category-spending',\n          title: 'Category wise Spending Distribution',\n          type: 'doughnut',\n          data: {\n            labels: ['Vegetables', 'Meat', 'Dairy', 'Beverages', 'Dry Goods'],\n            datasets: [{\n              label: 'Spending Distribution',\n              data: [30, 25, 20, 15, 10],\n              backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n              borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n            }]\n          }\n        }\n      ]\n    };\n\n    this.processDashboardData(sampleData);\n  }\n\n  private processDashboardData(data: any): void {\n    // Process summary cards\n    this.summaryCards = data.summary_items?.map((item: any) => ({\n      icon: this.smartDashboardService.getSummaryCardIcon(item.data_type, item.label),\n      value: item.value,\n      label: item.label,\n      color: this.getCardColor(item.data_type)\n    })) || [];\n\n    // If no summary cards from API, create default ones\n    if (this.summaryCards.length === 0) {\n      this.summaryCards = this.createDefaultSummaryCards();\n    }\n\n    // Process charts with enhanced data\n    this.charts = data.charts?.map((chart: any) => ({\n      ...chart,\n      data: this.smartDashboardService.processChartData(chart)\n    })) || [];\n\n    // If no charts from API, create default message\n    if (this.charts.length === 0) {\n      console.log('No charts data received from API');\n    }\n  }\n\n  private createDefaultSummaryCards(): SummaryCard[] {\n    return [\n      {\n        icon: 'attach_money',\n        value: '₹34,766',\n        label: 'Total Purchase Amount',\n        color: '#ff6b35'\n      },\n      {\n        icon: 'shopping_cart',\n        value: '271',\n        label: 'Total Orders',\n        color: '#ffa66f'\n      },\n      {\n        icon: 'trending_up',\n        value: '₹1,973',\n        label: 'Average Order Value',\n        color: '#ff8b4d'\n      },\n      {\n        icon: 'store',\n        value: 'Fresh Mart',\n        label: 'Top Vendor',\n        color: '#ff9966'\n      }\n    ];\n  }\n\n  private getCardColor(dataType: string): string {\n    const colorMap: { [key: string]: string } = {\n      'currency': '#ff6b35',\n      'number': '#ffa66f',\n      'percentage': '#ff8b4d',\n      'vendor': '#ff9966'\n    };\n    return colorMap[dataType] || '#ff6b35';\n  }\n\n  private formatDate(date: Date): string {\n    return date.toISOString().split('T')[0];\n  }\n\n  onLocationChange(): void {\n    this.loadDashboardData();\n  }\n\n  onDateChange(): void {\n    this.loadDashboardData();\n  }\n\n  onSearchQuery(): void {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n\n  private loadDashboardDataWithQuery(query: string): void {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success' && response.data.charts.length > 0) {\n            this.processDashboardData(response.data);\n          } else {\n            // Query returned no results, show default data\n            console.log('Query returned no data, loading default dashboard');\n            this.loadDashboardData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: (error) => {\n          console.warn('Error with query, falling back to default dashboard:', error);\n          this.loadDashboardData();\n        }\n      });\n  }\n\n  getChartData(chart: ChartDataModel): ChartData {\n    return chart.data;\n  }\n\n  getChartType(chart: ChartDataModel): ChartType {\n    return chart.type as ChartType;\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Top Gradient Header -->\n  <div class=\"top-header-gradient\">\n    <div class=\"dashboard-selection\">\n      <mat-form-field appearance=\"outline\" class=\"dashboard-dropdown\">\n        <mat-label>Purchase Dashboard</mat-label>\n        <mat-select [(value)]=\"selectedDashboard\">\n          <mat-option value=\"purchase\">Purchase Dashboard</mat-option>\n          <mat-option value=\"sales\">Sales Dashboard</mat-option>\n          <mat-option value=\"inventory\">Inventory Dashboard</mat-option>\n        </mat-select>\n      </mat-form-field>\n    </div>\n  </div>\n\n  <!-- Main Layout -->\n  <div class=\"main-layout\">\n    <!-- Left Sidebar -->\n    <div class=\"left-sidebar\">\n      <!-- Smart Filters Section -->\n      <div class=\"filters-section\">\n        <h3 class=\"filters-title\">\n          <mat-icon>tune</mat-icon>\n          Smart Filters\n          <span class=\"filter-count\">1</span>\n        </h3>\n\n        <!-- Restaurants Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>restaurant</mat-icon>\n            Restaurants\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select restaurants</mat-label>\n            <mat-select [(value)]=\"selectedLocation\" (selectionChange)=\"onLocationChange()\">\n              <mat-option>\n                <ngx-mat-select-search\n                  [formControl]=\"locationFilterCtrl\"\n                  placeholderLabel=\"Search locations...\"\n                  noEntriesFoundLabel=\"No locations found\">\n                </ngx-mat-select-search>\n              </mat-option>\n              <mat-option *ngFor=\"let branch of filteredBranches\" [value]=\"branch.restaurantIdOld\">\n                {{branch.branchName}}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>event</mat-icon>\n            Base Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select base date</mat-label>\n            <mat-select [formControl]=\"baseDateCtrl\" (selectionChange)=\"onDateChange()\">\n              <mat-option value=\"deliveryDate\">Delivery Date</mat-option>\n              <mat-option value=\"orderDate\">Order Date</mat-option>\n              <mat-option value=\"createdDate\">Created Date</mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            Start Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Start Date</mat-label>\n            <input matInput [matDatepicker]=\"startPicker\" [formControl]=\"startDate\" (dateChange)=\"onDateChange()\">\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            End Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>End Date</mat-label>\n            <input matInput [matDatepicker]=\"endPicker\" [formControl]=\"endDate\" (dateChange)=\"onDateChange()\">\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- Reset Filters Button -->\n        <div class=\"filter-actions\">\n          <button mat-stroked-button class=\"reset-filters-btn\" (click)=\"loadDashboardData()\">\n            <mat-icon>refresh</mat-icon>\n            Reset filters\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Right Content Area -->\n    <div class=\"right-content\">\n      <!-- Top Search Bar -->\n      <div class=\"search-header\">\n        <div class=\"assistant-info\">\n          <mat-icon class=\"assistant-icon\">smart_toy</mat-icon>\n          <div class=\"assistant-text\">\n            <span class=\"assistant-title\">Smart Dashboard Assistant</span>\n            <span class=\"assistant-status\">Ready to analyze</span>\n          </div>\n        </div>\n        <div class=\"search-container\">\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <mat-label>Ask me about your business data</mat-label>\n            <input matInput\n                   placeholder=\"Ask me about your business data\"\n                   [formControl]=\"searchQuery\"\n                   (keyup.enter)=\"onSearchQuery()\" />\n            <mat-icon matSuffix class=\"search-icon\" (click)=\"onSearchQuery()\">search</mat-icon>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Dashboard Content Area -->\n      <div class=\"dashboard-content-area\">\n        <!-- Loading Spinner -->\n        <div *ngIf=\"isLoading\" class=\"loading-container\">\n          <mat-spinner diameter=\"50\"></mat-spinner>\n          <p>Loading dashboard data...</p>\n        </div>\n\n        <!-- Dashboard Grid -->\n        <div *ngIf=\"!isLoading\" class=\"dashboard-grid\">\n        <!-- Summary Cards Row -->\n        <div class=\"summary-cards-row\">\n          <mat-card *ngFor=\"let card of summaryCards\" class=\"summary-card\" [style.border-left-color]=\"card.color\">\n            <mat-card-content>\n              <div class=\"card-content\">\n                <div class=\"card-icon\" [style.color]=\"card.color\">\n                  <mat-icon>{{card.icon}}</mat-icon>\n                </div>\n                <div class=\"card-info\">\n                  <div class=\"card-value\">{{card.value}}</div>\n                  <div class=\"card-label\">{{card.label}}</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Charts Grid -->\n        <div class=\"charts-grid\">\n          <!-- Purchase Trends Chart (Full Width) -->\n          <mat-card *ngFor=\"let chart of charts; let i = index\"\n                    class=\"chart-card\"\n                    [ngClass]=\"{\n                      'full-width': chart.type === 'line' || chart.title.toLowerCase().includes('trend'),\n                      'half-width': chart.type !== 'line' && !chart.title.toLowerCase().includes('trend'),\n                      'third-width': chart.type === 'doughnut' || chart.type === 'pie'\n                    }\">\n            <mat-card-header>\n              <mat-card-title class=\"chart-title\">{{chart.title}}</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"chart-container\" [attr.data-chart-type]=\"chart.type\">\n                <!-- Line Chart -->\n                <canvas *ngIf=\"chart.type === 'line'\"\n                        baseChart\n                        [type]=\"'line'\"\n                        [data]=\"getChartData(chart)\"\n                        [options]=\"lineChartOptions\">\n                </canvas>\n\n                <!-- Bar Chart -->\n                <canvas *ngIf=\"chart.type === 'bar'\"\n                        baseChart\n                        [type]=\"'bar'\"\n                        [data]=\"getChartData(chart)\"\n                        [options]=\"barChartOptions\">\n                </canvas>\n\n                <!-- Doughnut Chart -->\n                <canvas *ngIf=\"chart.type === 'doughnut' || chart.type === 'pie'\"\n                        baseChart\n                        [type]=\"'doughnut'\"\n                        [data]=\"getChartData(chart)\"\n                        [options]=\"doughnutChartOptions\">\n                </canvas>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Default Charts if no data -->\n          <ng-container *ngIf=\"charts.length === 0 && !isLoading\">\n            <!-- Purchase Trends Chart -->\n            <mat-card class=\"chart-card full-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Purchase Trends (Last 30 Days)</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>show_chart</mat-icon>\n                    <p>Purchase trends data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Top Vendors Chart -->\n            <mat-card class=\"chart-card half-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Top Vendors by Purchase Amount</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>bar_chart</mat-icon>\n                    <p>Vendor data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Category Spending Chart -->\n            <mat-card class=\"chart-card half-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Category wise Spending Distribution</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>pie_chart</mat-icon>\n                    <p>Category spending data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Top Items by Quantity Chart -->\n            <mat-card class=\"chart-card full-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Top Items by Quantity Purchased</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>inventory</mat-icon>\n                    <p>Top items data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </ng-container>\n        </div>\n\n        <!-- Empty State -->\n        <div *ngIf=\"!isLoading && summaryCards.length === 0\" class=\"empty-state\">\n          <mat-icon class=\"empty-icon\">analytics</mat-icon>\n          <h3>No Data Available</h3>\n          <p>Please select a location and date range to view dashboard data.</p>\n          <button mat-raised-button color=\"primary\" (click)=\"loadDashboardData()\">\n            <mat-icon>refresh</mat-icon>\n            Refresh Data\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;IC4B/DC,EAAA,CAAAC,cAAA,qBAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,eAAA,CAAgC;IAClFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,UAAA,MACF;;;;;IAqFNT,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAU,SAAA,sBAAyC;IACzCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAOhCH,EAAA,CAAAC,cAAA,mBAAwG;IAItFD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEpCH,EAAA,CAAAC,cAAA,cAAuB;IACGD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IARaH,EAAA,CAAAW,WAAA,sBAAAC,QAAA,CAAAC,KAAA,CAAsC;IAG1Eb,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAW,WAAA,UAAAC,QAAA,CAAAC,KAAA,CAA0B;IACrCb,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAc,iBAAA,CAAAF,QAAA,CAAAG,IAAA,CAAa;IAGCf,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAc,iBAAA,CAAAF,QAAA,CAAAI,KAAA,CAAc;IACdhB,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAc,iBAAA,CAAAF,QAAA,CAAAK,KAAA,CAAc;;;;;IAuBxCjB,EAAA,CAAAU,SAAA,iBAKS;;;;;IAHDV,EAAA,CAAAI,UAAA,gBAAe,SAAAc,OAAA,CAAAC,YAAA,CAAAC,SAAA,cAAAF,OAAA,CAAAG,gBAAA;;;;;IAMvBrB,EAAA,CAAAU,SAAA,iBAKS;;;;;IAHDV,EAAA,CAAAI,UAAA,eAAc,SAAAkB,OAAA,CAAAH,YAAA,CAAAC,SAAA,cAAAE,OAAA,CAAAC,eAAA;;;;;IAMtBvB,EAAA,CAAAU,SAAA,iBAKS;;;;;IAHDV,EAAA,CAAAI,UAAA,oBAAmB,SAAAoB,OAAA,CAAAL,YAAA,CAAAC,SAAA,cAAAI,OAAA,CAAAC,oBAAA;;;;;;;;;;;;IA/BjCzB,EAAA,CAAAC,cAAA,mBAMa;IAE2BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEtEH,EAAA,CAAAC,cAAA,uBAAkB;IAGdD,EAAA,CAAA0B,UAAA,IAAAC,2DAAA,qBAKS;IAGT3B,EAAA,CAAA0B,UAAA,IAAAE,2DAAA,qBAKS;IAGT5B,EAAA,CAAA0B,UAAA,IAAAG,2DAAA,qBAKS;IACX7B,EAAA,CAAAG,YAAA,EAAM;;;;IAjCAH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAX,SAAA,CAAAY,IAAA,eAAAZ,SAAA,CAAAa,KAAA,CAAAC,WAAA,GAAAC,QAAA,WAAAf,SAAA,CAAAY,IAAA,gBAAAZ,SAAA,CAAAa,KAAA,CAAAC,WAAA,GAAAC,QAAA,WAAAf,SAAA,CAAAY,IAAA,mBAAAZ,SAAA,CAAAY,IAAA,YAIE;IAE4BhC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAc,iBAAA,CAAAM,SAAA,CAAAa,KAAA,CAAe;IAGtBjC,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAoC,WAAA,oBAAAhB,SAAA,CAAAY,IAAA,CAAmC;IAErDhC,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAgB,SAAA,CAAAY,IAAA,YAA2B;IAQ3BhC,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,SAAAgB,SAAA,CAAAY,IAAA,WAA0B;IAQ1BhC,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAI,UAAA,SAAAgB,SAAA,CAAAY,IAAA,mBAAAZ,SAAA,CAAAY,IAAA,WAAuD;;;;;IAWtEhC,EAAA,CAAAqC,uBAAA,GAAwD;IAEtDrC,EAAA,CAAAC,cAAA,mBAAwC;IAEAD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAErFH,EAAA,CAAAC,cAAA,uBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAOpDH,EAAA,CAAAC,cAAA,oBAAwC;IAEAD,EAAA,CAAAE,MAAA,sCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAErFH,EAAA,CAAAC,cAAA,wBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAO3CH,EAAA,CAAAC,cAAA,oBAAwC;IAEAD,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAE1FH,EAAA,CAAAC,cAAA,wBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,+CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAOtDH,EAAA,CAAAC,cAAA,oBAAwC;IAEAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEtFH,EAAA,CAAAC,cAAA,wBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKhDH,EAAA,CAAAsC,qBAAA,EAAe;;;;;;IAIjBtC,EAAA,CAAAC,cAAA,cAAyE;IAC1CD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sEAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtEH,EAAA,CAAAC,cAAA,iBAAwE;IAA9BD,EAAA,CAAAuC,UAAA,mBAAAC,sEAAA;MAAAxC,EAAA,CAAAyC,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA6C,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACrE9C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IApIXH,EAAA,CAAAC,cAAA,cAA+C;IAG7CD,EAAA,CAAA0B,UAAA,IAAAqB,kDAAA,wBAYW;IACb/C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAyB;IAEvBD,EAAA,CAAA0B,UAAA,IAAAsB,kDAAA,wBAqCW;IAGXhD,EAAA,CAAA0B,UAAA,IAAAuB,sDAAA,4BA4De;IACjBjD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA0B,UAAA,IAAAwB,6CAAA,mBAQM;IACRlD,EAAA,CAAAG,YAAA,EAAM;;;;IAnIyBH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,YAAA+C,MAAA,CAAAC,YAAA,CAAe;IAkBdpD,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAI,UAAA,YAAA+C,MAAA,CAAAE,MAAA,CAAW;IAwCxBrD,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAA+C,MAAA,CAAAE,MAAA,CAAAC,MAAA,WAAAH,MAAA,CAAAI,SAAA,CAAuC;IAgElDvD,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAI,UAAA,UAAA+C,MAAA,CAAAI,SAAA,IAAAJ,MAAA,CAAAC,YAAA,CAAAE,MAAA,OAA6C;;;ADjO3D,MAuBaE,uBAAuB;EA4LlCC,YACUC,qBAA4C,EAC5CC,WAAwB,EACxBC,gBAAkC,EAClCC,GAAsB;IAHtB,KAAAH,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,GAAG,GAAHA,GAAG;IA/LL,KAAAC,QAAQ,GAAG,IAAIlE,OAAO,EAAQ;IAItC,KAAAmE,QAAQ,GAAU,EAAE;IACpB,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,kBAAkB,GAAG,IAAIzE,WAAW,CAAC,EAAE,CAAC;IACxC,KAAA0E,SAAS,GAAG,IAAI1E,WAAW,CAAC,IAAI2E,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9E,KAAAC,OAAO,GAAG,IAAI7E,WAAW,CAAC,IAAI2E,IAAI,EAAE,CAAC;IACrC,KAAAG,WAAW,GAAG,IAAI9E,WAAW,CAAC,EAAE,CAAC;IACjC,KAAA+E,YAAY,GAAG,IAAI/E,WAAW,CAAC,cAAc,CAAC;IAC9C,KAAAgF,iBAAiB,GAAG,UAAU;IAE9B;IACA,KAAArB,YAAY,GAAkB,EAAE;IAChC,KAAAC,MAAM,GAAqB,EAAE;IAC7B,KAAAE,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAlC,gBAAgB,GAAkC;MAChDqD,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE,KAAK;UACfC,MAAM,EAAE;YACNC,aAAa,EAAE,IAAI;YACnBC,OAAO,EAAE;;SAEZ;QACDC,OAAO,EAAE;UACPC,IAAI,EAAE,OAAO;UACbC,SAAS,EAAE,KAAK;UAChBC,eAAe,EAAE,iBAAiB;UAClCC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE;;OAEhB;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDd,OAAO,EAAE,IAAI;UACbe,IAAI,EAAE;YACJf,OAAO,EAAE,IAAI;YACbjE,KAAK,EAAE;WACR;UACDoB,KAAK,EAAE;YACL6C,OAAO,EAAE;;SAEZ;QACDgB,CAAC,EAAE;UACDhB,OAAO,EAAE,IAAI;UACbe,IAAI,EAAE;YACJf,OAAO,EAAE,IAAI;YACbjE,KAAK,EAAE;WACR;UACDoB,KAAK,EAAE;YACL6C,OAAO,EAAE;WACV;UACDiB,WAAW,EAAE,IAAI;UACjBC,KAAK,EAAE;YACLC,QAAQ,EAAE,SAAAA,CAASjF,KAAU;cAC3B,OAAO,GAAG,GAAGA,KAAK,CAACkF,cAAc,EAAE;YACrC;;;OAGL;MACDC,WAAW,EAAE;QACXf,IAAI,EAAE,SAAS;QACfgB,IAAI,EAAE,GAAG;QACTf,SAAS,EAAE;;KAEd;IAED,KAAA9D,eAAe,GAAkC;MAC/CmD,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1B0B,SAAS,EAAE,GAAY;MACvBzB,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;SACV;QACDK,OAAO,EAAE;UACPG,eAAe,EAAE,iBAAiB;UAClCC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE,CAAC;UACdY,SAAS,EAAE;YACTrF,KAAK,EAAE,SAAAA,CAASsF,OAAY;cAC1B,OAAO,GAAG,GAAGA,OAAO,CAACC,MAAM,CAACZ,CAAC,CAACM,cAAc,EAAE;YAChD;;;OAGL;MACDP,MAAM,EAAE;QACNC,CAAC,EAAE;UACDG,WAAW,EAAE,IAAI;UACjBF,IAAI,EAAE;YACJf,OAAO,EAAE,IAAI;YACbjE,KAAK,EAAE;WACR;UACDoB,KAAK,EAAE;YACL6C,OAAO,EAAE;WACV;UACDkB,KAAK,EAAE;YACLC,QAAQ,EAAE,SAAAA,CAASjF,KAAU;cAC3B,OAAO,GAAG,GAAGA,KAAK,CAACkF,cAAc,EAAE;YACrC;;SAEH;QACDJ,CAAC,EAAE;UACDD,IAAI,EAAE;YACJf,OAAO,EAAE;WACV;UACD7C,KAAK,EAAE;YACL6C,OAAO,EAAE;;;;KAIhB;IAED,KAAArD,oBAAoB,GAAQ;MAC1BiD,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;YACNC,aAAa,EAAE,IAAI;YACnBC,OAAO,EAAE,EAAE;YACXuB,IAAI,EAAE;cACJC,IAAI,EAAE;;;SAGX;QACDvB,OAAO,EAAE;UACPG,eAAe,EAAE,iBAAiB;UAClCC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE,CAAC;UACdY,SAAS,EAAE;YACTrF,KAAK,EAAE,SAAAA,CAASsF,OAAY;cAC1B,MAAMtF,KAAK,GAAGsF,OAAO,CAACtF,KAAK,IAAI,EAAE;cACjC,MAAMD,KAAK,GAAGuF,OAAO,CAACC,MAAM;cAC5B,MAAMG,KAAK,GAAGJ,OAAO,CAACK,OAAO,CAACC,IAAI,CAACC,MAAM,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;cAC7E,MAAMC,UAAU,GAAG,CAAEjG,KAAK,GAAG2F,KAAK,GAAI,GAAG,EAAEO,OAAO,CAAC,CAAC,CAAC;cACrD,OAAO,GAAGjG,KAAK,KAAKgG,UAAU,GAAG;YACnC;;;OAGL;MACDE,MAAM,EAAE;KACT;IAED,KAAAC,uBAAuB,GAAkC;MACvD1C,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDa,MAAM,EAAE;QACNC,CAAC,EAAE;UACD3D,KAAK,EAAE;YACL6C,OAAO,EAAE,IAAI;YACbuC,IAAI,EAAE;;SAET;QACDvB,CAAC,EAAE;UACDC,WAAW,EAAE,IAAI;UACjB9D,KAAK,EAAE;YACL6C,OAAO,EAAE,IAAI;YACbuC,IAAI,EAAE;;;;KAIb;IAQC,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC3D,WAAW,CAAC4D,cAAc,EAAE;EAC/C;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAAC5E,iBAAiB,EAAE;EAC1B;EAEA6E,WAAWA,CAAA;IACT,IAAI,CAAC7D,QAAQ,CAAC8D,IAAI,EAAE;IACpB,IAAI,CAAC9D,QAAQ,CAAC+D,QAAQ,EAAE;EAC1B;EAEQJ,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACvD,kBAAkB,CAAC4D,YAAY,CACjCC,IAAI,CACHjI,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CACzB,CACAkE,SAAS,CAAEhH,KAAoB,IAAI;MAClC,IAAI,CAACiH,cAAc,CAACjH,KAAK,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC;EACN;EAEQ0G,YAAYA,CAAA;IAClB,IAAI,CAAC9D,gBAAgB,CAACsE,sBAAsB,CACzCH,IAAI,CAAClI,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAC9BkE,SAAS,CAACnB,IAAI,IAAG;MAChB,IAAI,CAAC9C,QAAQ,GAAG8C,IAAI,IAAI,EAAE;MAC1B,IAAI,CAAC7C,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;MAE1C,IAAI,IAAI,CAACA,QAAQ,CAACT,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACW,gBAAgB,GAAG,IAAI,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACzD,eAAe;;IAE5D,CAAC,CAAC;EACN;EAEQ2H,cAAcA,CAACE,UAAkB;IACvC,IAAI,CAACA,UAAU,EAAE;MACf,IAAI,CAACnE,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;KAC3C,MAAM;MACL,MAAMqE,oBAAoB,GAAGD,UAAU,CAACjG,WAAW,EAAE,CAACmG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxE,IAAI,CAACrE,gBAAgB,GAAG,IAAI,CAACD,QAAQ,CAACuE,MAAM,CAACC,MAAM,IACjDA,MAAM,CAAC9H,UAAU,CAACyB,WAAW,EAAE,CAACmG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAClG,QAAQ,CAACiG,oBAAoB,CAAC,CAClF;;EAEL;EAEAtF,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACqB,SAAS,CAACnD,KAAK,IAAI,CAAC,IAAI,CAACsD,OAAO,CAACtD,KAAK,EAAE;MAChD;;IAGF,IAAI,CAACuC,SAAS,GAAG,IAAI;IAErB,MAAMiF,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACxE,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB,CAAC,GAAG,EAAE;MAC/DE,SAAS,EAAE,IAAI,CAACuE,UAAU,CAAC,IAAI,CAACvE,SAAS,CAACnD,KAAK,CAAC;MAChDsD,OAAO,EAAE,IAAI,CAACoE,UAAU,CAAC,IAAI,CAACpE,OAAO,CAACtD,KAAK,CAAC;MAC5C2H,QAAQ,EAAE,IAAI,CAACnE,YAAY,CAACxD,KAAK,IAAI;KACtC;IAED,MAAM4H,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACvB,IAAI,CAACwB,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE;KACrB;IAED,IAAI,CAACtF,qBAAqB,CAACuF,qBAAqB,CAACL,OAAO,CAAC,CACtDb,IAAI,CAAClI,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAC9BkE,SAAS,CAAC;MACTJ,IAAI,EAAGsB,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACC,oBAAoB,CAACF,QAAQ,CAACrC,IAAI,CAAC;SACzC,MAAM;UACL;UACA,IAAI,CAACwC,cAAc,EAAE;;QAEvB,IAAI,CAAC9F,SAAS,GAAG,KAAK;QACtB,IAAI,CAACM,GAAG,CAACyF,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD;QACA,IAAI,CAACF,cAAc,EAAE;QACrB,IAAI,CAAC9F,SAAS,GAAG,KAAK;QACtB,IAAI,CAACM,GAAG,CAACyF,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQD,cAAcA,CAAA;IACpB;IACA,MAAMI,UAAU,GAAG;MACjBC,aAAa,EAAE,CACb;QAAE3I,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE,uBAAuB;QAAE0I,SAAS,EAAE;MAAU,CAAE,EACjG;QAAE5I,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,cAAc;QAAE0I,SAAS,EAAE;MAAQ,CAAE,EACnF;QAAE5I,IAAI,EAAE,aAAa;QAAEC,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE,qBAAqB;QAAE0I,SAAS,EAAE;MAAU,CAAE,EAC7F;QAAE5I,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,YAAY;QAAEC,KAAK,EAAE,YAAY;QAAE0I,SAAS,EAAE;MAAQ,CAAE,CACjF;MACDtG,MAAM,EAAE,CACN;QACEuG,EAAE,EAAE,iBAAiB;QACrB3H,KAAK,EAAE,gCAAgC;QACvCD,IAAI,EAAE,MAAM;QACZ6E,IAAI,EAAE;UACJ7B,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAChD6E,QAAQ,EAAE,CAAC;YACT5I,KAAK,EAAE,uBAAuB;YAC9B4F,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YAClCvB,eAAe,EAAE,CAAC,SAAS,CAAC;YAC5BG,WAAW,EAAE,CAAC,SAAS;WACxB;;OAEJ,EACD;QACEmE,EAAE,EAAE,aAAa;QACjB3H,KAAK,EAAE,gCAAgC;QACvCD,IAAI,EAAE,KAAK;QACX6E,IAAI,EAAE;UACJ7B,MAAM,EAAE,CAAC,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,eAAe,CAAC;UACxF6E,QAAQ,EAAE,CAAC;YACT5I,KAAK,EAAE,uBAAuB;YAC9B4F,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;YAC9CvB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxEG,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;WACpE;;OAEJ,EACD;QACEmE,EAAE,EAAE,mBAAmB;QACvB3H,KAAK,EAAE,qCAAqC;QAC5CD,IAAI,EAAE,UAAU;QAChB6E,IAAI,EAAE;UACJ7B,MAAM,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC;UACjE6E,QAAQ,EAAE,CAAC;YACT5I,KAAK,EAAE,uBAAuB;YAC9B4F,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAC1BvB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxEG,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;WACpE;;OAEJ;KAEJ;IAED,IAAI,CAAC2D,oBAAoB,CAACK,UAAU,CAAC;EACvC;EAEQL,oBAAoBA,CAACvC,IAAS;IACpC;IACA,IAAI,CAACzD,YAAY,GAAGyD,IAAI,CAAC6C,aAAa,EAAEI,GAAG,CAAEC,IAAS,KAAM;MAC1DhJ,IAAI,EAAE,IAAI,CAAC2C,qBAAqB,CAACsG,kBAAkB,CAACD,IAAI,CAACJ,SAAS,EAAEI,IAAI,CAAC9I,KAAK,CAAC;MAC/ED,KAAK,EAAE+I,IAAI,CAAC/I,KAAK;MACjBC,KAAK,EAAE8I,IAAI,CAAC9I,KAAK;MACjBJ,KAAK,EAAE,IAAI,CAACoJ,YAAY,CAACF,IAAI,CAACJ,SAAS;KACxC,CAAC,CAAC,IAAI,EAAE;IAET;IACA,IAAI,IAAI,CAACvG,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACF,YAAY,GAAG,IAAI,CAAC8G,yBAAyB,EAAE;;IAGtD;IACA,IAAI,CAAC7G,MAAM,GAAGwD,IAAI,CAACxD,MAAM,EAAEyG,GAAG,CAAEK,KAAU,KAAM;MAC9C,GAAGA,KAAK;MACRtD,IAAI,EAAE,IAAI,CAACnD,qBAAqB,CAAC0G,gBAAgB,CAACD,KAAK;KACxD,CAAC,CAAC,IAAI,EAAE;IAET;IACA,IAAI,IAAI,CAAC9G,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5BkG,OAAO,CAACa,GAAG,CAAC,kCAAkC,CAAC;;EAEnD;EAEQH,yBAAyBA,CAAA;IAC/B,OAAO,CACL;MACEnJ,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,uBAAuB;MAC9BJ,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,cAAc;MACrBJ,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,QAAQ;MACfC,KAAK,EAAE,qBAAqB;MAC5BJ,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,YAAY;MACnBJ,KAAK,EAAE;KACR,CACF;EACH;EAEQoJ,YAAYA,CAACK,QAAgB;IACnC,MAAMC,QAAQ,GAA8B;MAC1C,UAAU,EAAE,SAAS;MACrB,QAAQ,EAAE,SAAS;MACnB,YAAY,EAAE,SAAS;MACvB,QAAQ,EAAE;KACX;IACD,OAAOA,QAAQ,CAACD,QAAQ,CAAC,IAAI,SAAS;EACxC;EAEQ5B,UAAUA,CAAC8B,IAAU;IAC3B,OAAOA,IAAI,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAC7H,iBAAiB,EAAE;EAC1B;EAEA8H,YAAYA,CAAA;IACV,IAAI,CAAC9H,iBAAiB,EAAE;EAC1B;EAEA+H,aAAaA,CAAA;IACX,MAAMC,KAAK,GAAG,IAAI,CAACvG,WAAW,CAACvD,KAAK,EAAE+J,IAAI,EAAE;IAC5C,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,0BAA0B,CAACF,KAAK,CAAC;KACvC,MAAM;MACL,IAAI,CAAChI,iBAAiB,EAAE;;EAE5B;EAEQkI,0BAA0BA,CAACF,KAAa;IAC9C,IAAI,CAAC,IAAI,CAAC3G,SAAS,CAACnD,KAAK,IAAI,CAAC,IAAI,CAACsD,OAAO,CAACtD,KAAK,EAAE;MAChD;;IAGF,IAAI,CAACuC,SAAS,GAAG,IAAI;IAErB,MAAMiF,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACxE,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB,CAAC,GAAG,EAAE;MAC/DE,SAAS,EAAE,IAAI,CAACuE,UAAU,CAAC,IAAI,CAACvE,SAAS,CAACnD,KAAK,CAAC;MAChDsD,OAAO,EAAE,IAAI,CAACoE,UAAU,CAAC,IAAI,CAACpE,OAAO,CAACtD,KAAK,CAAC;MAC5C2H,QAAQ,EAAE,IAAI,CAACnE,YAAY,CAACxD,KAAK,IAAI;KACtC;IAED,MAAM4H,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACvB,IAAI,CAACwB,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAE+B,KAAK;MACjB9B,kBAAkB,EAAE;KACrB;IAED,IAAI,CAACtF,qBAAqB,CAACuF,qBAAqB,CAACL,OAAO,CAAC,CACtDb,IAAI,CAAClI,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAC9BkE,SAAS,CAAC;MACTJ,IAAI,EAAGsB,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,IAAID,QAAQ,CAACrC,IAAI,CAACxD,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;UACpE,IAAI,CAAC8F,oBAAoB,CAACF,QAAQ,CAACrC,IAAI,CAAC;SACzC,MAAM;UACL;UACA2C,OAAO,CAACa,GAAG,CAAC,mDAAmD,CAAC;UAChE,IAAI,CAACvH,iBAAiB,EAAE;;QAE1B,IAAI,CAACS,SAAS,GAAG,KAAK;QACtB,IAAI,CAACM,GAAG,CAACyF,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACyB,IAAI,CAAC,sDAAsD,EAAE1B,KAAK,CAAC;QAC3E,IAAI,CAACzG,iBAAiB,EAAE;MAC1B;KACD,CAAC;EACN;EAEA3B,YAAYA,CAACgJ,KAAqB;IAChC,OAAOA,KAAK,CAACtD,IAAI;EACnB;EAEAqE,YAAYA,CAACf,KAAqB;IAChC,OAAOA,KAAK,CAACnI,IAAiB;EAChC;;;uBAjeWwB,uBAAuB,EAAAxD,EAAA,CAAAmL,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAArL,EAAA,CAAAmL,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAvL,EAAA,CAAAmL,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAzL,EAAA,CAAAmL,iBAAA,CAAAnL,EAAA,CAAA0L,iBAAA;IAAA;EAAA;;;YAAvBlI,uBAAuB;MAAAmI,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7L,EAAA,CAAA8L,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3DpCpM,EAAA,CAAAC,cAAA,aAAuC;UAKpBD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAC,cAAA,oBAA0C;UAA9BD,EAAA,CAAAuC,UAAA,yBAAA+J,mEAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA5H,iBAAA,GAAA8H,MAAA;UAAA,EAA6B;UACvCvM,EAAA,CAAAC,cAAA,oBAA6B;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC5DH,EAAA,CAAAC,cAAA,oBAA0B;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtDH,EAAA,CAAAC,cAAA,qBAA8B;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAOtEH,EAAA,CAAAC,cAAA,cAAyB;UAMPD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,uBACA;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIrCH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAC,cAAA,sBAAgF;UAApED,EAAA,CAAAuC,UAAA,yBAAAiK,oEAAAD,MAAA;YAAA,OAAAF,GAAA,CAAApI,gBAAA,GAAAsI,MAAA;UAAA,EAA4B,6BAAAE,wEAAA;YAAA,OAAoBJ,GAAA,CAAA1B,gBAAA,EAAkB;UAAA,EAAtC;UACtC3K,EAAA,CAAAC,cAAA,kBAAY;UACVD,EAAA,CAAAU,SAAA,iCAIwB;UAC1BV,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAA0B,UAAA,KAAAgL,8CAAA,yBAEa;UACf1M,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,sBAA4E;UAAnCD,EAAA,CAAAuC,UAAA,6BAAAoK,wEAAA;YAAA,OAAmBN,GAAA,CAAAzB,YAAA,EAAc;UAAA,EAAC;UACzE5K,EAAA,CAAAC,cAAA,sBAAiC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC3DH,EAAA,CAAAC,cAAA,sBAA8B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACrDH,EAAA,CAAAC,cAAA,sBAAgC;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAM/DH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,iBAAsG;UAA9BD,EAAA,CAAAuC,UAAA,wBAAAqK,8DAAA;YAAA,OAAcP,GAAA,CAAAzB,YAAA,EAAc;UAAA,EAAC;UAArG5K,EAAA,CAAAG,YAAA,EAAsG;UACtGH,EAAA,CAAAU,SAAA,iCAA6E;UAE/EV,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAC,cAAA,iBAAkG;UAA9BD,EAAA,CAAAuC,UAAA,wBAAAsK,8DAAA;YAAA,OAAcR,GAAA,CAAAzB,YAAA,EAAc;UAAA,EAAC;UAAjG5K,EAAA,CAAAG,YAAA,EAAkG;UAClGH,EAAA,CAAAU,SAAA,iCAA2E;UAE7EV,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA4B;UAC2BD,EAAA,CAAAuC,UAAA,mBAAAuK,0DAAA;YAAA,OAAST,GAAA,CAAAvJ,iBAAA,EAAmB;UAAA,EAAC;UAChF9C,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA2B;UAIYD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,eAA4B;UACID,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAA+B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG1DH,EAAA,CAAAC,cAAA,eAA8B;UAEfD,EAAA,CAAAE,MAAA,uCAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtDH,EAAA,CAAAC,cAAA,iBAGyC;UAAlCD,EAAA,CAAAuC,UAAA,yBAAAwK,+DAAA;YAAA,OAAeV,GAAA,CAAAxB,aAAA,EAAe;UAAA,EAAC;UAHtC7K,EAAA,CAAAG,YAAA,EAGyC;UACzCH,EAAA,CAAAC,cAAA,oBAAkE;UAA1BD,EAAA,CAAAuC,UAAA,mBAAAyK,4DAAA;YAAA,OAASX,GAAA,CAAAxB,aAAA,EAAe;UAAA,EAAC;UAAC7K,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAMzFH,EAAA,CAAAC,cAAA,eAAoC;UAElCD,EAAA,CAAA0B,UAAA,KAAAuL,uCAAA,kBAGM;UAGNjN,EAAA,CAAA0B,UAAA,KAAAwL,uCAAA,kBAsII;UACRlN,EAAA,CAAAG,YAAA,EAAM;;;;;UAzQUH,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,UAAAiM,GAAA,CAAA5H,iBAAA,CAA6B;UA6BzBzE,EAAA,CAAAO,SAAA,IAA4B;UAA5BP,EAAA,CAAAI,UAAA,UAAAiM,GAAA,CAAApI,gBAAA,CAA4B;UAGlCjE,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,gBAAAiM,GAAA,CAAAnI,kBAAA,CAAkC;UAKPlE,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAAiM,GAAA,CAAArI,gBAAA,CAAmB;UAexChE,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,gBAAAiM,GAAA,CAAA7H,YAAA,CAA4B;UAgBxBxE,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAI,UAAA,kBAAA+M,GAAA,CAA6B,gBAAAd,GAAA,CAAAlI,SAAA;UACZnE,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,QAAA+M,GAAA,CAAmB;UAapCnN,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,kBAAAgN,GAAA,CAA2B,gBAAAf,GAAA,CAAA/H,OAAA;UACVtE,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,QAAAgN,GAAA,CAAiB;UA+B3CpN,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,gBAAAiM,GAAA,CAAA9H,WAAA,CAA2B;UAUhCvE,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAiM,GAAA,CAAA9I,SAAA,CAAe;UAMfvD,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,UAAAiM,GAAA,CAAA9I,SAAA,CAAgB;;;qBDhG1B3E,YAAY,EAAAyO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ3O,aAAa,EAAA4O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACb/O,eAAe,EAAAgP,EAAA,CAAAC,SAAA,EACfhP,aAAa,EAAAiP,EAAA,CAAAC,OAAA,EACbjP,eAAe,EAAAkP,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,SAAA,EAAAC,EAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACfxP,kBAAkB,EAClBC,cAAc,EAAAwP,GAAA,CAAAC,QAAA,EACdxP,mBAAmB,EAAAyP,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB3P,mBAAmB,EACnBC,gBAAgB,EAChBC,wBAAwB,EAAA0P,GAAA,CAAAC,kBAAA,EACxB1P,cAAc,EAAA2P,GAAA,CAAAC,kBAAA,EACd3P,wBAAwB,EAAA4P,GAAA,CAAAC,wBAAA,EACxB3P,mBAAmB,EAAA4P,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,oBAAA,EACnB9P,WAAW;MAAA+P,MAAA;IAAA;EAAA;;SAKFlM,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}