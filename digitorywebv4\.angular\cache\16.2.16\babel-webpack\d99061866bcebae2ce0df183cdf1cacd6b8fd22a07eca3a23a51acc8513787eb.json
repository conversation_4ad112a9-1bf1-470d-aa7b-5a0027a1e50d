{"ast": null, "code": "/** internal\n * class Core\n *\n * Top-level rules executor. Glues block/inline parsers and does intermediate\n * transformations.\n **/\n'use strict';\n\nvar Ruler = require('./ruler');\nvar _rules = [['normalize', require('./rules_core/normalize')], ['block', require('./rules_core/block')], ['inline', require('./rules_core/inline')], ['linkify', require('./rules_core/linkify')], ['replacements', require('./rules_core/replacements')], ['smartquotes', require('./rules_core/smartquotes')],\n// `text_join` finds `text_special` tokens (for escape sequences)\n// and joins them with the rest of the text\n['text_join', require('./rules_core/text_join')]];\n\n/**\n * new Core()\n **/\nfunction Core() {\n  /**\n   * Core#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of core rules.\n   **/\n  this.ruler = new Ruler();\n  for (var i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1]);\n  }\n}\n\n/**\n * Core.process(state)\n *\n * Executes core chain rules.\n **/\nCore.prototype.process = function (state) {\n  var i, l, rules;\n  rules = this.ruler.getRules('');\n  for (i = 0, l = rules.length; i < l; i++) {\n    rules[i](state);\n  }\n};\nCore.prototype.State = require('./rules_core/state_core');\nmodule.exports = Core;", "map": {"version": 3, "names": ["Ruler", "require", "_rules", "Core", "ruler", "i", "length", "push", "prototype", "process", "state", "l", "rules", "getRules", "State", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/parser_core.js"], "sourcesContent": ["/** internal\n * class Core\n *\n * Top-level rules executor. Glues block/inline parsers and does intermediate\n * transformations.\n **/\n'use strict';\n\n\nvar Ruler  = require('./ruler');\n\n\nvar _rules = [\n  [ 'normalize',      require('./rules_core/normalize')      ],\n  [ 'block',          require('./rules_core/block')          ],\n  [ 'inline',         require('./rules_core/inline')         ],\n  [ 'linkify',        require('./rules_core/linkify')        ],\n  [ 'replacements',   require('./rules_core/replacements')   ],\n  [ 'smartquotes',    require('./rules_core/smartquotes')    ],\n  // `text_join` finds `text_special` tokens (for escape sequences)\n  // and joins them with the rest of the text\n  [ 'text_join',      require('./rules_core/text_join')      ]\n];\n\n\n/**\n * new Core()\n **/\nfunction Core() {\n  /**\n   * Core#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of core rules.\n   **/\n  this.ruler = new Ruler();\n\n  for (var i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1]);\n  }\n}\n\n\n/**\n * Core.process(state)\n *\n * Executes core chain rules.\n **/\nCore.prototype.process = function (state) {\n  var i, l, rules;\n\n  rules = this.ruler.getRules('');\n\n  for (i = 0, l = rules.length; i < l; i++) {\n    rules[i](state);\n  }\n};\n\nCore.prototype.State = require('./rules_core/state_core');\n\n\nmodule.exports = Core;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAGZ,IAAIA,KAAK,GAAIC,OAAO,CAAC,SAAS,CAAC;AAG/B,IAAIC,MAAM,GAAG,CACX,CAAE,WAAW,EAAOD,OAAO,CAAC,wBAAwB,CAAC,CAAO,EAC5D,CAAE,OAAO,EAAWA,OAAO,CAAC,oBAAoB,CAAC,CAAW,EAC5D,CAAE,QAAQ,EAAUA,OAAO,CAAC,qBAAqB,CAAC,CAAU,EAC5D,CAAE,SAAS,EAASA,OAAO,CAAC,sBAAsB,CAAC,CAAS,EAC5D,CAAE,cAAc,EAAIA,OAAO,CAAC,2BAA2B,CAAC,CAAI,EAC5D,CAAE,aAAa,EAAKA,OAAO,CAAC,0BAA0B,CAAC,CAAK;AAC5D;AACA;AACA,CAAE,WAAW,EAAOA,OAAO,CAAC,wBAAwB,CAAC,CAAO,CAC7D;;AAGD;AACA;AACA;AACA,SAASE,IAAIA,CAAA,EAAG;EACd;AACF;AACA;AACA;AACA;EACE,IAAI,CAACC,KAAK,GAAG,IAAIJ,KAAK,CAAC,CAAC;EAExB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAI,CAACD,KAAK,CAACG,IAAI,CAACL,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEH,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C;AACF;;AAGA;AACA;AACA;AACA;AACA;AACAF,IAAI,CAACK,SAAS,CAACC,OAAO,GAAG,UAAUC,KAAK,EAAE;EACxC,IAAIL,CAAC,EAAEM,CAAC,EAAEC,KAAK;EAEfA,KAAK,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAAC,EAAE,CAAC;EAE/B,KAAKR,CAAC,GAAG,CAAC,EAAEM,CAAC,GAAGC,KAAK,CAACN,MAAM,EAAED,CAAC,GAAGM,CAAC,EAAEN,CAAC,EAAE,EAAE;IACxCO,KAAK,CAACP,CAAC,CAAC,CAACK,KAAK,CAAC;EACjB;AACF,CAAC;AAEDP,IAAI,CAACK,SAAS,CAACM,KAAK,GAAGb,OAAO,CAAC,yBAAyB,CAAC;AAGzDc,MAAM,CAACC,OAAO,GAAGb,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}