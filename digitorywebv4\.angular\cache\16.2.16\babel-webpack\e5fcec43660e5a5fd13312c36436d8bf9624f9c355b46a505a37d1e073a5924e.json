{"ast": null, "code": "// Process html entity - &#123;, &#xAF;, &quot;, ...\n\n'use strict';\n\nvar entities = require('../common/entities');\nvar has = require('../common/utils').has;\nvar isValidEntityCode = require('../common/utils').isValidEntityCode;\nvar fromCodePoint = require('../common/utils').fromCodePoint;\nvar DIGITAL_RE = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i;\nvar NAMED_RE = /^&([a-z][a-z0-9]{1,31});/i;\nmodule.exports = function entity(state, silent) {\n  var ch,\n    code,\n    match,\n    token,\n    pos = state.pos,\n    max = state.posMax;\n  if (state.src.charCodeAt(pos) !== 0x26 /* & */) return false;\n  if (pos + 1 >= max) return false;\n  ch = state.src.charCodeAt(pos + 1);\n  if (ch === 0x23 /* # */) {\n    match = state.src.slice(pos).match(DIGITAL_RE);\n    if (match) {\n      if (!silent) {\n        code = match[1][0].toLowerCase() === 'x' ? parseInt(match[1].slice(1), 16) : parseInt(match[1], 10);\n        token = state.push('text_special', '', 0);\n        token.content = isValidEntityCode(code) ? fromCodePoint(code) : fromCodePoint(0xFFFD);\n        token.markup = match[0];\n        token.info = 'entity';\n      }\n      state.pos += match[0].length;\n      return true;\n    }\n  } else {\n    match = state.src.slice(pos).match(NAMED_RE);\n    if (match) {\n      if (has(entities, match[1])) {\n        if (!silent) {\n          token = state.push('text_special', '', 0);\n          token.content = entities[match[1]];\n          token.markup = match[0];\n          token.info = 'entity';\n        }\n        state.pos += match[0].length;\n        return true;\n      }\n    }\n  }\n  return false;\n};", "map": {"version": 3, "names": ["entities", "require", "has", "isValidEntityCode", "fromCodePoint", "DIGITAL_RE", "NAMED_RE", "module", "exports", "entity", "state", "silent", "ch", "code", "match", "token", "pos", "max", "posMax", "src", "charCodeAt", "slice", "toLowerCase", "parseInt", "push", "content", "markup", "info", "length"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_inline/entity.js"], "sourcesContent": ["// Process html entity - &#123;, &#xAF;, &quot;, ...\n\n'use strict';\n\nvar entities          = require('../common/entities');\nvar has               = require('../common/utils').has;\nvar isValidEntityCode = require('../common/utils').isValidEntityCode;\nvar fromCodePoint     = require('../common/utils').fromCodePoint;\n\n\nvar DIGITAL_RE = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i;\nvar NAMED_RE   = /^&([a-z][a-z0-9]{1,31});/i;\n\n\nmodule.exports = function entity(state, silent) {\n  var ch, code, match, token, pos = state.pos, max = state.posMax;\n\n  if (state.src.charCodeAt(pos) !== 0x26/* & */) return false;\n\n  if (pos + 1 >= max) return false;\n\n  ch = state.src.charCodeAt(pos + 1);\n\n  if (ch === 0x23 /* # */) {\n    match = state.src.slice(pos).match(DIGITAL_RE);\n    if (match) {\n      if (!silent) {\n        code = match[1][0].toLowerCase() === 'x' ? parseInt(match[1].slice(1), 16) : parseInt(match[1], 10);\n\n        token         = state.push('text_special', '', 0);\n        token.content = isValidEntityCode(code) ? fromCodePoint(code) : fromCodePoint(0xFFFD);\n        token.markup  = match[0];\n        token.info    = 'entity';\n      }\n      state.pos += match[0].length;\n      return true;\n    }\n  } else {\n    match = state.src.slice(pos).match(NAMED_RE);\n    if (match) {\n      if (has(entities, match[1])) {\n        if (!silent) {\n          token         = state.push('text_special', '', 0);\n          token.content = entities[match[1]];\n          token.markup  = match[0];\n          token.info    = 'entity';\n        }\n        state.pos += match[0].length;\n        return true;\n      }\n    }\n  }\n\n  return false;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,QAAQ,GAAYC,OAAO,CAAC,oBAAoB,CAAC;AACrD,IAAIC,GAAG,GAAiBD,OAAO,CAAC,iBAAiB,CAAC,CAACC,GAAG;AACtD,IAAIC,iBAAiB,GAAGF,OAAO,CAAC,iBAAiB,CAAC,CAACE,iBAAiB;AACpE,IAAIC,aAAa,GAAOH,OAAO,CAAC,iBAAiB,CAAC,CAACG,aAAa;AAGhE,IAAIC,UAAU,GAAG,sCAAsC;AACvD,IAAIC,QAAQ,GAAK,2BAA2B;AAG5CC,MAAM,CAACC,OAAO,GAAG,SAASC,MAAMA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC9C,IAAIC,EAAE;IAAEC,IAAI;IAAEC,KAAK;IAAEC,KAAK;IAAEC,GAAG,GAAGN,KAAK,CAACM,GAAG;IAAEC,GAAG,GAAGP,KAAK,CAACQ,MAAM;EAE/D,IAAIR,KAAK,CAACS,GAAG,CAACC,UAAU,CAACJ,GAAG,CAAC,KAAK,IAAI,UAAS,OAAO,KAAK;EAE3D,IAAIA,GAAG,GAAG,CAAC,IAAIC,GAAG,EAAE,OAAO,KAAK;EAEhCL,EAAE,GAAGF,KAAK,CAACS,GAAG,CAACC,UAAU,CAACJ,GAAG,GAAG,CAAC,CAAC;EAElC,IAAIJ,EAAE,KAAK,IAAI,CAAC,SAAS;IACvBE,KAAK,GAAGJ,KAAK,CAACS,GAAG,CAACE,KAAK,CAACL,GAAG,CAAC,CAACF,KAAK,CAACT,UAAU,CAAC;IAC9C,IAAIS,KAAK,EAAE;MACT,IAAI,CAACH,MAAM,EAAE;QACXE,IAAI,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC,KAAK,GAAG,GAAGC,QAAQ,CAACT,KAAK,CAAC,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGE,QAAQ,CAACT,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAEnGC,KAAK,GAAWL,KAAK,CAACc,IAAI,CAAC,cAAc,EAAE,EAAE,EAAE,CAAC,CAAC;QACjDT,KAAK,CAACU,OAAO,GAAGtB,iBAAiB,CAACU,IAAI,CAAC,GAAGT,aAAa,CAACS,IAAI,CAAC,GAAGT,aAAa,CAAC,MAAM,CAAC;QACrFW,KAAK,CAACW,MAAM,GAAIZ,KAAK,CAAC,CAAC,CAAC;QACxBC,KAAK,CAACY,IAAI,GAAM,QAAQ;MAC1B;MACAjB,KAAK,CAACM,GAAG,IAAIF,KAAK,CAAC,CAAC,CAAC,CAACc,MAAM;MAC5B,OAAO,IAAI;IACb;EACF,CAAC,MAAM;IACLd,KAAK,GAAGJ,KAAK,CAACS,GAAG,CAACE,KAAK,CAACL,GAAG,CAAC,CAACF,KAAK,CAACR,QAAQ,CAAC;IAC5C,IAAIQ,KAAK,EAAE;MACT,IAAIZ,GAAG,CAACF,QAAQ,EAAEc,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,IAAI,CAACH,MAAM,EAAE;UACXI,KAAK,GAAWL,KAAK,CAACc,IAAI,CAAC,cAAc,EAAE,EAAE,EAAE,CAAC,CAAC;UACjDT,KAAK,CAACU,OAAO,GAAGzB,QAAQ,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;UAClCC,KAAK,CAACW,MAAM,GAAIZ,KAAK,CAAC,CAAC,CAAC;UACxBC,KAAK,CAACY,IAAI,GAAM,QAAQ;QAC1B;QACAjB,KAAK,CAACM,GAAG,IAAIF,KAAK,CAAC,CAAC,CAAC,CAACc,MAAM;QAC5B,OAAO,IAAI;MACb;IACF;EACF;EAEA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}