{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/smart-dashboard.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"../../services/share-data.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/datepicker\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"ng2-charts\";\nimport * as i15 from \"ngx-mat-select-search\";\nimport * as i16 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const branch_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", branch_r6.restaurantIdOld);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", branch_r6.branchName, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"mat-spinner\", 48);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SmartDashboardComponent_div_99_mat_card_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 55)(1, \"mat-card-content\")(2, \"div\", 56)(3, \"div\", 57)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 58)(7, \"div\", 59);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 60);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const card_r10 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", card_r10.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", card_r10.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r10.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(card_r10.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r10.label);\n  }\n}\nfunction SmartDashboardComponent_div_99_mat_card_4_canvas_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 65);\n  }\n  if (rf & 2) {\n    const chart_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"type\", \"line\")(\"data\", ctx_r13.getChartData(chart_r11))(\"options\", ctx_r13.lineChartOptions);\n  }\n}\nfunction SmartDashboardComponent_div_99_mat_card_4_canvas_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 65);\n  }\n  if (rf & 2) {\n    const chart_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"type\", \"bar\")(\"data\", ctx_r14.getChartData(chart_r11))(\"options\", ctx_r14.barChartOptions);\n  }\n}\nfunction SmartDashboardComponent_div_99_mat_card_4_canvas_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 65);\n  }\n  if (rf & 2) {\n    const chart_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"type\", \"doughnut\")(\"data\", ctx_r15.getChartData(chart_r11))(\"options\", ctx_r15.doughnutChartOptions);\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"full-width\": a0,\n    \"half-width\": a1,\n    \"third-width\": a2\n  };\n};\nfunction SmartDashboardComponent_div_99_mat_card_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 61)(1, \"mat-card-header\")(2, \"mat-card-title\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 63);\n    i0.ɵɵtemplate(6, SmartDashboardComponent_div_99_mat_card_4_canvas_6_Template, 1, 3, \"canvas\", 64);\n    i0.ɵɵtemplate(7, SmartDashboardComponent_div_99_mat_card_4_canvas_7_Template, 1, 3, \"canvas\", 64);\n    i0.ɵɵtemplate(8, SmartDashboardComponent_div_99_mat_card_4_canvas_8_Template, 1, 3, \"canvas\", 64);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const chart_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, chart_r11.type === \"line\" || chart_r11.title.toLowerCase().includes(\"trend\"), chart_r11.type !== \"line\" && !chart_r11.title.toLowerCase().includes(\"trend\"), chart_r11.type === \"doughnut\" || chart_r11.type === \"pie\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(chart_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-chart-type\", chart_r11.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chart_r11.type === \"line\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chart_r11.type === \"bar\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chart_r11.type === \"doughnut\" || chart_r11.type === \"pie\");\n  }\n}\nfunction SmartDashboardComponent_div_99_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-card\", 66)(2, \"mat-card-header\")(3, \"mat-card-title\", 62);\n    i0.ɵɵtext(4, \"Purchase Trends (Last 30 Days)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 63)(7, \"div\", 67)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"show_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"Purchase trends data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(12, \"mat-card\", 68)(13, \"mat-card-header\")(14, \"mat-card-title\", 62);\n    i0.ɵɵtext(15, \"Top Vendors by Purchase Amount\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"mat-card-content\")(17, \"div\", 63)(18, \"div\", 67)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\");\n    i0.ɵɵtext(22, \"Vendor data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"mat-card\", 68)(24, \"mat-card-header\")(25, \"mat-card-title\", 62);\n    i0.ɵɵtext(26, \"Category wise Spending Distribution\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"mat-card-content\")(28, \"div\", 63)(29, \"div\", 67)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"pie_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"Category spending data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"mat-card\", 66)(35, \"mat-card-header\")(36, \"mat-card-title\", 62);\n    i0.ɵɵtext(37, \"Top Items by Quantity Purchased\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"mat-card-content\")(39, \"div\", 63)(40, \"div\", 67)(41, \"mat-icon\");\n    i0.ɵɵtext(42, \"inventory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\");\n    i0.ɵɵtext(44, \"Top items data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SmartDashboardComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_99_mat_card_2_Template, 11, 7, \"mat-card\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52);\n    i0.ɵɵtemplate(4, SmartDashboardComponent_div_99_mat_card_4_Template, 9, 10, \"mat-card\", 53);\n    i0.ɵɵtemplate(5, SmartDashboardComponent_div_99_ng_container_5_Template, 45, 0, \"ng-container\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.summaryCards);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.charts);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.charts.length === 0 && !ctx_r4.isLoading);\n  }\n}\nfunction SmartDashboardComponent_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"mat-icon\", 70);\n    i0.ɵɵtext(2, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Data Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Please select a location and date range to view dashboard data.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_100_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.loadDashboardData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Refresh Data \");\n    i0.ɵɵelementEnd()();\n  }\n}\nclass SmartDashboardComponent {\n  constructor(smartDashboardService, authService, shareDataService, cdr) {\n    this.smartDashboardService = smartDashboardService;\n    this.authService = authService;\n    this.shareDataService = shareDataService;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.branches = [];\n    this.filteredBranches = [];\n    this.selectedLocation = null;\n    // Form controls\n    this.locationFilterCtrl = new FormControl('');\n    this.startDate = new FormControl(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 30 days ago\n    this.endDate = new FormControl(new Date());\n    this.searchQuery = new FormControl('');\n    this.baseDateCtrl = new FormControl('deliveryDate');\n    this.selectedDashboard = 'purchase';\n    // Dashboard data\n    this.summaryCards = [];\n    this.charts = [];\n    this.isLoading = false;\n    // Chart configurations\n    this.lineChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: true,\n          position: 'top',\n          labels: {\n            usePointStyle: true,\n            padding: 20\n          }\n        },\n        tooltip: {\n          mode: 'index',\n          intersect: false,\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          titleColor: '#fff',\n          bodyColor: '#fff',\n          borderColor: '#ff6b35',\n          borderWidth: 1\n        }\n      },\n      scales: {\n        x: {\n          display: true,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          },\n          title: {\n            display: false\n          }\n        },\n        y: {\n          display: true,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          },\n          title: {\n            display: false\n          },\n          beginAtZero: true,\n          ticks: {\n            callback: function (value) {\n              return '₹' + value.toLocaleString();\n            }\n          }\n        }\n      },\n      interaction: {\n        mode: 'nearest',\n        axis: 'x',\n        intersect: false\n      }\n    };\n    this.barChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      indexAxis: 'y',\n      plugins: {\n        legend: {\n          display: false\n        },\n        tooltip: {\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          titleColor: '#fff',\n          bodyColor: '#fff',\n          borderColor: '#ff6b35',\n          borderWidth: 1,\n          callbacks: {\n            label: function (context) {\n              return '₹' + context.parsed.x.toLocaleString();\n            }\n          }\n        }\n      },\n      scales: {\n        x: {\n          beginAtZero: true,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          },\n          title: {\n            display: false\n          },\n          ticks: {\n            callback: function (value) {\n              return '₹' + value.toLocaleString();\n            }\n          }\n        },\n        y: {\n          grid: {\n            display: false\n          },\n          title: {\n            display: false\n          }\n        }\n      }\n    };\n    this.doughnutChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: true,\n          position: 'right',\n          labels: {\n            usePointStyle: true,\n            padding: 15,\n            font: {\n              size: 12\n            }\n          }\n        },\n        tooltip: {\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          titleColor: '#fff',\n          bodyColor: '#fff',\n          borderColor: '#ff6b35',\n          borderWidth: 1,\n          callbacks: {\n            label: function (context) {\n              const label = context.label || '';\n              const value = context.parsed;\n              const total = context.dataset.data.reduce((a, b) => a + b, 0);\n              const percentage = (value / total * 100).toFixed(1);\n              return `${label}: ${percentage}%`;\n            }\n          }\n        }\n      },\n      cutout: '60%'\n    };\n    this.quantityBarChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          title: {\n            display: true,\n            text: 'Items'\n          }\n        },\n        y: {\n          beginAtZero: true,\n          title: {\n            display: true,\n            text: 'Quantity'\n          }\n        }\n      }\n    };\n    this.user = this.authService.getCurrentUser();\n  }\n  ngOnInit() {\n    this.initializeFilters();\n    this.loadBranches();\n    this.loadDashboardData();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeFilters() {\n    // Location filter\n    this.locationFilterCtrl.valueChanges.pipe(debounceTime(200), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(value => {\n      this.filterBranches(value || '');\n    });\n  }\n  loadBranches() {\n    this.shareDataService.selectedBranchesSource.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.branches = data || [];\n      this.filteredBranches = [...this.branches];\n      if (this.branches.length === 1) {\n        this.selectedLocation = this.branches[0].restaurantIdOld;\n      }\n    });\n  }\n  filterBranches(searchTerm) {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm));\n    }\n  }\n  loadDashboardData() {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          // Load sample data for demonstration\n          this.loadSampleData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading dashboard data:', error);\n        // Load sample data on error for demonstration\n        this.loadSampleData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  loadSampleData() {\n    // Sample data matching the UI shown in the image\n    const sampleData = {\n      summary_items: [{\n        icon: 'attach_money',\n        value: '₹34,766',\n        label: 'Total Purchase Amount',\n        data_type: 'currency'\n      }, {\n        icon: 'shopping_cart',\n        value: '271',\n        label: 'Total Orders',\n        data_type: 'number'\n      }, {\n        icon: 'trending_up',\n        value: '₹1,973',\n        label: 'Average Order Value',\n        data_type: 'currency'\n      }, {\n        icon: 'store',\n        value: 'Fresh Mart',\n        label: 'Top Vendor',\n        data_type: 'vendor'\n      }],\n      charts: [{\n        id: 'purchase-trends',\n        title: 'Purchase Trends (Last 30 Days)',\n        type: 'line',\n        data: {\n          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n          datasets: [{\n            label: 'Daily Purchase Amount',\n            data: [15000, 22000, 18000, 25000],\n            backgroundColor: ['#ff6b35'],\n            borderColor: ['#ff6b35']\n          }]\n        }\n      }, {\n        id: 'top-vendors',\n        title: 'Top Vendors by Purchase Amount',\n        type: 'bar',\n        data: {\n          labels: ['ABC Suppliers', 'XYZ Foods', 'Fresh Mart', 'Quality Grocers', 'Best Price Co'],\n          datasets: [{\n            label: 'Total Purchase Amount',\n            data: [180000, 150000, 140000, 120000, 100000],\n            backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n            borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n          }]\n        }\n      }, {\n        id: 'category-spending',\n        title: 'Category wise Spending Distribution',\n        type: 'doughnut',\n        data: {\n          labels: ['Vegetables', 'Meat', 'Dairy', 'Beverages', 'Dry Goods'],\n          datasets: [{\n            label: 'Spending Distribution',\n            data: [30, 25, 20, 15, 10],\n            backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n            borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n          }]\n        }\n      }]\n    };\n    this.processDashboardData(sampleData);\n  }\n  processDashboardData(data) {\n    // Process summary cards\n    this.summaryCards = data.summary_items?.map(item => ({\n      icon: this.smartDashboardService.getSummaryCardIcon(item.data_type, item.label),\n      value: item.value,\n      label: item.label,\n      color: this.getCardColor(item.data_type)\n    })) || [];\n    // If no summary cards from API, create default ones\n    if (this.summaryCards.length === 0) {\n      this.summaryCards = this.createDefaultSummaryCards();\n    }\n    // Process charts with enhanced data\n    this.charts = data.charts?.map(chart => ({\n      ...chart,\n      data: this.smartDashboardService.processChartData(chart)\n    })) || [];\n    // If no charts from API, create default message\n    if (this.charts.length === 0) {\n      console.log('No charts data received from API');\n    }\n  }\n  createDefaultSummaryCards() {\n    return [{\n      icon: 'attach_money',\n      value: '₹34,766',\n      label: 'Total Purchase Amount',\n      color: '#ff6b35'\n    }, {\n      icon: 'shopping_cart',\n      value: '271',\n      label: 'Total Orders',\n      color: '#ffa66f'\n    }, {\n      icon: 'trending_up',\n      value: '₹1,973',\n      label: 'Average Order Value',\n      color: '#ff8b4d'\n    }, {\n      icon: 'store',\n      value: 'Fresh Mart',\n      label: 'Top Vendor',\n      color: '#ff9966'\n    }];\n  }\n  getCardColor(dataType) {\n    const colorMap = {\n      'currency': '#ff6b35',\n      'number': '#ffa66f',\n      'percentage': '#ff8b4d',\n      'vendor': '#ff9966'\n    };\n    return colorMap[dataType] || '#ff6b35';\n  }\n  formatDate(date) {\n    return date.toISOString().split('T')[0];\n  }\n  onLocationChange() {\n    this.loadDashboardData();\n  }\n  onDateChange() {\n    this.loadDashboardData();\n  }\n  onSearchQuery() {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n  loadDashboardDataWithQuery(query) {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success' && response.data.charts.length > 0) {\n          this.processDashboardData(response.data);\n        } else {\n          // Query returned no results, show default data\n          console.log('Query returned no data, loading default dashboard');\n          this.loadDashboardData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.warn('Error with query, falling back to default dashboard:', error);\n        this.loadDashboardData();\n      }\n    });\n  }\n  getChartData(chart) {\n    return chart.data;\n  }\n  getChartType(chart) {\n    return chart.type;\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.SmartDashboardService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 101,\n      vars: 15,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"dashboard-selection\"], [\"appearance\", \"outline\", 1, \"dashboard-dropdown\"], [3, \"value\", \"valueChange\"], [\"value\", \"purchase\"], [\"value\", \"sales\"], [\"value\", \"inventory\"], [1, \"dashboard-content\"], [1, \"sidebar\"], [1, \"filters-section\"], [1, \"filters-title\"], [1, \"filter-count\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [3, \"value\", \"valueChange\", \"selectionChange\"], [\"placeholderLabel\", \"Search locations...\", \"noEntriesFoundLabel\", \"No locations found\", 3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"formControl\", \"selectionChange\"], [\"value\", \"deliveryDate\"], [\"value\", \"orderDate\"], [\"value\", \"createdDate\"], [\"matInput\", \"\", 3, \"matDatepicker\", \"formControl\", \"dateChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"endPicker\", \"\"], [1, \"filter-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-filters-btn\", 3, \"click\"], [1, \"main-content\"], [1, \"content-header\"], [1, \"header-content\"], [1, \"header-left\"], [1, \"assistant-info\"], [1, \"assistant-icon\"], [1, \"assistant-text\"], [1, \"assistant-title\"], [1, \"assistant-status\"], [1, \"header-right\"], [1, \"search-container\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Ask me about your business data\", 3, \"formControl\", \"keyup.enter\"], [\"matSuffix\", \"\", 1, \"search-icon\", 3, \"click\"], [1, \"scrollable-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"dashboard-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [3, \"value\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"dashboard-grid\"], [1, \"summary-cards-row\"], [\"class\", \"summary-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"charts-grid\"], [\"class\", \"chart-card\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"summary-card\"], [1, \"card-content\"], [1, \"card-icon\"], [1, \"card-info\"], [1, \"card-value\"], [1, \"card-label\"], [1, \"chart-card\", 3, \"ngClass\"], [1, \"chart-title\"], [1, \"chart-container\"], [\"baseChart\", \"\", 3, \"type\", \"data\", \"options\", 4, \"ngIf\"], [\"baseChart\", \"\", 3, \"type\", \"data\", \"options\"], [1, \"chart-card\", \"full-width\"], [1, \"no-data-message\"], [1, \"chart-card\", \"half-width\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"mat-form-field\", 2)(3, \"mat-label\");\n          i0.ɵɵtext(4, \"Purchase Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"mat-select\", 3);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_5_listener($event) {\n            return ctx.selectedDashboard = $event;\n          });\n          i0.ɵɵelementStart(6, \"mat-option\", 4);\n          i0.ɵɵtext(7, \"Purchase Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"mat-option\", 5);\n          i0.ɵɵtext(9, \"Sales Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"mat-option\", 6);\n          i0.ɵɵtext(11, \"Inventory Dashboard\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 7)(13, \"div\", 8)(14, \"div\", 9)(15, \"h3\", 10)(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" Smart Filters \");\n          i0.ɵɵelementStart(19, \"span\", 11);\n          i0.ɵɵtext(20, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 12)(22, \"h4\", 13)(23, \"mat-icon\");\n          i0.ɵɵtext(24, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(25, \" Restaurants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-form-field\", 14)(27, \"mat-label\");\n          i0.ɵɵtext(28, \"Select Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-select\", 15);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_29_listener($event) {\n            return ctx.selectedLocation = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_29_listener() {\n            return ctx.onLocationChange();\n          });\n          i0.ɵɵelementStart(30, \"mat-option\");\n          i0.ɵɵelement(31, \"ngx-mat-select-search\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, SmartDashboardComponent_mat_option_32_Template, 2, 2, \"mat-option\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"div\", 12)(34, \"h4\", 13)(35, \"mat-icon\");\n          i0.ɵɵtext(36, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(37, \" Base Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"mat-form-field\", 14)(39, \"mat-label\");\n          i0.ɵɵtext(40, \"Select base date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"mat-select\", 18);\n          i0.ɵɵlistener(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_41_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementStart(42, \"mat-option\", 19);\n          i0.ɵɵtext(43, \"Delivery Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"mat-option\", 20);\n          i0.ɵɵtext(45, \"Order Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-option\", 21);\n          i0.ɵɵtext(47, \"Created Date\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(48, \"div\", 12)(49, \"h4\", 13)(50, \"mat-icon\");\n          i0.ɵɵtext(51, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(52, \" Start Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"mat-form-field\", 14)(54, \"mat-label\");\n          i0.ɵɵtext(55, \"Start Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"input\", 22);\n          i0.ɵɵlistener(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_56_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(57, \"mat-datepicker-toggle\", 23)(58, \"mat-datepicker\", null, 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 12)(61, \"h4\", 13)(62, \"mat-icon\");\n          i0.ɵɵtext(63, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(64, \" End Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"mat-form-field\", 14)(66, \"mat-label\");\n          i0.ɵɵtext(67, \"End Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"input\", 22);\n          i0.ɵɵlistener(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_68_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(69, \"mat-datepicker-toggle\", 23)(70, \"mat-datepicker\", null, 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 26)(73, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_73_listener() {\n            return ctx.loadDashboardData();\n          });\n          i0.ɵɵelementStart(74, \"mat-icon\");\n          i0.ɵɵtext(75, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(76, \" Reset filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(77, \"div\", 28)(78, \"div\", 29)(79, \"div\", 30)(80, \"div\", 31)(81, \"div\", 32)(82, \"mat-icon\", 33);\n          i0.ɵɵtext(83, \"smart_toy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"div\", 34)(85, \"span\", 35);\n          i0.ɵɵtext(86, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"span\", 36);\n          i0.ɵɵtext(88, \"Ready to analyze\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(89, \"div\", 37)(90, \"div\", 38)(91, \"mat-form-field\", 39)(92, \"mat-label\");\n          i0.ɵɵtext(93, \"Ask me about your business data\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"input\", 40);\n          i0.ɵɵlistener(\"keyup.enter\", function SmartDashboardComponent_Template_input_keyup_enter_94_listener() {\n            return ctx.onSearchQuery();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"mat-icon\", 41);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_mat_icon_click_95_listener() {\n            return ctx.onSearchQuery();\n          });\n          i0.ɵɵtext(96, \"search\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(97, \"div\", 42);\n          i0.ɵɵtemplate(98, SmartDashboardComponent_div_98_Template, 4, 0, \"div\", 43);\n          i0.ɵɵtemplate(99, SmartDashboardComponent_div_99_Template, 6, 3, \"div\", 44);\n          i0.ɵɵtemplate(100, SmartDashboardComponent_div_100_Template, 11, 0, \"div\", 45);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(59);\n          const _r2 = i0.ɵɵreference(71);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"value\", ctx.selectedDashboard);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.locationFilterCtrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredBranches);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formControl\", ctx.baseDateCtrl);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"matDatepicker\", _r1)(\"formControl\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r1);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"matDatepicker\", _r2)(\"formControl\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r2);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"formControl\", ctx.searchQuery);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.summaryCards.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, MatCardModule, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, MatButtonModule, i6.MatButton, MatIconModule, i7.MatIcon, MatSelectModule, i8.MatFormField, i8.MatLabel, i8.MatSuffix, i9.MatSelect, i10.MatOption, MatFormFieldModule, MatInputModule, i11.MatInput, MatDatepickerModule, i12.MatDatepicker, i12.MatDatepickerInput, i12.MatDatepickerToggle, MatNativeDateModule, MatDividerModule, MatProgressSpinnerModule, i13.MatProgressSpinner, NgChartsModule, i14.BaseChartDirective, NgxMatSelectSearchModule, i15.MatSelectSearchComponent, ReactiveFormsModule, i16.DefaultValueAccessor, i16.NgControlStatus, i16.FormControlDirective, FormsModule],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #f8f9fa;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b35 0%, #ffa66f 100%);\\n  padding: 12px 24px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%] {\\n  width: 250px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background-color: white;\\n  border-radius: 6px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  color: rgba(255, 255, 255, 0.3);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-outline-thick {\\n  color: white;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-label {\\n  color: #333;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-select-value {\\n  color: #333;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-select-arrow {\\n  color: #ff6b35;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  overflow: hidden;\\n  height: calc(100vh - 60px);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background: white;\\n  border-right: 1px solid #e9ecef;\\n  padding: 20px;\\n  overflow-y: auto;\\n  height: 100%;\\n  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 0 20px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background: #ff6b35;\\n  color: white;\\n  border-radius: 50%;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 11px;\\n  font-weight: bold;\\n  margin-left: auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 0 12px 0;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6b35;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 8px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background-color: white;\\n  border-radius: 4px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  color: #dee2e6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline-thick {\\n  color: #ff6b35;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n  padding-top: 20px;\\n  border-top: 1px solid #e9ecef;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .reset-filters-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  color: #6c757d;\\n  border-color: #dee2e6;\\n  font-weight: 500;\\n  padding: 10px;\\n  border-radius: 4px;\\n  font-size: 14px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .reset-filters-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  border-color: #adb5bd;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #f8f9fa;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #e9ecef;\\n  padding: 16px 24px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  flex-shrink: 0;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%]   .assistant-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%]   .assistant-status[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #ff6b35;\\n  font-weight: 500;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 400px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  color: #dee2e6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-focus-overlay {\\n  background-color: transparent;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  cursor: pointer;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scrollable-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scrollable-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  gap: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scrollable-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, 1fr);\\n  gap: 16px;\\n  margin-bottom: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n  transition: box-shadow 0.2s ease;\\n  position: relative;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:nth-child(1) {\\n  border-left: 4px solid #ff6b35;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:nth-child(2) {\\n  border-left: 4px solid #ffa66f;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:nth-child(3) {\\n  border-left: 4px solid #ff8b4d;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:nth-child(4) {\\n  border-left: 4px solid #ff9966;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]     .mat-mdc-card-content {\\n  padding: 16px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 4px;\\n  line-height: 1.2;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   .card-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c757d;\\n  font-weight: 500;\\n  line-height: 1.3;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(12, 1fr);\\n  gap: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n  transition: box-shadow 0.2s ease;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.full-width[_ngcontent-%COMP%] {\\n  grid-column: span 12;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.half-width[_ngcontent-%COMP%] {\\n  grid-column: span 6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.third-width[_ngcontent-%COMP%] {\\n  grid-column: span 4;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]     .mat-mdc-card-header {\\n  padding: 16px 16px 0 16px;\\n  border-bottom: 1px solid #f1f3f4;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]     .mat-mdc-card-content {\\n  padding: 16px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  height: 280px;\\n  position: relative;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  color: #adb5bd;\\n  text-align: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 40px;\\n  width: 40px;\\n  height: 40px;\\n  margin-bottom: 8px;\\n  opacity: 0.5;\\n  color: #ff6b35;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 13px;\\n  font-weight: 500;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[data-chart-type=line][_ngcontent-%COMP%] {\\n  height: 300px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[data-chart-type=doughnut][_ngcontent-%COMP%], .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[data-chart-type=pie][_ngcontent-%COMP%] {\\n  height: 260px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  text-align: center;\\n  color: #666;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  margin-bottom: 16px;\\n  opacity: 0.5;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  font-size: 14px;\\n}\\n\\n@media (max-width: 1400px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scrollable-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scrollable-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.half-width[_ngcontent-%COMP%], .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scrollable-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.third-width[_ngcontent-%COMP%] {\\n    grid-column: span 12;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    align-items: stretch;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n    width: 260px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: calc(100vh - 50px);\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n    max-height: 300px;\\n    border-right: none;\\n    border-bottom: 1px solid #e9ecef;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scrollable-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scrollable-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%] {\\n    grid-column: span 12 !important;\\n  }\\n}\\n  .chart-container canvas {\\n  max-width: 100% !important;\\n  height: auto !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatFormFieldModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatDividerModule", "MatProgressSpinnerModule", "NgChartsModule", "NgxMatSelectSearchModule", "FormControl", "ReactiveFormsModule", "FormsModule", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "branch_r6", "restaurantIdOld", "ɵɵadvance", "ɵɵtextInterpolate1", "branchName", "ɵɵelement", "ɵɵstyleProp", "card_r10", "color", "ɵɵtextInterpolate", "icon", "value", "label", "ctx_r13", "getChartData", "chart_r11", "lineChartOptions", "ctx_r14", "barChartOptions", "ctx_r15", "doughnutChartOptions", "ɵɵtemplate", "SmartDashboardComponent_div_99_mat_card_4_canvas_6_Template", "SmartDashboardComponent_div_99_mat_card_4_canvas_7_Template", "SmartDashboardComponent_div_99_mat_card_4_canvas_8_Template", "ɵɵpureFunction3", "_c0", "type", "title", "toLowerCase", "includes", "ɵɵattribute", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "SmartDashboardComponent_div_99_mat_card_2_Template", "SmartDashboardComponent_div_99_mat_card_4_Template", "SmartDashboardComponent_div_99_ng_container_5_Template", "ctx_r4", "summaryCards", "charts", "length", "isLoading", "ɵɵlistener", "SmartDashboardComponent_div_100_Template_button_click_7_listener", "ɵɵrestoreView", "_r20", "ctx_r19", "ɵɵnextContext", "ɵɵresetView", "loadDashboardData", "SmartDashboardComponent", "constructor", "smartDashboardService", "authService", "shareDataService", "cdr", "destroy$", "branches", "filteredBranches", "selectedLocation", "locationFilterCtrl", "startDate", "Date", "now", "endDate", "searchQuery", "baseDateCtrl", "selectedDashboard", "responsive", "maintainAspectRatio", "plugins", "legend", "display", "position", "labels", "usePointStyle", "padding", "tooltip", "mode", "intersect", "backgroundColor", "titleColor", "bodyColor", "borderColor", "borderWidth", "scales", "x", "grid", "y", "beginAtZero", "ticks", "callback", "toLocaleString", "interaction", "axis", "indexAxis", "callbacks", "context", "parsed", "font", "size", "total", "dataset", "data", "reduce", "a", "b", "percentage", "toFixed", "cutout", "quantityBarChartOptions", "text", "user", "getCurrentUser", "ngOnInit", "initializeFilters", "loadBranches", "ngOnDestroy", "next", "complete", "valueChanges", "pipe", "subscribe", "filterBranches", "selectedBranchesSource", "searchTerm", "normalizedSearchTerm", "replace", "filter", "branch", "filters", "locations", "formatDate", "baseDate", "request", "tenant_id", "tenantId", "user_query", "use_default_charts", "getSmartDashboardData", "response", "status", "processDashboardData", "loadSampleData", "detectChanges", "error", "console", "sampleData", "summary_items", "data_type", "id", "datasets", "map", "item", "getSummaryCardIcon", "getCardColor", "createDefaultSummaryCards", "chart", "processChartData", "log", "dataType", "colorMap", "date", "toISOString", "split", "onLocationChange", "onDateChange", "onSearchQuery", "query", "trim", "loadDashboardDataWithQuery", "warn", "getChartType", "ɵɵdirectiveInject", "i1", "SmartDashboardService", "i2", "AuthService", "i3", "ShareDataService", "ChangeDetectorRef", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "SmartDashboardComponent_Template_mat_select_valueChange_5_listener", "$event", "SmartDashboardComponent_Template_mat_select_valueChange_29_listener", "SmartDashboardComponent_Template_mat_select_selectionChange_29_listener", "SmartDashboardComponent_mat_option_32_Template", "SmartDashboardComponent_Template_mat_select_selectionChange_41_listener", "SmartDashboardComponent_Template_input_dateChange_56_listener", "SmartDashboardComponent_Template_input_dateChange_68_listener", "SmartDashboardComponent_Template_button_click_73_listener", "SmartDashboardComponent_Template_input_keyup_enter_94_listener", "SmartDashboardComponent_Template_mat_icon_click_95_listener", "SmartDashboardComponent_div_98_Template", "SmartDashboardComponent_div_99_Template", "SmartDashboardComponent_div_100_Template", "_r1", "_r2", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i6", "MatButton", "i7", "MatIcon", "i8", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i9", "MatSelect", "i10", "MatOption", "i11", "MatInput", "i12", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i13", "MatProgressSpinner", "i14", "BaseChartDirective", "i15", "MatSelectSearchComponent", "i16", "DefaultValueAccessor", "NgControlStatus", "FormControlDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { ChartConfiguration, ChartData, ChartType } from 'chart.js';\n\nimport { SmartDashboardService } from '../../services/smart-dashboard.service';\nimport { AuthService } from '../../services/auth.service';\nimport { ShareDataService } from '../../services/share-data.service';\n\ninterface SummaryCard {\n  icon: string;\n  value: string;\n  label: string;\n  color: string;\n}\n\ninterface ChartDataModel {\n  id: string;\n  title: string;\n  type: string;\n  data: ChartData;\n}\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    NgChartsModule,\n    NgxMatSelectSearchModule,\n    ReactiveFormsModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  // User and branch data\n  user: any;\n  branches: any[] = [];\n  filteredBranches: any[] = [];\n  selectedLocation: string | null = null;\n  \n  // Form controls\n  locationFilterCtrl = new FormControl('');\n  startDate = new FormControl(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 30 days ago\n  endDate = new FormControl(new Date());\n  searchQuery = new FormControl('');\n  baseDateCtrl = new FormControl('deliveryDate');\n  selectedDashboard = 'purchase';\n  \n  // Dashboard data\n  summaryCards: SummaryCard[] = [];\n  charts: ChartDataModel[] = [];\n  isLoading = false;\n  \n  // Chart configurations\n  lineChartOptions: ChartConfiguration['options'] = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: true,\n        position: 'top',\n        labels: {\n          usePointStyle: true,\n          padding: 20\n        }\n      },\n      tooltip: {\n        mode: 'index',\n        intersect: false,\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: '#ff6b35',\n        borderWidth: 1\n      }\n    },\n    scales: {\n      x: {\n        display: true,\n        grid: {\n          display: true,\n          color: 'rgba(0,0,0,0.1)'\n        },\n        title: {\n          display: false\n        }\n      },\n      y: {\n        display: true,\n        grid: {\n          display: true,\n          color: 'rgba(0,0,0,0.1)'\n        },\n        title: {\n          display: false\n        },\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return '₹' + value.toLocaleString();\n          }\n        }\n      }\n    },\n    interaction: {\n      mode: 'nearest',\n      axis: 'x',\n      intersect: false\n    }\n  };\n\n  barChartOptions: ChartConfiguration['options'] = {\n    responsive: true,\n    maintainAspectRatio: false,\n    indexAxis: 'y' as const,\n    plugins: {\n      legend: {\n        display: false\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: '#ff6b35',\n        borderWidth: 1,\n        callbacks: {\n          label: function(context: any) {\n            return '₹' + context.parsed.x.toLocaleString();\n          }\n        }\n      }\n    },\n    scales: {\n      x: {\n        beginAtZero: true,\n        grid: {\n          display: true,\n          color: 'rgba(0,0,0,0.1)'\n        },\n        title: {\n          display: false\n        },\n        ticks: {\n          callback: function(value: any) {\n            return '₹' + value.toLocaleString();\n          }\n        }\n      },\n      y: {\n        grid: {\n          display: false\n        },\n        title: {\n          display: false\n        }\n      }\n    }\n  };\n\n  doughnutChartOptions: any = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: true,\n        position: 'right',\n        labels: {\n          usePointStyle: true,\n          padding: 15,\n          font: {\n            size: 12\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: '#ff6b35',\n        borderWidth: 1,\n        callbacks: {\n          label: function(context: any) {\n            const label = context.label || '';\n            const value = context.parsed;\n            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);\n            const percentage = ((value / total) * 100).toFixed(1);\n            return `${label}: ${percentage}%`;\n          }\n        }\n      }\n    },\n    cutout: '60%'\n  };\n\n  quantityBarChartOptions: ChartConfiguration['options'] = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        title: {\n          display: true,\n          text: 'Items'\n        }\n      },\n      y: {\n        beginAtZero: true,\n        title: {\n          display: true,\n          text: 'Quantity'\n        }\n      }\n    }\n  };\n\n  constructor(\n    private smartDashboardService: SmartDashboardService,\n    private authService: AuthService,\n    private shareDataService: ShareDataService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.user = this.authService.getCurrentUser();\n  }\n\n  ngOnInit(): void {\n    this.initializeFilters();\n    this.loadBranches();\n    this.loadDashboardData();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeFilters(): void {\n    // Location filter\n    this.locationFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(200),\n        distinctUntilChanged(),\n        takeUntil(this.destroy$)\n      )\n      .subscribe((value: string | null) => {\n        this.filterBranches(value || '');\n      });\n  }\n\n  private loadBranches(): void {\n    this.shareDataService.selectedBranchesSource\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.branches = data || [];\n        this.filteredBranches = [...this.branches];\n        \n        if (this.branches.length === 1) {\n          this.selectedLocation = this.branches[0].restaurantIdOld;\n        }\n      });\n  }\n\n  private filterBranches(searchTerm: string): void {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch =>\n        branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm)\n      );\n    }\n  }\n\n  loadDashboardData(): void {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            // Load sample data for demonstration\n            this.loadSampleData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: (error) => {\n          console.error('Error loading dashboard data:', error);\n          // Load sample data on error for demonstration\n          this.loadSampleData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  private loadSampleData(): void {\n    // Sample data matching the UI shown in the image\n    const sampleData = {\n      summary_items: [\n        { icon: 'attach_money', value: '₹34,766', label: 'Total Purchase Amount', data_type: 'currency' },\n        { icon: 'shopping_cart', value: '271', label: 'Total Orders', data_type: 'number' },\n        { icon: 'trending_up', value: '₹1,973', label: 'Average Order Value', data_type: 'currency' },\n        { icon: 'store', value: 'Fresh Mart', label: 'Top Vendor', data_type: 'vendor' }\n      ],\n      charts: [\n        {\n          id: 'purchase-trends',\n          title: 'Purchase Trends (Last 30 Days)',\n          type: 'line',\n          data: {\n            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n            datasets: [{\n              label: 'Daily Purchase Amount',\n              data: [15000, 22000, 18000, 25000],\n              backgroundColor: ['#ff6b35'],\n              borderColor: ['#ff6b35']\n            }]\n          }\n        },\n        {\n          id: 'top-vendors',\n          title: 'Top Vendors by Purchase Amount',\n          type: 'bar',\n          data: {\n            labels: ['ABC Suppliers', 'XYZ Foods', 'Fresh Mart', 'Quality Grocers', 'Best Price Co'],\n            datasets: [{\n              label: 'Total Purchase Amount',\n              data: [180000, 150000, 140000, 120000, 100000],\n              backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n              borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n            }]\n          }\n        },\n        {\n          id: 'category-spending',\n          title: 'Category wise Spending Distribution',\n          type: 'doughnut',\n          data: {\n            labels: ['Vegetables', 'Meat', 'Dairy', 'Beverages', 'Dry Goods'],\n            datasets: [{\n              label: 'Spending Distribution',\n              data: [30, 25, 20, 15, 10],\n              backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n              borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n            }]\n          }\n        }\n      ]\n    };\n\n    this.processDashboardData(sampleData);\n  }\n\n  private processDashboardData(data: any): void {\n    // Process summary cards\n    this.summaryCards = data.summary_items?.map((item: any) => ({\n      icon: this.smartDashboardService.getSummaryCardIcon(item.data_type, item.label),\n      value: item.value,\n      label: item.label,\n      color: this.getCardColor(item.data_type)\n    })) || [];\n\n    // If no summary cards from API, create default ones\n    if (this.summaryCards.length === 0) {\n      this.summaryCards = this.createDefaultSummaryCards();\n    }\n\n    // Process charts with enhanced data\n    this.charts = data.charts?.map((chart: any) => ({\n      ...chart,\n      data: this.smartDashboardService.processChartData(chart)\n    })) || [];\n\n    // If no charts from API, create default message\n    if (this.charts.length === 0) {\n      console.log('No charts data received from API');\n    }\n  }\n\n  private createDefaultSummaryCards(): SummaryCard[] {\n    return [\n      {\n        icon: 'attach_money',\n        value: '₹34,766',\n        label: 'Total Purchase Amount',\n        color: '#ff6b35'\n      },\n      {\n        icon: 'shopping_cart',\n        value: '271',\n        label: 'Total Orders',\n        color: '#ffa66f'\n      },\n      {\n        icon: 'trending_up',\n        value: '₹1,973',\n        label: 'Average Order Value',\n        color: '#ff8b4d'\n      },\n      {\n        icon: 'store',\n        value: 'Fresh Mart',\n        label: 'Top Vendor',\n        color: '#ff9966'\n      }\n    ];\n  }\n\n  private getCardColor(dataType: string): string {\n    const colorMap: { [key: string]: string } = {\n      'currency': '#ff6b35',\n      'number': '#ffa66f',\n      'percentage': '#ff8b4d',\n      'vendor': '#ff9966'\n    };\n    return colorMap[dataType] || '#ff6b35';\n  }\n\n  private formatDate(date: Date): string {\n    return date.toISOString().split('T')[0];\n  }\n\n  onLocationChange(): void {\n    this.loadDashboardData();\n  }\n\n  onDateChange(): void {\n    this.loadDashboardData();\n  }\n\n  onSearchQuery(): void {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n\n  private loadDashboardDataWithQuery(query: string): void {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success' && response.data.charts.length > 0) {\n            this.processDashboardData(response.data);\n          } else {\n            // Query returned no results, show default data\n            console.log('Query returned no data, loading default dashboard');\n            this.loadDashboardData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: (error) => {\n          console.warn('Error with query, falling back to default dashboard:', error);\n          this.loadDashboardData();\n        }\n      });\n  }\n\n  getChartData(chart: ChartDataModel): ChartData {\n    return chart.data;\n  }\n\n  getChartType(chart: ChartDataModel): ChartType {\n    return chart.type as ChartType;\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Top Dashboard Selection -->\n  <div class=\"dashboard-selection\">\n    <mat-form-field appearance=\"outline\" class=\"dashboard-dropdown\">\n      <mat-label>Purchase Dashboard</mat-label>\n      <mat-select [(value)]=\"selectedDashboard\">\n        <mat-option value=\"purchase\">Purchase Dashboard</mat-option>\n        <mat-option value=\"sales\">Sales Dashboard</mat-option>\n        <mat-option value=\"inventory\">Inventory Dashboard</mat-option>\n      </mat-select>\n    </mat-form-field>\n  </div>\n\n  <div class=\"dashboard-content\">\n    <!-- Sidebar Filters -->\n    <div class=\"sidebar\">\n      <div class=\"filters-section\">\n        <h3 class=\"filters-title\">\n          <mat-icon>tune</mat-icon>\n          Smart Filters\n          <span class=\"filter-count\">1</span>\n        </h3>\n\n        <!-- Location Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>location_on</mat-icon>\n            Restaurants\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select Location</mat-label>\n            <mat-select [(value)]=\"selectedLocation\" (selectionChange)=\"onLocationChange()\">\n              <mat-option>\n                <ngx-mat-select-search \n                  [formControl]=\"locationFilterCtrl\"\n                  placeholderLabel=\"Search locations...\"\n                  noEntriesFoundLabel=\"No locations found\">\n                </ngx-mat-select-search>\n              </mat-option>\n              <mat-option *ngFor=\"let branch of filteredBranches\" [value]=\"branch.restaurantIdOld\">\n                {{branch.branchName}}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>event</mat-icon>\n            Base Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select base date</mat-label>\n            <mat-select [formControl]=\"baseDateCtrl\" (selectionChange)=\"onDateChange()\">\n              <mat-option value=\"deliveryDate\">Delivery Date</mat-option>\n              <mat-option value=\"orderDate\">Order Date</mat-option>\n              <mat-option value=\"createdDate\">Created Date</mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            Start Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Start Date</mat-label>\n            <input matInput [matDatepicker]=\"startPicker\" [formControl]=\"startDate\" (dateChange)=\"onDateChange()\">\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            End Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>End Date</mat-label>\n            <input matInput [matDatepicker]=\"endPicker\" [formControl]=\"endDate\" (dateChange)=\"onDateChange()\">\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- Reset Filters Button -->\n        <div class=\"filter-actions\">\n          <button mat-stroked-button class=\"reset-filters-btn\" (click)=\"loadDashboardData()\">\n            <mat-icon>refresh</mat-icon>\n            Reset filters\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Main Content -->\n    <div class=\"main-content\">\n      <!-- Header with Search -->\n      <div class=\"content-header\">\n        <div class=\"header-content\">\n          <div class=\"header-left\">\n            <div class=\"assistant-info\">\n              <mat-icon class=\"assistant-icon\">smart_toy</mat-icon>\n              <div class=\"assistant-text\">\n                <span class=\"assistant-title\">Smart Dashboard Assistant</span>\n                <span class=\"assistant-status\">Ready to analyze</span>\n              </div>\n            </div>\n          </div>\n          <div class=\"header-right\">\n            <div class=\"search-container\">\n              <mat-form-field appearance=\"outline\" class=\"search-field\">\n                <mat-label>Ask me about your business data</mat-label>\n                <input matInput\n                       placeholder=\"Ask me about your business data\"\n                       [formControl]=\"searchQuery\"\n                       (keyup.enter)=\"onSearchQuery()\" />\n                <mat-icon matSuffix class=\"search-icon\" (click)=\"onSearchQuery()\">search</mat-icon>\n              </mat-form-field>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Scrollable Content -->\n      <div class=\"scrollable-content\">\n        <!-- Loading Spinner -->\n        <div *ngIf=\"isLoading\" class=\"loading-container\">\n          <mat-spinner diameter=\"50\"></mat-spinner>\n          <p>Loading dashboard data...</p>\n        </div>\n\n      <!-- Dashboard Content -->\n      <div *ngIf=\"!isLoading\" class=\"dashboard-grid\">\n        <!-- Summary Cards Row -->\n        <div class=\"summary-cards-row\">\n          <mat-card *ngFor=\"let card of summaryCards\" class=\"summary-card\" [style.border-left-color]=\"card.color\">\n            <mat-card-content>\n              <div class=\"card-content\">\n                <div class=\"card-icon\" [style.color]=\"card.color\">\n                  <mat-icon>{{card.icon}}</mat-icon>\n                </div>\n                <div class=\"card-info\">\n                  <div class=\"card-value\">{{card.value}}</div>\n                  <div class=\"card-label\">{{card.label}}</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Charts Grid -->\n        <div class=\"charts-grid\">\n          <!-- Purchase Trends Chart (Full Width) -->\n          <mat-card *ngFor=\"let chart of charts; let i = index\"\n                    class=\"chart-card\"\n                    [ngClass]=\"{\n                      'full-width': chart.type === 'line' || chart.title.toLowerCase().includes('trend'),\n                      'half-width': chart.type !== 'line' && !chart.title.toLowerCase().includes('trend'),\n                      'third-width': chart.type === 'doughnut' || chart.type === 'pie'\n                    }\">\n            <mat-card-header>\n              <mat-card-title class=\"chart-title\">{{chart.title}}</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"chart-container\" [attr.data-chart-type]=\"chart.type\">\n                <!-- Line Chart -->\n                <canvas *ngIf=\"chart.type === 'line'\"\n                        baseChart\n                        [type]=\"'line'\"\n                        [data]=\"getChartData(chart)\"\n                        [options]=\"lineChartOptions\">\n                </canvas>\n\n                <!-- Bar Chart -->\n                <canvas *ngIf=\"chart.type === 'bar'\"\n                        baseChart\n                        [type]=\"'bar'\"\n                        [data]=\"getChartData(chart)\"\n                        [options]=\"barChartOptions\">\n                </canvas>\n\n                <!-- Doughnut Chart -->\n                <canvas *ngIf=\"chart.type === 'doughnut' || chart.type === 'pie'\"\n                        baseChart\n                        [type]=\"'doughnut'\"\n                        [data]=\"getChartData(chart)\"\n                        [options]=\"doughnutChartOptions\">\n                </canvas>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Default Charts if no data -->\n          <ng-container *ngIf=\"charts.length === 0 && !isLoading\">\n            <!-- Purchase Trends Chart -->\n            <mat-card class=\"chart-card full-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Purchase Trends (Last 30 Days)</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>show_chart</mat-icon>\n                    <p>Purchase trends data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Top Vendors Chart -->\n            <mat-card class=\"chart-card half-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Top Vendors by Purchase Amount</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>bar_chart</mat-icon>\n                    <p>Vendor data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Category Spending Chart -->\n            <mat-card class=\"chart-card half-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Category wise Spending Distribution</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>pie_chart</mat-icon>\n                    <p>Category spending data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Top Items by Quantity Chart -->\n            <mat-card class=\"chart-card full-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Top Items by Quantity Purchased</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>inventory</mat-icon>\n                    <p>Top items data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </ng-container>\n        </div>\n      </div>\n\n        <!-- Empty State -->\n        <div *ngIf=\"!isLoading && summaryCards.length === 0\" class=\"empty-state\">\n          <mat-icon class=\"empty-icon\">analytics</mat-icon>\n          <h3>No Data Available</h3>\n          <p>Please select a location and date range to view dashboard data.</p>\n          <button mat-raised-button color=\"primary\" (click)=\"loadDashboardData()\">\n            <mat-icon>refresh</mat-icon>\n            Refresh Data\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;ICwB/DC,EAAA,CAAAC,cAAA,qBAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,eAAA,CAAgC;IAClFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,UAAA,MACF;;;;;IA2FNT,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAU,SAAA,sBAAyC;IACzCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAOhCH,EAAA,CAAAC,cAAA,mBAAwG;IAItFD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEpCH,EAAA,CAAAC,cAAA,cAAuB;IACGD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IARaH,EAAA,CAAAW,WAAA,sBAAAC,QAAA,CAAAC,KAAA,CAAsC;IAG1Eb,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAW,WAAA,UAAAC,QAAA,CAAAC,KAAA,CAA0B;IACrCb,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAc,iBAAA,CAAAF,QAAA,CAAAG,IAAA,CAAa;IAGCf,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAc,iBAAA,CAAAF,QAAA,CAAAI,KAAA,CAAc;IACdhB,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAc,iBAAA,CAAAF,QAAA,CAAAK,KAAA,CAAc;;;;;IAuBxCjB,EAAA,CAAAU,SAAA,iBAKS;;;;;IAHDV,EAAA,CAAAI,UAAA,gBAAe,SAAAc,OAAA,CAAAC,YAAA,CAAAC,SAAA,cAAAF,OAAA,CAAAG,gBAAA;;;;;IAMvBrB,EAAA,CAAAU,SAAA,iBAKS;;;;;IAHDV,EAAA,CAAAI,UAAA,eAAc,SAAAkB,OAAA,CAAAH,YAAA,CAAAC,SAAA,cAAAE,OAAA,CAAAC,eAAA;;;;;IAMtBvB,EAAA,CAAAU,SAAA,iBAKS;;;;;IAHDV,EAAA,CAAAI,UAAA,oBAAmB,SAAAoB,OAAA,CAAAL,YAAA,CAAAC,SAAA,cAAAI,OAAA,CAAAC,oBAAA;;;;;;;;;;;;IA/BjCzB,EAAA,CAAAC,cAAA,mBAMa;IAE2BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEtEH,EAAA,CAAAC,cAAA,uBAAkB;IAGdD,EAAA,CAAA0B,UAAA,IAAAC,2DAAA,qBAKS;IAGT3B,EAAA,CAAA0B,UAAA,IAAAE,2DAAA,qBAKS;IAGT5B,EAAA,CAAA0B,UAAA,IAAAG,2DAAA,qBAKS;IACX7B,EAAA,CAAAG,YAAA,EAAM;;;;IAjCAH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAX,SAAA,CAAAY,IAAA,eAAAZ,SAAA,CAAAa,KAAA,CAAAC,WAAA,GAAAC,QAAA,WAAAf,SAAA,CAAAY,IAAA,gBAAAZ,SAAA,CAAAa,KAAA,CAAAC,WAAA,GAAAC,QAAA,WAAAf,SAAA,CAAAY,IAAA,mBAAAZ,SAAA,CAAAY,IAAA,YAIE;IAE4BhC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAc,iBAAA,CAAAM,SAAA,CAAAa,KAAA,CAAe;IAGtBjC,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAoC,WAAA,oBAAAhB,SAAA,CAAAY,IAAA,CAAmC;IAErDhC,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAgB,SAAA,CAAAY,IAAA,YAA2B;IAQ3BhC,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,SAAAgB,SAAA,CAAAY,IAAA,WAA0B;IAQ1BhC,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAI,UAAA,SAAAgB,SAAA,CAAAY,IAAA,mBAAAZ,SAAA,CAAAY,IAAA,WAAuD;;;;;IAWtEhC,EAAA,CAAAqC,uBAAA,GAAwD;IAEtDrC,EAAA,CAAAC,cAAA,mBAAwC;IAEAD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAErFH,EAAA,CAAAC,cAAA,uBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAOpDH,EAAA,CAAAC,cAAA,oBAAwC;IAEAD,EAAA,CAAAE,MAAA,sCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAErFH,EAAA,CAAAC,cAAA,wBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAO3CH,EAAA,CAAAC,cAAA,oBAAwC;IAEAD,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAE1FH,EAAA,CAAAC,cAAA,wBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,+CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAOtDH,EAAA,CAAAC,cAAA,oBAAwC;IAEAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEtFH,EAAA,CAAAC,cAAA,wBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKhDH,EAAA,CAAAsC,qBAAA,EAAe;;;;;IAzHnBtC,EAAA,CAAAC,cAAA,cAA+C;IAG3CD,EAAA,CAAA0B,UAAA,IAAAa,kDAAA,wBAYW;IACbvC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAyB;IAEvBD,EAAA,CAAA0B,UAAA,IAAAc,kDAAA,wBAqCW;IAGXxC,EAAA,CAAA0B,UAAA,IAAAe,sDAAA,4BA4De;IACjBzC,EAAA,CAAAG,YAAA,EAAM;;;;IAvHuBH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,YAAAsC,MAAA,CAAAC,YAAA,CAAe;IAkBd3C,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAI,UAAA,YAAAsC,MAAA,CAAAE,MAAA,CAAW;IAwCxB5C,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAsC,MAAA,CAAAE,MAAA,CAAAC,MAAA,WAAAH,MAAA,CAAAI,SAAA,CAAuC;;;;;;IAiExD9C,EAAA,CAAAC,cAAA,cAAyE;IAC1CD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sEAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtEH,EAAA,CAAAC,cAAA,iBAAwE;IAA9BD,EAAA,CAAA+C,UAAA,mBAAAC,iEAAA;MAAAhD,EAAA,CAAAiD,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAoD,aAAA;MAAA,OAASpD,EAAA,CAAAqD,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACrEtD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;AD3OnB,MAuBaoD,uBAAuB;EA4LlCC,YACUC,qBAA4C,EAC5CC,WAAwB,EACxBC,gBAAkC,EAClCC,GAAsB;IAHtB,KAAAH,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,GAAG,GAAHA,GAAG;IA/LL,KAAAC,QAAQ,GAAG,IAAIjE,OAAO,EAAQ;IAItC,KAAAkE,QAAQ,GAAU,EAAE;IACpB,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,kBAAkB,GAAG,IAAIxE,WAAW,CAAC,EAAE,CAAC;IACxC,KAAAyE,SAAS,GAAG,IAAIzE,WAAW,CAAC,IAAI0E,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9E,KAAAC,OAAO,GAAG,IAAI5E,WAAW,CAAC,IAAI0E,IAAI,EAAE,CAAC;IACrC,KAAAG,WAAW,GAAG,IAAI7E,WAAW,CAAC,EAAE,CAAC;IACjC,KAAA8E,YAAY,GAAG,IAAI9E,WAAW,CAAC,cAAc,CAAC;IAC9C,KAAA+E,iBAAiB,GAAG,UAAU;IAE9B;IACA,KAAA7B,YAAY,GAAkB,EAAE;IAChC,KAAAC,MAAM,GAAqB,EAAE;IAC7B,KAAAE,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAzB,gBAAgB,GAAkC;MAChDoD,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE,KAAK;UACfC,MAAM,EAAE;YACNC,aAAa,EAAE,IAAI;YACnBC,OAAO,EAAE;;SAEZ;QACDC,OAAO,EAAE;UACPC,IAAI,EAAE,OAAO;UACbC,SAAS,EAAE,KAAK;UAChBC,eAAe,EAAE,iBAAiB;UAClCC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE;;OAEhB;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDd,OAAO,EAAE,IAAI;UACbe,IAAI,EAAE;YACJf,OAAO,EAAE,IAAI;YACbhE,KAAK,EAAE;WACR;UACDoB,KAAK,EAAE;YACL4C,OAAO,EAAE;;SAEZ;QACDgB,CAAC,EAAE;UACDhB,OAAO,EAAE,IAAI;UACbe,IAAI,EAAE;YACJf,OAAO,EAAE,IAAI;YACbhE,KAAK,EAAE;WACR;UACDoB,KAAK,EAAE;YACL4C,OAAO,EAAE;WACV;UACDiB,WAAW,EAAE,IAAI;UACjBC,KAAK,EAAE;YACLC,QAAQ,EAAE,SAAAA,CAAShF,KAAU;cAC3B,OAAO,GAAG,GAAGA,KAAK,CAACiF,cAAc,EAAE;YACrC;;;OAGL;MACDC,WAAW,EAAE;QACXf,IAAI,EAAE,SAAS;QACfgB,IAAI,EAAE,GAAG;QACTf,SAAS,EAAE;;KAEd;IAED,KAAA7D,eAAe,GAAkC;MAC/CkD,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1B0B,SAAS,EAAE,GAAY;MACvBzB,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;SACV;QACDK,OAAO,EAAE;UACPG,eAAe,EAAE,iBAAiB;UAClCC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE,CAAC;UACdY,SAAS,EAAE;YACTpF,KAAK,EAAE,SAAAA,CAASqF,OAAY;cAC1B,OAAO,GAAG,GAAGA,OAAO,CAACC,MAAM,CAACZ,CAAC,CAACM,cAAc,EAAE;YAChD;;;OAGL;MACDP,MAAM,EAAE;QACNC,CAAC,EAAE;UACDG,WAAW,EAAE,IAAI;UACjBF,IAAI,EAAE;YACJf,OAAO,EAAE,IAAI;YACbhE,KAAK,EAAE;WACR;UACDoB,KAAK,EAAE;YACL4C,OAAO,EAAE;WACV;UACDkB,KAAK,EAAE;YACLC,QAAQ,EAAE,SAAAA,CAAShF,KAAU;cAC3B,OAAO,GAAG,GAAGA,KAAK,CAACiF,cAAc,EAAE;YACrC;;SAEH;QACDJ,CAAC,EAAE;UACDD,IAAI,EAAE;YACJf,OAAO,EAAE;WACV;UACD5C,KAAK,EAAE;YACL4C,OAAO,EAAE;;;;KAIhB;IAED,KAAApD,oBAAoB,GAAQ;MAC1BgD,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;YACNC,aAAa,EAAE,IAAI;YACnBC,OAAO,EAAE,EAAE;YACXuB,IAAI,EAAE;cACJC,IAAI,EAAE;;;SAGX;QACDvB,OAAO,EAAE;UACPG,eAAe,EAAE,iBAAiB;UAClCC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE,CAAC;UACdY,SAAS,EAAE;YACTpF,KAAK,EAAE,SAAAA,CAASqF,OAAY;cAC1B,MAAMrF,KAAK,GAAGqF,OAAO,CAACrF,KAAK,IAAI,EAAE;cACjC,MAAMD,KAAK,GAAGsF,OAAO,CAACC,MAAM;cAC5B,MAAMG,KAAK,GAAGJ,OAAO,CAACK,OAAO,CAACC,IAAI,CAACC,MAAM,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;cAC7E,MAAMC,UAAU,GAAG,CAAEhG,KAAK,GAAG0F,KAAK,GAAI,GAAG,EAAEO,OAAO,CAAC,CAAC,CAAC;cACrD,OAAO,GAAGhG,KAAK,KAAK+F,UAAU,GAAG;YACnC;;;OAGL;MACDE,MAAM,EAAE;KACT;IAED,KAAAC,uBAAuB,GAAkC;MACvD1C,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDa,MAAM,EAAE;QACNC,CAAC,EAAE;UACD1D,KAAK,EAAE;YACL4C,OAAO,EAAE,IAAI;YACbuC,IAAI,EAAE;;SAET;QACDvB,CAAC,EAAE;UACDC,WAAW,EAAE,IAAI;UACjB7D,KAAK,EAAE;YACL4C,OAAO,EAAE,IAAI;YACbuC,IAAI,EAAE;;;;KAIb;IAQC,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC3D,WAAW,CAAC4D,cAAc,EAAE;EAC/C;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACnE,iBAAiB,EAAE;EAC1B;EAEAoE,WAAWA,CAAA;IACT,IAAI,CAAC7D,QAAQ,CAAC8D,IAAI,EAAE;IACpB,IAAI,CAAC9D,QAAQ,CAAC+D,QAAQ,EAAE;EAC1B;EAEQJ,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACvD,kBAAkB,CAAC4D,YAAY,CACjCC,IAAI,CACHhI,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAACgE,QAAQ,CAAC,CACzB,CACAkE,SAAS,CAAE/G,KAAoB,IAAI;MAClC,IAAI,CAACgH,cAAc,CAAChH,KAAK,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC;EACN;EAEQyG,YAAYA,CAAA;IAClB,IAAI,CAAC9D,gBAAgB,CAACsE,sBAAsB,CACzCH,IAAI,CAACjI,SAAS,CAAC,IAAI,CAACgE,QAAQ,CAAC,CAAC,CAC9BkE,SAAS,CAACnB,IAAI,IAAG;MAChB,IAAI,CAAC9C,QAAQ,GAAG8C,IAAI,IAAI,EAAE;MAC1B,IAAI,CAAC7C,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;MAE1C,IAAI,IAAI,CAACA,QAAQ,CAACjB,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACmB,gBAAgB,GAAG,IAAI,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACxD,eAAe;;IAE5D,CAAC,CAAC;EACN;EAEQ0H,cAAcA,CAACE,UAAkB;IACvC,IAAI,CAACA,UAAU,EAAE;MACf,IAAI,CAACnE,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;KAC3C,MAAM;MACL,MAAMqE,oBAAoB,GAAGD,UAAU,CAAChG,WAAW,EAAE,CAACkG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxE,IAAI,CAACrE,gBAAgB,GAAG,IAAI,CAACD,QAAQ,CAACuE,MAAM,CAACC,MAAM,IACjDA,MAAM,CAAC7H,UAAU,CAACyB,WAAW,EAAE,CAACkG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACjG,QAAQ,CAACgG,oBAAoB,CAAC,CAClF;;EAEL;EAEA7E,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACY,SAAS,CAAClD,KAAK,IAAI,CAAC,IAAI,CAACqD,OAAO,CAACrD,KAAK,EAAE;MAChD;;IAGF,IAAI,CAAC8B,SAAS,GAAG,IAAI;IAErB,MAAMyF,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACxE,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB,CAAC,GAAG,EAAE;MAC/DE,SAAS,EAAE,IAAI,CAACuE,UAAU,CAAC,IAAI,CAACvE,SAAS,CAAClD,KAAK,CAAC;MAChDqD,OAAO,EAAE,IAAI,CAACoE,UAAU,CAAC,IAAI,CAACpE,OAAO,CAACrD,KAAK,CAAC;MAC5C0H,QAAQ,EAAE,IAAI,CAACnE,YAAY,CAACvD,KAAK,IAAI;KACtC;IAED,MAAM2H,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACvB,IAAI,CAACwB,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE;KACrB;IAED,IAAI,CAACtF,qBAAqB,CAACuF,qBAAqB,CAACL,OAAO,CAAC,CACtDb,IAAI,CAACjI,SAAS,CAAC,IAAI,CAACgE,QAAQ,CAAC,CAAC,CAC9BkE,SAAS,CAAC;MACTJ,IAAI,EAAGsB,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACC,oBAAoB,CAACF,QAAQ,CAACrC,IAAI,CAAC;SACzC,MAAM;UACL;UACA,IAAI,CAACwC,cAAc,EAAE;;QAEvB,IAAI,CAACtG,SAAS,GAAG,KAAK;QACtB,IAAI,CAACc,GAAG,CAACyF,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD;QACA,IAAI,CAACF,cAAc,EAAE;QACrB,IAAI,CAACtG,SAAS,GAAG,KAAK;QACtB,IAAI,CAACc,GAAG,CAACyF,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQD,cAAcA,CAAA;IACpB;IACA,MAAMI,UAAU,GAAG;MACjBC,aAAa,EAAE,CACb;QAAE1I,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE,uBAAuB;QAAEyI,SAAS,EAAE;MAAU,CAAE,EACjG;QAAE3I,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,cAAc;QAAEyI,SAAS,EAAE;MAAQ,CAAE,EACnF;QAAE3I,IAAI,EAAE,aAAa;QAAEC,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE,qBAAqB;QAAEyI,SAAS,EAAE;MAAU,CAAE,EAC7F;QAAE3I,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,YAAY;QAAEC,KAAK,EAAE,YAAY;QAAEyI,SAAS,EAAE;MAAQ,CAAE,CACjF;MACD9G,MAAM,EAAE,CACN;QACE+G,EAAE,EAAE,iBAAiB;QACrB1H,KAAK,EAAE,gCAAgC;QACvCD,IAAI,EAAE,MAAM;QACZ4E,IAAI,EAAE;UACJ7B,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAChD6E,QAAQ,EAAE,CAAC;YACT3I,KAAK,EAAE,uBAAuB;YAC9B2F,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YAClCvB,eAAe,EAAE,CAAC,SAAS,CAAC;YAC5BG,WAAW,EAAE,CAAC,SAAS;WACxB;;OAEJ,EACD;QACEmE,EAAE,EAAE,aAAa;QACjB1H,KAAK,EAAE,gCAAgC;QACvCD,IAAI,EAAE,KAAK;QACX4E,IAAI,EAAE;UACJ7B,MAAM,EAAE,CAAC,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,eAAe,CAAC;UACxF6E,QAAQ,EAAE,CAAC;YACT3I,KAAK,EAAE,uBAAuB;YAC9B2F,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;YAC9CvB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxEG,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;WACpE;;OAEJ,EACD;QACEmE,EAAE,EAAE,mBAAmB;QACvB1H,KAAK,EAAE,qCAAqC;QAC5CD,IAAI,EAAE,UAAU;QAChB4E,IAAI,EAAE;UACJ7B,MAAM,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC;UACjE6E,QAAQ,EAAE,CAAC;YACT3I,KAAK,EAAE,uBAAuB;YAC9B2F,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAC1BvB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxEG,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;WACpE;;OAEJ;KAEJ;IAED,IAAI,CAAC2D,oBAAoB,CAACK,UAAU,CAAC;EACvC;EAEQL,oBAAoBA,CAACvC,IAAS;IACpC;IACA,IAAI,CAACjE,YAAY,GAAGiE,IAAI,CAAC6C,aAAa,EAAEI,GAAG,CAAEC,IAAS,KAAM;MAC1D/I,IAAI,EAAE,IAAI,CAAC0C,qBAAqB,CAACsG,kBAAkB,CAACD,IAAI,CAACJ,SAAS,EAAEI,IAAI,CAAC7I,KAAK,CAAC;MAC/ED,KAAK,EAAE8I,IAAI,CAAC9I,KAAK;MACjBC,KAAK,EAAE6I,IAAI,CAAC7I,KAAK;MACjBJ,KAAK,EAAE,IAAI,CAACmJ,YAAY,CAACF,IAAI,CAACJ,SAAS;KACxC,CAAC,CAAC,IAAI,EAAE;IAET;IACA,IAAI,IAAI,CAAC/G,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACF,YAAY,GAAG,IAAI,CAACsH,yBAAyB,EAAE;;IAGtD;IACA,IAAI,CAACrH,MAAM,GAAGgE,IAAI,CAAChE,MAAM,EAAEiH,GAAG,CAAEK,KAAU,KAAM;MAC9C,GAAGA,KAAK;MACRtD,IAAI,EAAE,IAAI,CAACnD,qBAAqB,CAAC0G,gBAAgB,CAACD,KAAK;KACxD,CAAC,CAAC,IAAI,EAAE;IAET;IACA,IAAI,IAAI,CAACtH,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B0G,OAAO,CAACa,GAAG,CAAC,kCAAkC,CAAC;;EAEnD;EAEQH,yBAAyBA,CAAA;IAC/B,OAAO,CACL;MACElJ,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,uBAAuB;MAC9BJ,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,cAAc;MACrBJ,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,QAAQ;MACfC,KAAK,EAAE,qBAAqB;MAC5BJ,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,YAAY;MACnBJ,KAAK,EAAE;KACR,CACF;EACH;EAEQmJ,YAAYA,CAACK,QAAgB;IACnC,MAAMC,QAAQ,GAA8B;MAC1C,UAAU,EAAE,SAAS;MACrB,QAAQ,EAAE,SAAS;MACnB,YAAY,EAAE,SAAS;MACvB,QAAQ,EAAE;KACX;IACD,OAAOA,QAAQ,CAACD,QAAQ,CAAC,IAAI,SAAS;EACxC;EAEQ5B,UAAUA,CAAC8B,IAAU;IAC3B,OAAOA,IAAI,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACpH,iBAAiB,EAAE;EAC1B;EAEAqH,YAAYA,CAAA;IACV,IAAI,CAACrH,iBAAiB,EAAE;EAC1B;EAEAsH,aAAaA,CAAA;IACX,MAAMC,KAAK,GAAG,IAAI,CAACvG,WAAW,CAACtD,KAAK,EAAE8J,IAAI,EAAE;IAC5C,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,0BAA0B,CAACF,KAAK,CAAC;KACvC,MAAM;MACL,IAAI,CAACvH,iBAAiB,EAAE;;EAE5B;EAEQyH,0BAA0BA,CAACF,KAAa;IAC9C,IAAI,CAAC,IAAI,CAAC3G,SAAS,CAAClD,KAAK,IAAI,CAAC,IAAI,CAACqD,OAAO,CAACrD,KAAK,EAAE;MAChD;;IAGF,IAAI,CAAC8B,SAAS,GAAG,IAAI;IAErB,MAAMyF,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACxE,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB,CAAC,GAAG,EAAE;MAC/DE,SAAS,EAAE,IAAI,CAACuE,UAAU,CAAC,IAAI,CAACvE,SAAS,CAAClD,KAAK,CAAC;MAChDqD,OAAO,EAAE,IAAI,CAACoE,UAAU,CAAC,IAAI,CAACpE,OAAO,CAACrD,KAAK,CAAC;MAC5C0H,QAAQ,EAAE,IAAI,CAACnE,YAAY,CAACvD,KAAK,IAAI;KACtC;IAED,MAAM2H,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACvB,IAAI,CAACwB,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAE+B,KAAK;MACjB9B,kBAAkB,EAAE;KACrB;IAED,IAAI,CAACtF,qBAAqB,CAACuF,qBAAqB,CAACL,OAAO,CAAC,CACtDb,IAAI,CAACjI,SAAS,CAAC,IAAI,CAACgE,QAAQ,CAAC,CAAC,CAC9BkE,SAAS,CAAC;MACTJ,IAAI,EAAGsB,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,IAAID,QAAQ,CAACrC,IAAI,CAAChE,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;UACpE,IAAI,CAACsG,oBAAoB,CAACF,QAAQ,CAACrC,IAAI,CAAC;SACzC,MAAM;UACL;UACA2C,OAAO,CAACa,GAAG,CAAC,mDAAmD,CAAC;UAChE,IAAI,CAAC9G,iBAAiB,EAAE;;QAE1B,IAAI,CAACR,SAAS,GAAG,KAAK;QACtB,IAAI,CAACc,GAAG,CAACyF,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACyB,IAAI,CAAC,sDAAsD,EAAE1B,KAAK,CAAC;QAC3E,IAAI,CAAChG,iBAAiB,EAAE;MAC1B;KACD,CAAC;EACN;EAEAnC,YAAYA,CAAC+I,KAAqB;IAChC,OAAOA,KAAK,CAACtD,IAAI;EACnB;EAEAqE,YAAYA,CAACf,KAAqB;IAChC,OAAOA,KAAK,CAAClI,IAAiB;EAChC;;;uBAjeWuB,uBAAuB,EAAAvD,EAAA,CAAAkL,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAApL,EAAA,CAAAkL,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtL,EAAA,CAAAkL,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAxL,EAAA,CAAAkL,iBAAA,CAAAlL,EAAA,CAAAyL,iBAAA;IAAA;EAAA;;;YAAvBlI,uBAAuB;MAAAmI,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA5L,EAAA,CAAA6L,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3DpCnM,EAAA,CAAAC,cAAA,aAAuC;UAItBD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAC,cAAA,oBAA0C;UAA9BD,EAAA,CAAA+C,UAAA,yBAAAsJ,mEAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA5H,iBAAA,GAAA8H,MAAA;UAAA,EAA6B;UACvCtM,EAAA,CAAAC,cAAA,oBAA6B;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC5DH,EAAA,CAAAC,cAAA,oBAA0B;UAAAD,EAAA,CAAAE,MAAA,sBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtDH,EAAA,CAAAC,cAAA,qBAA8B;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAKpEH,EAAA,CAAAC,cAAA,cAA+B;UAKbD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,uBACA;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIrCH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,sBAAgF;UAApED,EAAA,CAAA+C,UAAA,yBAAAwJ,oEAAAD,MAAA;YAAA,OAAAF,GAAA,CAAApI,gBAAA,GAAAsI,MAAA;UAAA,EAA4B,6BAAAE,wEAAA;YAAA,OAAoBJ,GAAA,CAAA1B,gBAAA,EAAkB;UAAA,EAAtC;UACtC1K,EAAA,CAAAC,cAAA,kBAAY;UACVD,EAAA,CAAAU,SAAA,iCAIwB;UAC1BV,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAA0B,UAAA,KAAA+K,8CAAA,yBAEa;UACfzM,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,sBAA4E;UAAnCD,EAAA,CAAA+C,UAAA,6BAAA2J,wEAAA;YAAA,OAAmBN,GAAA,CAAAzB,YAAA,EAAc;UAAA,EAAC;UACzE3K,EAAA,CAAAC,cAAA,sBAAiC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC3DH,EAAA,CAAAC,cAAA,sBAA8B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACrDH,EAAA,CAAAC,cAAA,sBAAgC;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAM/DH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,iBAAsG;UAA9BD,EAAA,CAAA+C,UAAA,wBAAA4J,8DAAA;YAAA,OAAcP,GAAA,CAAAzB,YAAA,EAAc;UAAA,EAAC;UAArG3K,EAAA,CAAAG,YAAA,EAAsG;UACtGH,EAAA,CAAAU,SAAA,iCAA6E;UAE/EV,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAC,cAAA,iBAAkG;UAA9BD,EAAA,CAAA+C,UAAA,wBAAA6J,8DAAA;YAAA,OAAcR,GAAA,CAAAzB,YAAA,EAAc;UAAA,EAAC;UAAjG3K,EAAA,CAAAG,YAAA,EAAkG;UAClGH,EAAA,CAAAU,SAAA,iCAA2E;UAE7EV,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA4B;UAC2BD,EAAA,CAAA+C,UAAA,mBAAA8J,0DAAA;YAAA,OAAST,GAAA,CAAA9I,iBAAA,EAAmB;UAAA,EAAC;UAChFtD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAMiBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,eAA4B;UACID,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAA+B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI5DH,EAAA,CAAAC,cAAA,eAA0B;UAGTD,EAAA,CAAAE,MAAA,uCAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtDH,EAAA,CAAAC,cAAA,iBAGyC;UAAlCD,EAAA,CAAA+C,UAAA,yBAAA+J,+DAAA;YAAA,OAAeV,GAAA,CAAAxB,aAAA,EAAe;UAAA,EAAC;UAHtC5K,EAAA,CAAAG,YAAA,EAGyC;UACzCH,EAAA,CAAAC,cAAA,oBAAkE;UAA1BD,EAAA,CAAA+C,UAAA,mBAAAgK,4DAAA;YAAA,OAASX,GAAA,CAAAxB,aAAA,EAAe;UAAA,EAAC;UAAC5K,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAQ7FH,EAAA,CAAAC,cAAA,eAAgC;UAE9BD,EAAA,CAAA0B,UAAA,KAAAsL,uCAAA,kBAGM;UAGRhN,EAAA,CAAA0B,UAAA,KAAAuL,uCAAA,kBA2HM;UAGJjN,EAAA,CAAA0B,UAAA,MAAAwL,wCAAA,mBAQM;UACRlN,EAAA,CAAAG,YAAA,EAAM;;;;;UA5QMH,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,UAAAgM,GAAA,CAAA5H,iBAAA,CAA6B;UA0BvBxE,EAAA,CAAAO,SAAA,IAA4B;UAA5BP,EAAA,CAAAI,UAAA,UAAAgM,GAAA,CAAApI,gBAAA,CAA4B;UAGlChE,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,gBAAAgM,GAAA,CAAAnI,kBAAA,CAAkC;UAKPjE,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAAgM,GAAA,CAAArI,gBAAA,CAAmB;UAexC/D,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,gBAAAgM,GAAA,CAAA7H,YAAA,CAA4B;UAgBxBvE,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAI,UAAA,kBAAA+M,GAAA,CAA6B,gBAAAf,GAAA,CAAAlI,SAAA;UACZlE,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,QAAA+M,GAAA,CAAmB;UAapCnN,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,kBAAAgN,GAAA,CAA2B,gBAAAhB,GAAA,CAAA/H,OAAA;UACVrE,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,QAAAgN,GAAA,CAAiB;UAmCvCpN,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,gBAAAgM,GAAA,CAAA9H,WAAA,CAA2B;UAYpCtE,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAgM,GAAA,CAAAtJ,SAAA,CAAe;UAMjB9C,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,UAAAgM,GAAA,CAAAtJ,SAAA,CAAgB;UA8Hd9C,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAI,UAAA,UAAAgM,GAAA,CAAAtJ,SAAA,IAAAsJ,GAAA,CAAAzJ,YAAA,CAAAE,MAAA,OAA6C;;;qBDhOvDjE,YAAY,EAAAyO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ3O,aAAa,EAAA4O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACb/O,eAAe,EAAAgP,EAAA,CAAAC,SAAA,EACfhP,aAAa,EAAAiP,EAAA,CAAAC,OAAA,EACbjP,eAAe,EAAAkP,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,SAAA,EAAAC,EAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACfxP,kBAAkB,EAClBC,cAAc,EAAAwP,GAAA,CAAAC,QAAA,EACdxP,mBAAmB,EAAAyP,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB3P,mBAAmB,EACnBC,gBAAgB,EAChBC,wBAAwB,EAAA0P,GAAA,CAAAC,kBAAA,EACxB1P,cAAc,EAAA2P,GAAA,CAAAC,kBAAA,EACd3P,wBAAwB,EAAA4P,GAAA,CAAAC,wBAAA,EACxB3P,mBAAmB,EAAA4P,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,oBAAA,EACnB9P,WAAW;MAAA+P,MAAA;IAAA;EAAA;;SAKFnM,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}