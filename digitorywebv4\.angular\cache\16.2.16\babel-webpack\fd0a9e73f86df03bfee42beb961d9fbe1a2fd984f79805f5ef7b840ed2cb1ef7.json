{"ast": null, "code": "'use strict';\n\nvar normalizeReference = require('../common/utils').normalizeReference;\nvar isSpace = require('../common/utils').isSpace;\nmodule.exports = function reference(state, startLine, _endLine, silent) {\n  var ch,\n    destEndPos,\n    destEndLineNo,\n    endLine,\n    href,\n    i,\n    l,\n    label,\n    labelEnd,\n    oldParentType,\n    res,\n    start,\n    str,\n    terminate,\n    terminatorRules,\n    title,\n    lines = 0,\n    pos = state.bMarks[startLine] + state.tShift[startLine],\n    max = state.eMarks[startLine],\n    nextLine = startLine + 1;\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) {\n    return false;\n  }\n  if (state.src.charCodeAt(pos) !== 0x5B /* [ */) {\n    return false;\n  }\n\n  // Simple check to quickly interrupt scan on [link](url) at the start of line.\n  // Can be useful on practice: https://github.com/markdown-it/markdown-it/issues/54\n  while (++pos < max) {\n    if (state.src.charCodeAt(pos) === 0x5D /* ] */ && state.src.charCodeAt(pos - 1) !== 0x5C /* \\ */) {\n      if (pos + 1 === max) {\n        return false;\n      }\n      if (state.src.charCodeAt(pos + 1) !== 0x3A /* : */) {\n        return false;\n      }\n      break;\n    }\n  }\n  endLine = state.lineMax;\n\n  // jump line-by-line until empty one or EOF\n  terminatorRules = state.md.block.ruler.getRules('reference');\n  oldParentType = state.parentType;\n  state.parentType = 'reference';\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) {\n      continue;\n    }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) {\n      continue;\n    }\n\n    // Some tags can terminate paragraph without empty line.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) {\n      break;\n    }\n  }\n  str = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n  max = str.length;\n  for (pos = 1; pos < max; pos++) {\n    ch = str.charCodeAt(pos);\n    if (ch === 0x5B /* [ */) {\n      return false;\n    } else if (ch === 0x5D /* ] */) {\n      labelEnd = pos;\n      break;\n    } else if (ch === 0x0A /* \\n */) {\n      lines++;\n    } else if (ch === 0x5C /* \\ */) {\n      pos++;\n      if (pos < max && str.charCodeAt(pos) === 0x0A) {\n        lines++;\n      }\n    }\n  }\n  if (labelEnd < 0 || str.charCodeAt(labelEnd + 1) !== 0x3A /* : */) {\n    return false;\n  }\n\n  // [label]:   destination   'title'\n  //         ^^^ skip optional whitespace here\n  for (pos = labelEnd + 2; pos < max; pos++) {\n    ch = str.charCodeAt(pos);\n    if (ch === 0x0A) {\n      lines++;\n    } else if (isSpace(ch)) {\n      /*eslint no-empty:0*/\n    } else {\n      break;\n    }\n  }\n\n  // [label]:   destination   'title'\n  //            ^^^^^^^^^^^ parse this\n  res = state.md.helpers.parseLinkDestination(str, pos, max);\n  if (!res.ok) {\n    return false;\n  }\n  href = state.md.normalizeLink(res.str);\n  if (!state.md.validateLink(href)) {\n    return false;\n  }\n  pos = res.pos;\n  lines += res.lines;\n\n  // save cursor state, we could require to rollback later\n  destEndPos = pos;\n  destEndLineNo = lines;\n\n  // [label]:   destination   'title'\n  //                       ^^^ skipping those spaces\n  start = pos;\n  for (; pos < max; pos++) {\n    ch = str.charCodeAt(pos);\n    if (ch === 0x0A) {\n      lines++;\n    } else if (isSpace(ch)) {\n      /*eslint no-empty:0*/\n    } else {\n      break;\n    }\n  }\n\n  // [label]:   destination   'title'\n  //                          ^^^^^^^ parse this\n  res = state.md.helpers.parseLinkTitle(str, pos, max);\n  if (pos < max && start !== pos && res.ok) {\n    title = res.str;\n    pos = res.pos;\n    lines += res.lines;\n  } else {\n    title = '';\n    pos = destEndPos;\n    lines = destEndLineNo;\n  }\n\n  // skip trailing spaces until the rest of the line\n  while (pos < max) {\n    ch = str.charCodeAt(pos);\n    if (!isSpace(ch)) {\n      break;\n    }\n    pos++;\n  }\n  if (pos < max && str.charCodeAt(pos) !== 0x0A) {\n    if (title) {\n      // garbage at the end of the line after title,\n      // but it could still be a valid reference if we roll back\n      title = '';\n      pos = destEndPos;\n      lines = destEndLineNo;\n      while (pos < max) {\n        ch = str.charCodeAt(pos);\n        if (!isSpace(ch)) {\n          break;\n        }\n        pos++;\n      }\n    }\n  }\n  if (pos < max && str.charCodeAt(pos) !== 0x0A) {\n    // garbage at the end of the line\n    return false;\n  }\n  label = normalizeReference(str.slice(1, labelEnd));\n  if (!label) {\n    // CommonMark 0.20 disallows empty labels\n    return false;\n  }\n\n  // Reference can not terminate anything. This check is for safety only.\n  /*istanbul ignore if*/\n  if (silent) {\n    return true;\n  }\n  if (typeof state.env.references === 'undefined') {\n    state.env.references = {};\n  }\n  if (typeof state.env.references[label] === 'undefined') {\n    state.env.references[label] = {\n      title: title,\n      href: href\n    };\n  }\n  state.parentType = oldParentType;\n  state.line = startLine + lines + 1;\n  return true;\n};", "map": {"version": 3, "names": ["normalizeReference", "require", "isSpace", "module", "exports", "reference", "state", "startLine", "_endLine", "silent", "ch", "destEndPos", "destEndLineNo", "endLine", "href", "i", "l", "label", "labelEnd", "oldParentType", "res", "start", "str", "terminate", "terminatorRules", "title", "lines", "pos", "bMarks", "tShift", "max", "eMarks", "nextLine", "sCount", "blkIndent", "src", "charCodeAt", "lineMax", "md", "block", "ruler", "getRules", "parentType", "isEmpty", "length", "getLines", "trim", "helpers", "parseLinkDestination", "ok", "normalizeLink", "validateLink", "parseLinkTitle", "slice", "env", "references", "line"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_block/reference.js"], "sourcesContent": ["'use strict';\n\n\nvar normalizeReference   = require('../common/utils').normalizeReference;\nvar isSpace              = require('../common/utils').isSpace;\n\n\nmodule.exports = function reference(state, startLine, _endLine, silent) {\n  var ch,\n      destEndPos,\n      destEndLineNo,\n      endLine,\n      href,\n      i,\n      l,\n      label,\n      labelEnd,\n      oldParentType,\n      res,\n      start,\n      str,\n      terminate,\n      terminatorRules,\n      title,\n      lines = 0,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine],\n      nextLine = startLine + 1;\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  if (state.src.charCodeAt(pos) !== 0x5B/* [ */) { return false; }\n\n  // Simple check to quickly interrupt scan on [link](url) at the start of line.\n  // Can be useful on practice: https://github.com/markdown-it/markdown-it/issues/54\n  while (++pos < max) {\n    if (state.src.charCodeAt(pos) === 0x5D /* ] */ &&\n        state.src.charCodeAt(pos - 1) !== 0x5C/* \\ */) {\n      if (pos + 1 === max) { return false; }\n      if (state.src.charCodeAt(pos + 1) !== 0x3A/* : */) { return false; }\n      break;\n    }\n  }\n\n  endLine = state.lineMax;\n\n  // jump line-by-line until empty one or EOF\n  terminatorRules = state.md.block.ruler.getRules('reference');\n\n  oldParentType = state.parentType;\n  state.parentType = 'reference';\n\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { continue; }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { continue; }\n\n    // Some tags can terminate paragraph without empty line.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) { break; }\n  }\n\n  str = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n  max = str.length;\n\n  for (pos = 1; pos < max; pos++) {\n    ch = str.charCodeAt(pos);\n    if (ch === 0x5B /* [ */) {\n      return false;\n    } else if (ch === 0x5D /* ] */) {\n      labelEnd = pos;\n      break;\n    } else if (ch === 0x0A /* \\n */) {\n      lines++;\n    } else if (ch === 0x5C /* \\ */) {\n      pos++;\n      if (pos < max && str.charCodeAt(pos) === 0x0A) {\n        lines++;\n      }\n    }\n  }\n\n  if (labelEnd < 0 || str.charCodeAt(labelEnd + 1) !== 0x3A/* : */) { return false; }\n\n  // [label]:   destination   'title'\n  //         ^^^ skip optional whitespace here\n  for (pos = labelEnd + 2; pos < max; pos++) {\n    ch = str.charCodeAt(pos);\n    if (ch === 0x0A) {\n      lines++;\n    } else if (isSpace(ch)) {\n      /*eslint no-empty:0*/\n    } else {\n      break;\n    }\n  }\n\n  // [label]:   destination   'title'\n  //            ^^^^^^^^^^^ parse this\n  res = state.md.helpers.parseLinkDestination(str, pos, max);\n  if (!res.ok) { return false; }\n\n  href = state.md.normalizeLink(res.str);\n  if (!state.md.validateLink(href)) { return false; }\n\n  pos = res.pos;\n  lines += res.lines;\n\n  // save cursor state, we could require to rollback later\n  destEndPos = pos;\n  destEndLineNo = lines;\n\n  // [label]:   destination   'title'\n  //                       ^^^ skipping those spaces\n  start = pos;\n  for (; pos < max; pos++) {\n    ch = str.charCodeAt(pos);\n    if (ch === 0x0A) {\n      lines++;\n    } else if (isSpace(ch)) {\n      /*eslint no-empty:0*/\n    } else {\n      break;\n    }\n  }\n\n  // [label]:   destination   'title'\n  //                          ^^^^^^^ parse this\n  res = state.md.helpers.parseLinkTitle(str, pos, max);\n  if (pos < max && start !== pos && res.ok) {\n    title = res.str;\n    pos = res.pos;\n    lines += res.lines;\n  } else {\n    title = '';\n    pos = destEndPos;\n    lines = destEndLineNo;\n  }\n\n  // skip trailing spaces until the rest of the line\n  while (pos < max) {\n    ch = str.charCodeAt(pos);\n    if (!isSpace(ch)) { break; }\n    pos++;\n  }\n\n  if (pos < max && str.charCodeAt(pos) !== 0x0A) {\n    if (title) {\n      // garbage at the end of the line after title,\n      // but it could still be a valid reference if we roll back\n      title = '';\n      pos = destEndPos;\n      lines = destEndLineNo;\n      while (pos < max) {\n        ch = str.charCodeAt(pos);\n        if (!isSpace(ch)) { break; }\n        pos++;\n      }\n    }\n  }\n\n  if (pos < max && str.charCodeAt(pos) !== 0x0A) {\n    // garbage at the end of the line\n    return false;\n  }\n\n  label = normalizeReference(str.slice(1, labelEnd));\n  if (!label) {\n    // CommonMark 0.20 disallows empty labels\n    return false;\n  }\n\n  // Reference can not terminate anything. This check is for safety only.\n  /*istanbul ignore if*/\n  if (silent) { return true; }\n\n  if (typeof state.env.references === 'undefined') {\n    state.env.references = {};\n  }\n  if (typeof state.env.references[label] === 'undefined') {\n    state.env.references[label] = { title: title, href: href };\n  }\n\n  state.parentType = oldParentType;\n\n  state.line = startLine + lines + 1;\n  return true;\n};\n"], "mappings": "AAAA,YAAY;;AAGZ,IAAIA,kBAAkB,GAAKC,OAAO,CAAC,iBAAiB,CAAC,CAACD,kBAAkB;AACxE,IAAIE,OAAO,GAAgBD,OAAO,CAAC,iBAAiB,CAAC,CAACC,OAAO;AAG7DC,MAAM,CAACC,OAAO,GAAG,SAASC,SAASA,CAACC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACtE,IAAIC,EAAE;IACFC,UAAU;IACVC,aAAa;IACbC,OAAO;IACPC,IAAI;IACJC,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,QAAQ;IACRC,aAAa;IACbC,GAAG;IACHC,KAAK;IACLC,GAAG;IACHC,SAAS;IACTC,eAAe;IACfC,KAAK;IACLC,KAAK,GAAG,CAAC;IACTC,GAAG,GAAGrB,KAAK,CAACsB,MAAM,CAACrB,SAAS,CAAC,GAAGD,KAAK,CAACuB,MAAM,CAACtB,SAAS,CAAC;IACvDuB,GAAG,GAAGxB,KAAK,CAACyB,MAAM,CAACxB,SAAS,CAAC;IAC7ByB,QAAQ,GAAGzB,SAAS,GAAG,CAAC;;EAE5B;EACA,IAAID,KAAK,CAAC2B,MAAM,CAAC1B,SAAS,CAAC,GAAGD,KAAK,CAAC4B,SAAS,IAAI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEpE,IAAI5B,KAAK,CAAC6B,GAAG,CAACC,UAAU,CAACT,GAAG,CAAC,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;;EAE/D;EACA;EACA,OAAO,EAAEA,GAAG,GAAGG,GAAG,EAAE;IAClB,IAAIxB,KAAK,CAAC6B,GAAG,CAACC,UAAU,CAACT,GAAG,CAAC,KAAK,IAAI,CAAC,WACnCrB,KAAK,CAAC6B,GAAG,CAACC,UAAU,CAACT,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS;MACjD,IAAIA,GAAG,GAAG,CAAC,KAAKG,GAAG,EAAE;QAAE,OAAO,KAAK;MAAE;MACrC,IAAIxB,KAAK,CAAC6B,GAAG,CAACC,UAAU,CAACT,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS;QAAE,OAAO,KAAK;MAAE;MACnE;IACF;EACF;EAEAd,OAAO,GAAGP,KAAK,CAAC+B,OAAO;;EAEvB;EACAb,eAAe,GAAGlB,KAAK,CAACgC,EAAE,CAACC,KAAK,CAACC,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC;EAE5DtB,aAAa,GAAGb,KAAK,CAACoC,UAAU;EAChCpC,KAAK,CAACoC,UAAU,GAAG,WAAW;EAE9B,OAAOV,QAAQ,GAAGnB,OAAO,IAAI,CAACP,KAAK,CAACqC,OAAO,CAACX,QAAQ,CAAC,EAAEA,QAAQ,EAAE,EAAE;IACjE;IACA;IACA,IAAI1B,KAAK,CAAC2B,MAAM,CAACD,QAAQ,CAAC,GAAG1B,KAAK,CAAC4B,SAAS,GAAG,CAAC,EAAE;MAAE;IAAU;;IAE9D;IACA,IAAI5B,KAAK,CAAC2B,MAAM,CAACD,QAAQ,CAAC,GAAG,CAAC,EAAE;MAAE;IAAU;;IAE5C;IACAT,SAAS,GAAG,KAAK;IACjB,KAAKR,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGQ,eAAe,CAACoB,MAAM,EAAE7B,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAClD,IAAIS,eAAe,CAACT,CAAC,CAAC,CAACT,KAAK,EAAE0B,QAAQ,EAAEnB,OAAO,EAAE,IAAI,CAAC,EAAE;QACtDU,SAAS,GAAG,IAAI;QAChB;MACF;IACF;IACA,IAAIA,SAAS,EAAE;MAAE;IAAO;EAC1B;EAEAD,GAAG,GAAGhB,KAAK,CAACuC,QAAQ,CAACtC,SAAS,EAAEyB,QAAQ,EAAE1B,KAAK,CAAC4B,SAAS,EAAE,KAAK,CAAC,CAACY,IAAI,CAAC,CAAC;EACxEhB,GAAG,GAAGR,GAAG,CAACsB,MAAM;EAEhB,KAAKjB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGG,GAAG,EAAEH,GAAG,EAAE,EAAE;IAC9BjB,EAAE,GAAGY,GAAG,CAACc,UAAU,CAACT,GAAG,CAAC;IACxB,IAAIjB,EAAE,KAAK,IAAI,CAAC,SAAS;MACvB,OAAO,KAAK;IACd,CAAC,MAAM,IAAIA,EAAE,KAAK,IAAI,CAAC,SAAS;MAC9BQ,QAAQ,GAAGS,GAAG;MACd;IACF,CAAC,MAAM,IAAIjB,EAAE,KAAK,IAAI,CAAC,UAAU;MAC/BgB,KAAK,EAAE;IACT,CAAC,MAAM,IAAIhB,EAAE,KAAK,IAAI,CAAC,SAAS;MAC9BiB,GAAG,EAAE;MACL,IAAIA,GAAG,GAAGG,GAAG,IAAIR,GAAG,CAACc,UAAU,CAACT,GAAG,CAAC,KAAK,IAAI,EAAE;QAC7CD,KAAK,EAAE;MACT;IACF;EACF;EAEA,IAAIR,QAAQ,GAAG,CAAC,IAAII,GAAG,CAACc,UAAU,CAAClB,QAAQ,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;;EAElF;EACA;EACA,KAAKS,GAAG,GAAGT,QAAQ,GAAG,CAAC,EAAES,GAAG,GAAGG,GAAG,EAAEH,GAAG,EAAE,EAAE;IACzCjB,EAAE,GAAGY,GAAG,CAACc,UAAU,CAACT,GAAG,CAAC;IACxB,IAAIjB,EAAE,KAAK,IAAI,EAAE;MACfgB,KAAK,EAAE;IACT,CAAC,MAAM,IAAIxB,OAAO,CAACQ,EAAE,CAAC,EAAE;MACtB;IAAA,CACD,MAAM;MACL;IACF;EACF;;EAEA;EACA;EACAU,GAAG,GAAGd,KAAK,CAACgC,EAAE,CAACS,OAAO,CAACC,oBAAoB,CAAC1B,GAAG,EAAEK,GAAG,EAAEG,GAAG,CAAC;EAC1D,IAAI,CAACV,GAAG,CAAC6B,EAAE,EAAE;IAAE,OAAO,KAAK;EAAE;EAE7BnC,IAAI,GAAGR,KAAK,CAACgC,EAAE,CAACY,aAAa,CAAC9B,GAAG,CAACE,GAAG,CAAC;EACtC,IAAI,CAAChB,KAAK,CAACgC,EAAE,CAACa,YAAY,CAACrC,IAAI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAElDa,GAAG,GAAGP,GAAG,CAACO,GAAG;EACbD,KAAK,IAAIN,GAAG,CAACM,KAAK;;EAElB;EACAf,UAAU,GAAGgB,GAAG;EAChBf,aAAa,GAAGc,KAAK;;EAErB;EACA;EACAL,KAAK,GAAGM,GAAG;EACX,OAAOA,GAAG,GAAGG,GAAG,EAAEH,GAAG,EAAE,EAAE;IACvBjB,EAAE,GAAGY,GAAG,CAACc,UAAU,CAACT,GAAG,CAAC;IACxB,IAAIjB,EAAE,KAAK,IAAI,EAAE;MACfgB,KAAK,EAAE;IACT,CAAC,MAAM,IAAIxB,OAAO,CAACQ,EAAE,CAAC,EAAE;MACtB;IAAA,CACD,MAAM;MACL;IACF;EACF;;EAEA;EACA;EACAU,GAAG,GAAGd,KAAK,CAACgC,EAAE,CAACS,OAAO,CAACK,cAAc,CAAC9B,GAAG,EAAEK,GAAG,EAAEG,GAAG,CAAC;EACpD,IAAIH,GAAG,GAAGG,GAAG,IAAIT,KAAK,KAAKM,GAAG,IAAIP,GAAG,CAAC6B,EAAE,EAAE;IACxCxB,KAAK,GAAGL,GAAG,CAACE,GAAG;IACfK,GAAG,GAAGP,GAAG,CAACO,GAAG;IACbD,KAAK,IAAIN,GAAG,CAACM,KAAK;EACpB,CAAC,MAAM;IACLD,KAAK,GAAG,EAAE;IACVE,GAAG,GAAGhB,UAAU;IAChBe,KAAK,GAAGd,aAAa;EACvB;;EAEA;EACA,OAAOe,GAAG,GAAGG,GAAG,EAAE;IAChBpB,EAAE,GAAGY,GAAG,CAACc,UAAU,CAACT,GAAG,CAAC;IACxB,IAAI,CAACzB,OAAO,CAACQ,EAAE,CAAC,EAAE;MAAE;IAAO;IAC3BiB,GAAG,EAAE;EACP;EAEA,IAAIA,GAAG,GAAGG,GAAG,IAAIR,GAAG,CAACc,UAAU,CAACT,GAAG,CAAC,KAAK,IAAI,EAAE;IAC7C,IAAIF,KAAK,EAAE;MACT;MACA;MACAA,KAAK,GAAG,EAAE;MACVE,GAAG,GAAGhB,UAAU;MAChBe,KAAK,GAAGd,aAAa;MACrB,OAAOe,GAAG,GAAGG,GAAG,EAAE;QAChBpB,EAAE,GAAGY,GAAG,CAACc,UAAU,CAACT,GAAG,CAAC;QACxB,IAAI,CAACzB,OAAO,CAACQ,EAAE,CAAC,EAAE;UAAE;QAAO;QAC3BiB,GAAG,EAAE;MACP;IACF;EACF;EAEA,IAAIA,GAAG,GAAGG,GAAG,IAAIR,GAAG,CAACc,UAAU,CAACT,GAAG,CAAC,KAAK,IAAI,EAAE;IAC7C;IACA,OAAO,KAAK;EACd;EAEAV,KAAK,GAAGjB,kBAAkB,CAACsB,GAAG,CAAC+B,KAAK,CAAC,CAAC,EAAEnC,QAAQ,CAAC,CAAC;EAClD,IAAI,CAACD,KAAK,EAAE;IACV;IACA,OAAO,KAAK;EACd;;EAEA;EACA;EACA,IAAIR,MAAM,EAAE;IAAE,OAAO,IAAI;EAAE;EAE3B,IAAI,OAAOH,KAAK,CAACgD,GAAG,CAACC,UAAU,KAAK,WAAW,EAAE;IAC/CjD,KAAK,CAACgD,GAAG,CAACC,UAAU,GAAG,CAAC,CAAC;EAC3B;EACA,IAAI,OAAOjD,KAAK,CAACgD,GAAG,CAACC,UAAU,CAACtC,KAAK,CAAC,KAAK,WAAW,EAAE;IACtDX,KAAK,CAACgD,GAAG,CAACC,UAAU,CAACtC,KAAK,CAAC,GAAG;MAAEQ,KAAK,EAAEA,KAAK;MAAEX,IAAI,EAAEA;IAAK,CAAC;EAC5D;EAEAR,KAAK,CAACoC,UAAU,GAAGvB,aAAa;EAEhCb,KAAK,CAACkD,IAAI,GAAGjD,SAAS,GAAGmB,KAAK,GAAG,CAAC;EAClC,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}