{"ast": null, "code": "import baseWrapperValue from './_baseWrapperValue.js';\nimport getView from './_getView.js';\nimport isArray from './isArray.js';\n\n/** Used to indicate the type of lazy iteratees. */\nvar LAZY_FILTER_FLAG = 1,\n  LAZY_MAP_FLAG = 2;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Extracts the unwrapped value from its lazy wrapper.\n *\n * @private\n * @name value\n * @memberOf LazyWrapper\n * @returns {*} Returns the unwrapped value.\n */\nfunction lazyValue() {\n  var array = this.__wrapped__.value(),\n    dir = this.__dir__,\n    isArr = isArray(array),\n    isRight = dir < 0,\n    arrLength = isArr ? array.length : 0,\n    view = getView(0, arrLength, this.__views__),\n    start = view.start,\n    end = view.end,\n    length = end - start,\n    index = isRight ? end : start - 1,\n    iteratees = this.__iteratees__,\n    iterLength = iteratees.length,\n    resIndex = 0,\n    takeCount = nativeMin(length, this.__takeCount__);\n  if (!isArr || !isRight && arrLength == length && takeCount == length) {\n    return baseWrapperValue(array, this.__actions__);\n  }\n  var result = [];\n  outer: while (length-- && resIndex < takeCount) {\n    index += dir;\n    var iterIndex = -1,\n      value = array[index];\n    while (++iterIndex < iterLength) {\n      var data = iteratees[iterIndex],\n        iteratee = data.iteratee,\n        type = data.type,\n        computed = iteratee(value);\n      if (type == LAZY_MAP_FLAG) {\n        value = computed;\n      } else if (!computed) {\n        if (type == LAZY_FILTER_FLAG) {\n          continue outer;\n        } else {\n          break outer;\n        }\n      }\n    }\n    result[resIndex++] = value;\n  }\n  return result;\n}\nexport default lazyValue;", "map": {"version": 3, "names": ["baseWrapperValue", "get<PERSON>iew", "isArray", "LAZY_FILTER_FLAG", "LAZY_MAP_FLAG", "nativeMin", "Math", "min", "lazyValue", "array", "__wrapped__", "value", "dir", "__dir__", "isArr", "isRight", "arr<PERSON><PERSON><PERSON>", "length", "view", "__views__", "start", "end", "index", "iteratees", "__iteratees__", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resIndex", "takeCount", "__takeCount__", "__actions__", "result", "outer", "iterIndex", "data", "iteratee", "type", "computed"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/lodash-es/_lazyValue.js"], "sourcesContent": ["import baseWrapperValue from './_baseWrapperValue.js';\nimport getView from './_getView.js';\nimport isArray from './isArray.js';\n\n/** Used to indicate the type of lazy iteratees. */\nvar LAZY_FILTER_FLAG = 1,\n    LAZY_MAP_FLAG = 2;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Extracts the unwrapped value from its lazy wrapper.\n *\n * @private\n * @name value\n * @memberOf LazyWrapper\n * @returns {*} Returns the unwrapped value.\n */\nfunction lazyValue() {\n  var array = this.__wrapped__.value(),\n      dir = this.__dir__,\n      isArr = isArray(array),\n      isRight = dir < 0,\n      arrLength = isArr ? array.length : 0,\n      view = getView(0, arrLength, this.__views__),\n      start = view.start,\n      end = view.end,\n      length = end - start,\n      index = isRight ? end : (start - 1),\n      iteratees = this.__iteratees__,\n      iterLength = iteratees.length,\n      resIndex = 0,\n      takeCount = nativeMin(length, this.__takeCount__);\n\n  if (!isArr || (!isRight && arrLength == length && takeCount == length)) {\n    return baseWrapperValue(array, this.__actions__);\n  }\n  var result = [];\n\n  outer:\n  while (length-- && resIndex < takeCount) {\n    index += dir;\n\n    var iterIndex = -1,\n        value = array[index];\n\n    while (++iterIndex < iterLength) {\n      var data = iteratees[iterIndex],\n          iteratee = data.iteratee,\n          type = data.type,\n          computed = iteratee(value);\n\n      if (type == LAZY_MAP_FLAG) {\n        value = computed;\n      } else if (!computed) {\n        if (type == LAZY_FILTER_FLAG) {\n          continue outer;\n        } else {\n          break outer;\n        }\n      }\n    }\n    result[resIndex++] = value;\n  }\n  return result;\n}\n\nexport default lazyValue;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA,IAAIC,gBAAgB,GAAG,CAAC;EACpBC,aAAa,GAAG,CAAC;;AAErB;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAA,EAAG;EACnB,IAAIC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACC,KAAK,CAAC,CAAC;IAChCC,GAAG,GAAG,IAAI,CAACC,OAAO;IAClBC,KAAK,GAAGZ,OAAO,CAACO,KAAK,CAAC;IACtBM,OAAO,GAAGH,GAAG,GAAG,CAAC;IACjBI,SAAS,GAAGF,KAAK,GAAGL,KAAK,CAACQ,MAAM,GAAG,CAAC;IACpCC,IAAI,GAAGjB,OAAO,CAAC,CAAC,EAAEe,SAAS,EAAE,IAAI,CAACG,SAAS,CAAC;IAC5CC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,GAAG,GAAGH,IAAI,CAACG,GAAG;IACdJ,MAAM,GAAGI,GAAG,GAAGD,KAAK;IACpBE,KAAK,GAAGP,OAAO,GAAGM,GAAG,GAAID,KAAK,GAAG,CAAE;IACnCG,SAAS,GAAG,IAAI,CAACC,aAAa;IAC9BC,UAAU,GAAGF,SAAS,CAACN,MAAM;IAC7BS,QAAQ,GAAG,CAAC;IACZC,SAAS,GAAGtB,SAAS,CAACY,MAAM,EAAE,IAAI,CAACW,aAAa,CAAC;EAErD,IAAI,CAACd,KAAK,IAAK,CAACC,OAAO,IAAIC,SAAS,IAAIC,MAAM,IAAIU,SAAS,IAAIV,MAAO,EAAE;IACtE,OAAOjB,gBAAgB,CAACS,KAAK,EAAE,IAAI,CAACoB,WAAW,CAAC;EAClD;EACA,IAAIC,MAAM,GAAG,EAAE;EAEfC,KAAK,EACL,OAAOd,MAAM,EAAE,IAAIS,QAAQ,GAAGC,SAAS,EAAE;IACvCL,KAAK,IAAIV,GAAG;IAEZ,IAAIoB,SAAS,GAAG,CAAC,CAAC;MACdrB,KAAK,GAAGF,KAAK,CAACa,KAAK,CAAC;IAExB,OAAO,EAAEU,SAAS,GAAGP,UAAU,EAAE;MAC/B,IAAIQ,IAAI,GAAGV,SAAS,CAACS,SAAS,CAAC;QAC3BE,QAAQ,GAAGD,IAAI,CAACC,QAAQ;QACxBC,IAAI,GAAGF,IAAI,CAACE,IAAI;QAChBC,QAAQ,GAAGF,QAAQ,CAACvB,KAAK,CAAC;MAE9B,IAAIwB,IAAI,IAAI/B,aAAa,EAAE;QACzBO,KAAK,GAAGyB,QAAQ;MAClB,CAAC,MAAM,IAAI,CAACA,QAAQ,EAAE;QACpB,IAAID,IAAI,IAAIhC,gBAAgB,EAAE;UAC5B,SAAS4B,KAAK;QAChB,CAAC,MAAM;UACL,MAAMA,KAAK;QACb;MACF;IACF;IACAD,MAAM,CAACJ,QAAQ,EAAE,CAAC,GAAGf,KAAK;EAC5B;EACA,OAAOmB,MAAM;AACf;AAEA,eAAetB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}