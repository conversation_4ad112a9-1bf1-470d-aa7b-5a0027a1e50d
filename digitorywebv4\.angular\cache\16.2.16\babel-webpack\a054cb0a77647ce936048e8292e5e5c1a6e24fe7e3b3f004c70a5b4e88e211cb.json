{"ast": null, "code": "// ~~strike through~~\n//\n'use strict';\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nmodule.exports.tokenize = function strikethrough(state, silent) {\n  var i,\n    scanned,\n    token,\n    len,\n    ch,\n    start = state.pos,\n    marker = state.src.charCodeAt(start);\n  if (silent) {\n    return false;\n  }\n  if (marker !== 0x7E /* ~ */) {\n    return false;\n  }\n  scanned = state.scanDelims(state.pos, true);\n  len = scanned.length;\n  ch = String.fromCharCode(marker);\n  if (len < 2) {\n    return false;\n  }\n  if (len % 2) {\n    token = state.push('text', '', 0);\n    token.content = ch;\n    len--;\n  }\n  for (i = 0; i < len; i += 2) {\n    token = state.push('text', '', 0);\n    token.content = ch + ch;\n    state.delimiters.push({\n      marker: marker,\n      length: 0,\n      // disable \"rule of 3\" length checks meant for emphasis\n      token: state.tokens.length - 1,\n      end: -1,\n      open: scanned.can_open,\n      close: scanned.can_close\n    });\n  }\n  state.pos += scanned.length;\n  return true;\n};\nfunction postProcess(state, delimiters) {\n  var i,\n    j,\n    startDelim,\n    endDelim,\n    token,\n    loneMarkers = [],\n    max = delimiters.length;\n  for (i = 0; i < max; i++) {\n    startDelim = delimiters[i];\n    if (startDelim.marker !== 0x7E /* ~ */) {\n      continue;\n    }\n    if (startDelim.end === -1) {\n      continue;\n    }\n    endDelim = delimiters[startDelim.end];\n    token = state.tokens[startDelim.token];\n    token.type = 's_open';\n    token.tag = 's';\n    token.nesting = 1;\n    token.markup = '~~';\n    token.content = '';\n    token = state.tokens[endDelim.token];\n    token.type = 's_close';\n    token.tag = 's';\n    token.nesting = -1;\n    token.markup = '~~';\n    token.content = '';\n    if (state.tokens[endDelim.token - 1].type === 'text' && state.tokens[endDelim.token - 1].content === '~') {\n      loneMarkers.push(endDelim.token - 1);\n    }\n  }\n\n  // If a marker sequence has an odd number of characters, it's splitted\n  // like this: `~~~~~` -> `~` + `~~` + `~~`, leaving one marker at the\n  // start of the sequence.\n  //\n  // So, we have to move all those markers after subsequent s_close tags.\n  //\n  while (loneMarkers.length) {\n    i = loneMarkers.pop();\n    j = i + 1;\n    while (j < state.tokens.length && state.tokens[j].type === 's_close') {\n      j++;\n    }\n    j--;\n    if (i !== j) {\n      token = state.tokens[j];\n      state.tokens[j] = state.tokens[i];\n      state.tokens[i] = token;\n    }\n  }\n}\n\n// Walk through delimiter list and replace text tokens with tags\n//\nmodule.exports.postProcess = function strikethrough(state) {\n  var curr,\n    tokens_meta = state.tokens_meta,\n    max = state.tokens_meta.length;\n  postProcess(state, state.delimiters);\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters);\n    }\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "tokenize", "strikethrough", "state", "silent", "i", "scanned", "token", "len", "ch", "start", "pos", "marker", "src", "charCodeAt", "scanDelims", "length", "String", "fromCharCode", "push", "content", "delimiters", "tokens", "end", "open", "can_open", "close", "can_close", "postProcess", "j", "start<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loneMarkers", "max", "type", "tag", "nesting", "markup", "pop", "curr", "tokens_meta"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_inline/strikethrough.js"], "sourcesContent": ["// ~~strike through~~\n//\n'use strict';\n\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nmodule.exports.tokenize = function strikethrough(state, silent) {\n  var i, scanned, token, len, ch,\n      start = state.pos,\n      marker = state.src.charCodeAt(start);\n\n  if (silent) { return false; }\n\n  if (marker !== 0x7E/* ~ */) { return false; }\n\n  scanned = state.scanDelims(state.pos, true);\n  len = scanned.length;\n  ch = String.fromCharCode(marker);\n\n  if (len < 2) { return false; }\n\n  if (len % 2) {\n    token         = state.push('text', '', 0);\n    token.content = ch;\n    len--;\n  }\n\n  for (i = 0; i < len; i += 2) {\n    token         = state.push('text', '', 0);\n    token.content = ch + ch;\n\n    state.delimiters.push({\n      marker: marker,\n      length: 0,     // disable \"rule of 3\" length checks meant for emphasis\n      token:  state.tokens.length - 1,\n      end:    -1,\n      open:   scanned.can_open,\n      close:  scanned.can_close\n    });\n  }\n\n  state.pos += scanned.length;\n\n  return true;\n};\n\n\nfunction postProcess(state, delimiters) {\n  var i, j,\n      startDelim,\n      endDelim,\n      token,\n      loneMarkers = [],\n      max = delimiters.length;\n\n  for (i = 0; i < max; i++) {\n    startDelim = delimiters[i];\n\n    if (startDelim.marker !== 0x7E/* ~ */) {\n      continue;\n    }\n\n    if (startDelim.end === -1) {\n      continue;\n    }\n\n    endDelim = delimiters[startDelim.end];\n\n    token         = state.tokens[startDelim.token];\n    token.type    = 's_open';\n    token.tag     = 's';\n    token.nesting = 1;\n    token.markup  = '~~';\n    token.content = '';\n\n    token         = state.tokens[endDelim.token];\n    token.type    = 's_close';\n    token.tag     = 's';\n    token.nesting = -1;\n    token.markup  = '~~';\n    token.content = '';\n\n    if (state.tokens[endDelim.token - 1].type === 'text' &&\n        state.tokens[endDelim.token - 1].content === '~') {\n\n      loneMarkers.push(endDelim.token - 1);\n    }\n  }\n\n  // If a marker sequence has an odd number of characters, it's splitted\n  // like this: `~~~~~` -> `~` + `~~` + `~~`, leaving one marker at the\n  // start of the sequence.\n  //\n  // So, we have to move all those markers after subsequent s_close tags.\n  //\n  while (loneMarkers.length) {\n    i = loneMarkers.pop();\n    j = i + 1;\n\n    while (j < state.tokens.length && state.tokens[j].type === 's_close') {\n      j++;\n    }\n\n    j--;\n\n    if (i !== j) {\n      token = state.tokens[j];\n      state.tokens[j] = state.tokens[i];\n      state.tokens[i] = token;\n    }\n  }\n}\n\n\n// Walk through delimiter list and replace text tokens with tags\n//\nmodule.exports.postProcess = function strikethrough(state) {\n  var curr,\n      tokens_meta = state.tokens_meta,\n      max = state.tokens_meta.length;\n\n  postProcess(state, state.delimiters);\n\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters);\n    }\n  }\n};\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAGZ;AACA;AACAA,MAAM,CAACC,OAAO,CAACC,QAAQ,GAAG,SAASC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC9D,IAAIC,CAAC;IAAEC,OAAO;IAAEC,KAAK;IAAEC,GAAG;IAAEC,EAAE;IAC1BC,KAAK,GAAGP,KAAK,CAACQ,GAAG;IACjBC,MAAM,GAAGT,KAAK,CAACU,GAAG,CAACC,UAAU,CAACJ,KAAK,CAAC;EAExC,IAAIN,MAAM,EAAE;IAAE,OAAO,KAAK;EAAE;EAE5B,IAAIQ,MAAM,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;EAE5CN,OAAO,GAAGH,KAAK,CAACY,UAAU,CAACZ,KAAK,CAACQ,GAAG,EAAE,IAAI,CAAC;EAC3CH,GAAG,GAAGF,OAAO,CAACU,MAAM;EACpBP,EAAE,GAAGQ,MAAM,CAACC,YAAY,CAACN,MAAM,CAAC;EAEhC,IAAIJ,GAAG,GAAG,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAE7B,IAAIA,GAAG,GAAG,CAAC,EAAE;IACXD,KAAK,GAAWJ,KAAK,CAACgB,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IACzCZ,KAAK,CAACa,OAAO,GAAGX,EAAE;IAClBD,GAAG,EAAE;EACP;EAEA,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,GAAG,EAAEH,CAAC,IAAI,CAAC,EAAE;IAC3BE,KAAK,GAAWJ,KAAK,CAACgB,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IACzCZ,KAAK,CAACa,OAAO,GAAGX,EAAE,GAAGA,EAAE;IAEvBN,KAAK,CAACkB,UAAU,CAACF,IAAI,CAAC;MACpBP,MAAM,EAAEA,MAAM;MACdI,MAAM,EAAE,CAAC;MAAM;MACfT,KAAK,EAAGJ,KAAK,CAACmB,MAAM,CAACN,MAAM,GAAG,CAAC;MAC/BO,GAAG,EAAK,CAAC,CAAC;MACVC,IAAI,EAAIlB,OAAO,CAACmB,QAAQ;MACxBC,KAAK,EAAGpB,OAAO,CAACqB;IAClB,CAAC,CAAC;EACJ;EAEAxB,KAAK,CAACQ,GAAG,IAAIL,OAAO,CAACU,MAAM;EAE3B,OAAO,IAAI;AACb,CAAC;AAGD,SAASY,WAAWA,CAACzB,KAAK,EAAEkB,UAAU,EAAE;EACtC,IAAIhB,CAAC;IAAEwB,CAAC;IACJC,UAAU;IACVC,QAAQ;IACRxB,KAAK;IACLyB,WAAW,GAAG,EAAE;IAChBC,GAAG,GAAGZ,UAAU,CAACL,MAAM;EAE3B,KAAKX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,GAAG,EAAE5B,CAAC,EAAE,EAAE;IACxByB,UAAU,GAAGT,UAAU,CAAChB,CAAC,CAAC;IAE1B,IAAIyB,UAAU,CAAClB,MAAM,KAAK,IAAI,UAAS;MACrC;IACF;IAEA,IAAIkB,UAAU,CAACP,GAAG,KAAK,CAAC,CAAC,EAAE;MACzB;IACF;IAEAQ,QAAQ,GAAGV,UAAU,CAACS,UAAU,CAACP,GAAG,CAAC;IAErChB,KAAK,GAAWJ,KAAK,CAACmB,MAAM,CAACQ,UAAU,CAACvB,KAAK,CAAC;IAC9CA,KAAK,CAAC2B,IAAI,GAAM,QAAQ;IACxB3B,KAAK,CAAC4B,GAAG,GAAO,GAAG;IACnB5B,KAAK,CAAC6B,OAAO,GAAG,CAAC;IACjB7B,KAAK,CAAC8B,MAAM,GAAI,IAAI;IACpB9B,KAAK,CAACa,OAAO,GAAG,EAAE;IAElBb,KAAK,GAAWJ,KAAK,CAACmB,MAAM,CAACS,QAAQ,CAACxB,KAAK,CAAC;IAC5CA,KAAK,CAAC2B,IAAI,GAAM,SAAS;IACzB3B,KAAK,CAAC4B,GAAG,GAAO,GAAG;IACnB5B,KAAK,CAAC6B,OAAO,GAAG,CAAC,CAAC;IAClB7B,KAAK,CAAC8B,MAAM,GAAI,IAAI;IACpB9B,KAAK,CAACa,OAAO,GAAG,EAAE;IAElB,IAAIjB,KAAK,CAACmB,MAAM,CAACS,QAAQ,CAACxB,KAAK,GAAG,CAAC,CAAC,CAAC2B,IAAI,KAAK,MAAM,IAChD/B,KAAK,CAACmB,MAAM,CAACS,QAAQ,CAACxB,KAAK,GAAG,CAAC,CAAC,CAACa,OAAO,KAAK,GAAG,EAAE;MAEpDY,WAAW,CAACb,IAAI,CAACY,QAAQ,CAACxB,KAAK,GAAG,CAAC,CAAC;IACtC;EACF;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA,OAAOyB,WAAW,CAAChB,MAAM,EAAE;IACzBX,CAAC,GAAG2B,WAAW,CAACM,GAAG,CAAC,CAAC;IACrBT,CAAC,GAAGxB,CAAC,GAAG,CAAC;IAET,OAAOwB,CAAC,GAAG1B,KAAK,CAACmB,MAAM,CAACN,MAAM,IAAIb,KAAK,CAACmB,MAAM,CAACO,CAAC,CAAC,CAACK,IAAI,KAAK,SAAS,EAAE;MACpEL,CAAC,EAAE;IACL;IAEAA,CAAC,EAAE;IAEH,IAAIxB,CAAC,KAAKwB,CAAC,EAAE;MACXtB,KAAK,GAAGJ,KAAK,CAACmB,MAAM,CAACO,CAAC,CAAC;MACvB1B,KAAK,CAACmB,MAAM,CAACO,CAAC,CAAC,GAAG1B,KAAK,CAACmB,MAAM,CAACjB,CAAC,CAAC;MACjCF,KAAK,CAACmB,MAAM,CAACjB,CAAC,CAAC,GAAGE,KAAK;IACzB;EACF;AACF;;AAGA;AACA;AACAR,MAAM,CAACC,OAAO,CAAC4B,WAAW,GAAG,SAAS1B,aAAaA,CAACC,KAAK,EAAE;EACzD,IAAIoC,IAAI;IACJC,WAAW,GAAGrC,KAAK,CAACqC,WAAW;IAC/BP,GAAG,GAAG9B,KAAK,CAACqC,WAAW,CAACxB,MAAM;EAElCY,WAAW,CAACzB,KAAK,EAAEA,KAAK,CAACkB,UAAU,CAAC;EAEpC,KAAKkB,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGN,GAAG,EAAEM,IAAI,EAAE,EAAE;IACjC,IAAIC,WAAW,CAACD,IAAI,CAAC,IAAIC,WAAW,CAACD,IAAI,CAAC,CAAClB,UAAU,EAAE;MACrDO,WAAW,CAACzB,KAAK,EAAEqC,WAAW,CAACD,IAAI,CAAC,CAAClB,UAAU,CAAC;IAClD;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}