{"ast": null, "code": "/**\n * class Ruler\n *\n * Helper class, used by [[MarkdownIt#core]], [[MarkdownIt#block]] and\n * [[MarkdownIt#inline]] to manage sequences of functions (rules):\n *\n * - keep rules in defined order\n * - assign the name to each rule\n * - enable/disable rules\n * - add/replace rules\n * - allow assign rules to additional named chains (in the same)\n * - cacheing lists of active rules\n *\n * You will not need use this class directly until write plugins. For simple\n * rules control use [[MarkdownIt.disable]], [[MarkdownIt.enable]] and\n * [[MarkdownIt.use]].\n **/\n'use strict';\n\n/**\n * new Ruler()\n **/\nfunction Ruler() {\n  // List of added rules. Each element is:\n  //\n  // {\n  //   name: XXX,\n  //   enabled: Boolean,\n  //   fn: Function(),\n  //   alt: [ name2, name3 ]\n  // }\n  //\n  this.__rules__ = [];\n\n  // Cached rule chains.\n  //\n  // First level - chain name, '' for default.\n  // Second level - diginal anchor for fast filtering by charcodes.\n  //\n  this.__cache__ = null;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// Helper methods, should not be used directly\n\n// Find rule index by name\n//\nRuler.prototype.__find__ = function (name) {\n  for (var i = 0; i < this.__rules__.length; i++) {\n    if (this.__rules__[i].name === name) {\n      return i;\n    }\n  }\n  return -1;\n};\n\n// Build rules lookup cache\n//\nRuler.prototype.__compile__ = function () {\n  var self = this;\n  var chains = [''];\n\n  // collect unique names\n  self.__rules__.forEach(function (rule) {\n    if (!rule.enabled) {\n      return;\n    }\n    rule.alt.forEach(function (altName) {\n      if (chains.indexOf(altName) < 0) {\n        chains.push(altName);\n      }\n    });\n  });\n  self.__cache__ = {};\n  chains.forEach(function (chain) {\n    self.__cache__[chain] = [];\n    self.__rules__.forEach(function (rule) {\n      if (!rule.enabled) {\n        return;\n      }\n      if (chain && rule.alt.indexOf(chain) < 0) {\n        return;\n      }\n      self.__cache__[chain].push(rule.fn);\n    });\n  });\n};\n\n/**\n * Ruler.at(name, fn [, options])\n * - name (String): rule name to replace.\n * - fn (Function): new rule function.\n * - options (Object): new rule options (not mandatory).\n *\n * Replace rule by name with new function & options. Throws error if name not\n * found.\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * Replace existing typographer replacement rule with new one:\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.core.ruler.at('replacements', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.at = function (name, fn, options) {\n  var index = this.__find__(name);\n  var opt = options || {};\n  if (index === -1) {\n    throw new Error('Parser rule not found: ' + name);\n  }\n  this.__rules__[index].fn = fn;\n  this.__rules__[index].alt = opt.alt || [];\n  this.__cache__ = null;\n};\n\n/**\n * Ruler.before(beforeName, ruleName, fn [, options])\n * - beforeName (String): new rule will be added before this one.\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Add new rule to chain before one with given name. See also\n * [[Ruler.after]], [[Ruler.push]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.block.ruler.before('paragraph', 'my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.before = function (beforeName, ruleName, fn, options) {\n  var index = this.__find__(beforeName);\n  var opt = options || {};\n  if (index === -1) {\n    throw new Error('Parser rule not found: ' + beforeName);\n  }\n  this.__rules__.splice(index, 0, {\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n  this.__cache__ = null;\n};\n\n/**\n * Ruler.after(afterName, ruleName, fn [, options])\n * - afterName (String): new rule will be added after this one.\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Add new rule to chain after one with given name. See also\n * [[Ruler.before]], [[Ruler.push]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.inline.ruler.after('text', 'my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.after = function (afterName, ruleName, fn, options) {\n  var index = this.__find__(afterName);\n  var opt = options || {};\n  if (index === -1) {\n    throw new Error('Parser rule not found: ' + afterName);\n  }\n  this.__rules__.splice(index + 1, 0, {\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n  this.__cache__ = null;\n};\n\n/**\n * Ruler.push(ruleName, fn [, options])\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Push new rule to the end of chain. See also\n * [[Ruler.before]], [[Ruler.after]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.core.ruler.push('my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.push = function (ruleName, fn, options) {\n  var opt = options || {};\n  this.__rules__.push({\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n  this.__cache__ = null;\n};\n\n/**\n * Ruler.enable(list [, ignoreInvalid]) -> Array\n * - list (String|Array): list of rule names to enable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable rules with given names. If any rule name not found - throw Error.\n * Errors can be disabled by second param.\n *\n * Returns list of found rule names (if no exception happened).\n *\n * See also [[Ruler.disable]], [[Ruler.enableOnly]].\n **/\nRuler.prototype.enable = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) {\n    list = [list];\n  }\n  var result = [];\n\n  // Search by name and enable\n  list.forEach(function (name) {\n    var idx = this.__find__(name);\n    if (idx < 0) {\n      if (ignoreInvalid) {\n        return;\n      }\n      throw new Error('Rules manager: invalid rule name ' + name);\n    }\n    this.__rules__[idx].enabled = true;\n    result.push(name);\n  }, this);\n  this.__cache__ = null;\n  return result;\n};\n\n/**\n * Ruler.enableOnly(list [, ignoreInvalid])\n * - list (String|Array): list of rule names to enable (whitelist).\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable rules with given names, and disable everything else. If any rule name\n * not found - throw Error. Errors can be disabled by second param.\n *\n * See also [[Ruler.disable]], [[Ruler.enable]].\n **/\nRuler.prototype.enableOnly = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) {\n    list = [list];\n  }\n  this.__rules__.forEach(function (rule) {\n    rule.enabled = false;\n  });\n  this.enable(list, ignoreInvalid);\n};\n\n/**\n * Ruler.disable(list [, ignoreInvalid]) -> Array\n * - list (String|Array): list of rule names to disable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Disable rules with given names. If any rule name not found - throw Error.\n * Errors can be disabled by second param.\n *\n * Returns list of found rule names (if no exception happened).\n *\n * See also [[Ruler.enable]], [[Ruler.enableOnly]].\n **/\nRuler.prototype.disable = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) {\n    list = [list];\n  }\n  var result = [];\n\n  // Search by name and disable\n  list.forEach(function (name) {\n    var idx = this.__find__(name);\n    if (idx < 0) {\n      if (ignoreInvalid) {\n        return;\n      }\n      throw new Error('Rules manager: invalid rule name ' + name);\n    }\n    this.__rules__[idx].enabled = false;\n    result.push(name);\n  }, this);\n  this.__cache__ = null;\n  return result;\n};\n\n/**\n * Ruler.getRules(chainName) -> Array\n *\n * Return array of active functions (rules) for given chain name. It analyzes\n * rules configuration, compiles caches if not exists and returns result.\n *\n * Default chain name is `''` (empty string). It can't be skipped. That's\n * done intentionally, to keep signature monomorphic for high speed.\n **/\nRuler.prototype.getRules = function (chainName) {\n  if (this.__cache__ === null) {\n    this.__compile__();\n  }\n\n  // Chain can be empty, if rules disabled. But we still have to return Array.\n  return this.__cache__[chainName] || [];\n};\nmodule.exports = Ruler;", "map": {"version": 3, "names": ["Ruler", "__rules__", "__cache__", "prototype", "__find__", "name", "i", "length", "__compile__", "self", "chains", "for<PERSON>ach", "rule", "enabled", "alt", "altName", "indexOf", "push", "chain", "fn", "at", "options", "index", "opt", "Error", "before", "beforeName", "ruleName", "splice", "after", "after<PERSON>ame", "enable", "list", "ignoreInvalid", "Array", "isArray", "result", "idx", "enableOnly", "disable", "getRules", "chainName", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/ruler.js"], "sourcesContent": ["/**\n * class Ruler\n *\n * Helper class, used by [[MarkdownIt#core]], [[MarkdownIt#block]] and\n * [[MarkdownIt#inline]] to manage sequences of functions (rules):\n *\n * - keep rules in defined order\n * - assign the name to each rule\n * - enable/disable rules\n * - add/replace rules\n * - allow assign rules to additional named chains (in the same)\n * - cacheing lists of active rules\n *\n * You will not need use this class directly until write plugins. For simple\n * rules control use [[MarkdownIt.disable]], [[MarkdownIt.enable]] and\n * [[MarkdownIt.use]].\n **/\n'use strict';\n\n\n/**\n * new Ruler()\n **/\nfunction Ruler() {\n  // List of added rules. Each element is:\n  //\n  // {\n  //   name: XXX,\n  //   enabled: Boolean,\n  //   fn: Function(),\n  //   alt: [ name2, name3 ]\n  // }\n  //\n  this.__rules__ = [];\n\n  // Cached rule chains.\n  //\n  // First level - chain name, '' for default.\n  // Second level - diginal anchor for fast filtering by charcodes.\n  //\n  this.__cache__ = null;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// Helper methods, should not be used directly\n\n\n// Find rule index by name\n//\nRuler.prototype.__find__ = function (name) {\n  for (var i = 0; i < this.__rules__.length; i++) {\n    if (this.__rules__[i].name === name) {\n      return i;\n    }\n  }\n  return -1;\n};\n\n\n// Build rules lookup cache\n//\nRuler.prototype.__compile__ = function () {\n  var self = this;\n  var chains = [ '' ];\n\n  // collect unique names\n  self.__rules__.forEach(function (rule) {\n    if (!rule.enabled) { return; }\n\n    rule.alt.forEach(function (altName) {\n      if (chains.indexOf(altName) < 0) {\n        chains.push(altName);\n      }\n    });\n  });\n\n  self.__cache__ = {};\n\n  chains.forEach(function (chain) {\n    self.__cache__[chain] = [];\n    self.__rules__.forEach(function (rule) {\n      if (!rule.enabled) { return; }\n\n      if (chain && rule.alt.indexOf(chain) < 0) { return; }\n\n      self.__cache__[chain].push(rule.fn);\n    });\n  });\n};\n\n\n/**\n * Ruler.at(name, fn [, options])\n * - name (String): rule name to replace.\n * - fn (Function): new rule function.\n * - options (Object): new rule options (not mandatory).\n *\n * Replace rule by name with new function & options. Throws error if name not\n * found.\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * Replace existing typographer replacement rule with new one:\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.core.ruler.at('replacements', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.at = function (name, fn, options) {\n  var index = this.__find__(name);\n  var opt = options || {};\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + name); }\n\n  this.__rules__[index].fn = fn;\n  this.__rules__[index].alt = opt.alt || [];\n  this.__cache__ = null;\n};\n\n\n/**\n * Ruler.before(beforeName, ruleName, fn [, options])\n * - beforeName (String): new rule will be added before this one.\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Add new rule to chain before one with given name. See also\n * [[Ruler.after]], [[Ruler.push]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.block.ruler.before('paragraph', 'my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.before = function (beforeName, ruleName, fn, options) {\n  var index = this.__find__(beforeName);\n  var opt = options || {};\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + beforeName); }\n\n  this.__rules__.splice(index, 0, {\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n\n  this.__cache__ = null;\n};\n\n\n/**\n * Ruler.after(afterName, ruleName, fn [, options])\n * - afterName (String): new rule will be added after this one.\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Add new rule to chain after one with given name. See also\n * [[Ruler.before]], [[Ruler.push]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.inline.ruler.after('text', 'my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.after = function (afterName, ruleName, fn, options) {\n  var index = this.__find__(afterName);\n  var opt = options || {};\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + afterName); }\n\n  this.__rules__.splice(index + 1, 0, {\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n\n  this.__cache__ = null;\n};\n\n/**\n * Ruler.push(ruleName, fn [, options])\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Push new rule to the end of chain. See also\n * [[Ruler.before]], [[Ruler.after]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.core.ruler.push('my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.push = function (ruleName, fn, options) {\n  var opt = options || {};\n\n  this.__rules__.push({\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n\n  this.__cache__ = null;\n};\n\n\n/**\n * Ruler.enable(list [, ignoreInvalid]) -> Array\n * - list (String|Array): list of rule names to enable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable rules with given names. If any rule name not found - throw Error.\n * Errors can be disabled by second param.\n *\n * Returns list of found rule names (if no exception happened).\n *\n * See also [[Ruler.disable]], [[Ruler.enableOnly]].\n **/\nRuler.prototype.enable = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [ list ]; }\n\n  var result = [];\n\n  // Search by name and enable\n  list.forEach(function (name) {\n    var idx = this.__find__(name);\n\n    if (idx < 0) {\n      if (ignoreInvalid) { return; }\n      throw new Error('Rules manager: invalid rule name ' + name);\n    }\n    this.__rules__[idx].enabled = true;\n    result.push(name);\n  }, this);\n\n  this.__cache__ = null;\n  return result;\n};\n\n\n/**\n * Ruler.enableOnly(list [, ignoreInvalid])\n * - list (String|Array): list of rule names to enable (whitelist).\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable rules with given names, and disable everything else. If any rule name\n * not found - throw Error. Errors can be disabled by second param.\n *\n * See also [[Ruler.disable]], [[Ruler.enable]].\n **/\nRuler.prototype.enableOnly = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [ list ]; }\n\n  this.__rules__.forEach(function (rule) { rule.enabled = false; });\n\n  this.enable(list, ignoreInvalid);\n};\n\n\n/**\n * Ruler.disable(list [, ignoreInvalid]) -> Array\n * - list (String|Array): list of rule names to disable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Disable rules with given names. If any rule name not found - throw Error.\n * Errors can be disabled by second param.\n *\n * Returns list of found rule names (if no exception happened).\n *\n * See also [[Ruler.enable]], [[Ruler.enableOnly]].\n **/\nRuler.prototype.disable = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [ list ]; }\n\n  var result = [];\n\n  // Search by name and disable\n  list.forEach(function (name) {\n    var idx = this.__find__(name);\n\n    if (idx < 0) {\n      if (ignoreInvalid) { return; }\n      throw new Error('Rules manager: invalid rule name ' + name);\n    }\n    this.__rules__[idx].enabled = false;\n    result.push(name);\n  }, this);\n\n  this.__cache__ = null;\n  return result;\n};\n\n\n/**\n * Ruler.getRules(chainName) -> Array\n *\n * Return array of active functions (rules) for given chain name. It analyzes\n * rules configuration, compiles caches if not exists and returns result.\n *\n * Default chain name is `''` (empty string). It can't be skipped. That's\n * done intentionally, to keep signature monomorphic for high speed.\n **/\nRuler.prototype.getRules = function (chainName) {\n  if (this.__cache__ === null) {\n    this.__compile__();\n  }\n\n  // Chain can be empty, if rules disabled. But we still have to return Array.\n  return this.__cache__[chainName] || [];\n};\n\nmodule.exports = Ruler;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAGZ;AACA;AACA;AACA,SAASA,KAAKA,CAAA,EAAG;EACf;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,CAACC,SAAS,GAAG,EAAE;;EAEnB;EACA;EACA;EACA;EACA;EACA,IAAI,CAACC,SAAS,GAAG,IAAI;AACvB;;AAEA;AACA;;AAGA;AACA;AACAF,KAAK,CAACG,SAAS,CAACC,QAAQ,GAAG,UAAUC,IAAI,EAAE;EACzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACL,SAAS,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAI,IAAI,CAACL,SAAS,CAACK,CAAC,CAAC,CAACD,IAAI,KAAKA,IAAI,EAAE;MACnC,OAAOC,CAAC;IACV;EACF;EACA,OAAO,CAAC,CAAC;AACX,CAAC;;AAGD;AACA;AACAN,KAAK,CAACG,SAAS,CAACK,WAAW,GAAG,YAAY;EACxC,IAAIC,IAAI,GAAG,IAAI;EACf,IAAIC,MAAM,GAAG,CAAE,EAAE,CAAE;;EAEnB;EACAD,IAAI,CAACR,SAAS,CAACU,OAAO,CAAC,UAAUC,IAAI,EAAE;IACrC,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;MAAE;IAAQ;IAE7BD,IAAI,CAACE,GAAG,CAACH,OAAO,CAAC,UAAUI,OAAO,EAAE;MAClC,IAAIL,MAAM,CAACM,OAAO,CAACD,OAAO,CAAC,GAAG,CAAC,EAAE;QAC/BL,MAAM,CAACO,IAAI,CAACF,OAAO,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFN,IAAI,CAACP,SAAS,GAAG,CAAC,CAAC;EAEnBQ,MAAM,CAACC,OAAO,CAAC,UAAUO,KAAK,EAAE;IAC9BT,IAAI,CAACP,SAAS,CAACgB,KAAK,CAAC,GAAG,EAAE;IAC1BT,IAAI,CAACR,SAAS,CAACU,OAAO,CAAC,UAAUC,IAAI,EAAE;MACrC,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;QAAE;MAAQ;MAE7B,IAAIK,KAAK,IAAIN,IAAI,CAACE,GAAG,CAACE,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC,EAAE;QAAE;MAAQ;MAEpDT,IAAI,CAACP,SAAS,CAACgB,KAAK,CAAC,CAACD,IAAI,CAACL,IAAI,CAACO,EAAE,CAAC;IACrC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAnB,KAAK,CAACG,SAAS,CAACiB,EAAE,GAAG,UAAUf,IAAI,EAAEc,EAAE,EAAEE,OAAO,EAAE;EAChD,IAAIC,KAAK,GAAG,IAAI,CAAClB,QAAQ,CAACC,IAAI,CAAC;EAC/B,IAAIkB,GAAG,GAAGF,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;IAAE,MAAM,IAAIE,KAAK,CAAC,yBAAyB,GAAGnB,IAAI,CAAC;EAAE;EAEvE,IAAI,CAACJ,SAAS,CAACqB,KAAK,CAAC,CAACH,EAAE,GAAGA,EAAE;EAC7B,IAAI,CAAClB,SAAS,CAACqB,KAAK,CAAC,CAACR,GAAG,GAAGS,GAAG,CAACT,GAAG,IAAI,EAAE;EACzC,IAAI,CAACZ,SAAS,GAAG,IAAI;AACvB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,KAAK,CAACG,SAAS,CAACsB,MAAM,GAAG,UAAUC,UAAU,EAAEC,QAAQ,EAAER,EAAE,EAAEE,OAAO,EAAE;EACpE,IAAIC,KAAK,GAAG,IAAI,CAAClB,QAAQ,CAACsB,UAAU,CAAC;EACrC,IAAIH,GAAG,GAAGF,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;IAAE,MAAM,IAAIE,KAAK,CAAC,yBAAyB,GAAGE,UAAU,CAAC;EAAE;EAE7E,IAAI,CAACzB,SAAS,CAAC2B,MAAM,CAACN,KAAK,EAAE,CAAC,EAAE;IAC9BjB,IAAI,EAAEsB,QAAQ;IACdd,OAAO,EAAE,IAAI;IACbM,EAAE,EAAEA,EAAE;IACNL,GAAG,EAAES,GAAG,CAACT,GAAG,IAAI;EAClB,CAAC,CAAC;EAEF,IAAI,CAACZ,SAAS,GAAG,IAAI;AACvB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,KAAK,CAACG,SAAS,CAAC0B,KAAK,GAAG,UAAUC,SAAS,EAAEH,QAAQ,EAAER,EAAE,EAAEE,OAAO,EAAE;EAClE,IAAIC,KAAK,GAAG,IAAI,CAAClB,QAAQ,CAAC0B,SAAS,CAAC;EACpC,IAAIP,GAAG,GAAGF,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;IAAE,MAAM,IAAIE,KAAK,CAAC,yBAAyB,GAAGM,SAAS,CAAC;EAAE;EAE5E,IAAI,CAAC7B,SAAS,CAAC2B,MAAM,CAACN,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE;IAClCjB,IAAI,EAAEsB,QAAQ;IACdd,OAAO,EAAE,IAAI;IACbM,EAAE,EAAEA,EAAE;IACNL,GAAG,EAAES,GAAG,CAACT,GAAG,IAAI;EAClB,CAAC,CAAC;EAEF,IAAI,CAACZ,SAAS,GAAG,IAAI;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,KAAK,CAACG,SAAS,CAACc,IAAI,GAAG,UAAUU,QAAQ,EAAER,EAAE,EAAEE,OAAO,EAAE;EACtD,IAAIE,GAAG,GAAGF,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAI,CAACpB,SAAS,CAACgB,IAAI,CAAC;IAClBZ,IAAI,EAAEsB,QAAQ;IACdd,OAAO,EAAE,IAAI;IACbM,EAAE,EAAEA,EAAE;IACNL,GAAG,EAAES,GAAG,CAACT,GAAG,IAAI;EAClB,CAAC,CAAC;EAEF,IAAI,CAACZ,SAAS,GAAG,IAAI;AACvB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,KAAK,CAACG,SAAS,CAAC4B,MAAM,GAAG,UAAUC,IAAI,EAAEC,aAAa,EAAE;EACtD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;IAAEA,IAAI,GAAG,CAAEA,IAAI,CAAE;EAAE;EAE7C,IAAII,MAAM,GAAG,EAAE;;EAEf;EACAJ,IAAI,CAACrB,OAAO,CAAC,UAAUN,IAAI,EAAE;IAC3B,IAAIgC,GAAG,GAAG,IAAI,CAACjC,QAAQ,CAACC,IAAI,CAAC;IAE7B,IAAIgC,GAAG,GAAG,CAAC,EAAE;MACX,IAAIJ,aAAa,EAAE;QAAE;MAAQ;MAC7B,MAAM,IAAIT,KAAK,CAAC,mCAAmC,GAAGnB,IAAI,CAAC;IAC7D;IACA,IAAI,CAACJ,SAAS,CAACoC,GAAG,CAAC,CAACxB,OAAO,GAAG,IAAI;IAClCuB,MAAM,CAACnB,IAAI,CAACZ,IAAI,CAAC;EACnB,CAAC,EAAE,IAAI,CAAC;EAER,IAAI,CAACH,SAAS,GAAG,IAAI;EACrB,OAAOkC,MAAM;AACf,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACApC,KAAK,CAACG,SAAS,CAACmC,UAAU,GAAG,UAAUN,IAAI,EAAEC,aAAa,EAAE;EAC1D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;IAAEA,IAAI,GAAG,CAAEA,IAAI,CAAE;EAAE;EAE7C,IAAI,CAAC/B,SAAS,CAACU,OAAO,CAAC,UAAUC,IAAI,EAAE;IAAEA,IAAI,CAACC,OAAO,GAAG,KAAK;EAAE,CAAC,CAAC;EAEjE,IAAI,CAACkB,MAAM,CAACC,IAAI,EAAEC,aAAa,CAAC;AAClC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjC,KAAK,CAACG,SAAS,CAACoC,OAAO,GAAG,UAAUP,IAAI,EAAEC,aAAa,EAAE;EACvD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;IAAEA,IAAI,GAAG,CAAEA,IAAI,CAAE;EAAE;EAE7C,IAAII,MAAM,GAAG,EAAE;;EAEf;EACAJ,IAAI,CAACrB,OAAO,CAAC,UAAUN,IAAI,EAAE;IAC3B,IAAIgC,GAAG,GAAG,IAAI,CAACjC,QAAQ,CAACC,IAAI,CAAC;IAE7B,IAAIgC,GAAG,GAAG,CAAC,EAAE;MACX,IAAIJ,aAAa,EAAE;QAAE;MAAQ;MAC7B,MAAM,IAAIT,KAAK,CAAC,mCAAmC,GAAGnB,IAAI,CAAC;IAC7D;IACA,IAAI,CAACJ,SAAS,CAACoC,GAAG,CAAC,CAACxB,OAAO,GAAG,KAAK;IACnCuB,MAAM,CAACnB,IAAI,CAACZ,IAAI,CAAC;EACnB,CAAC,EAAE,IAAI,CAAC;EAER,IAAI,CAACH,SAAS,GAAG,IAAI;EACrB,OAAOkC,MAAM;AACf,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACApC,KAAK,CAACG,SAAS,CAACqC,QAAQ,GAAG,UAAUC,SAAS,EAAE;EAC9C,IAAI,IAAI,CAACvC,SAAS,KAAK,IAAI,EAAE;IAC3B,IAAI,CAACM,WAAW,CAAC,CAAC;EACpB;;EAEA;EACA,OAAO,IAAI,CAACN,SAAS,CAACuC,SAAS,CAAC,IAAI,EAAE;AACxC,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG3C,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}