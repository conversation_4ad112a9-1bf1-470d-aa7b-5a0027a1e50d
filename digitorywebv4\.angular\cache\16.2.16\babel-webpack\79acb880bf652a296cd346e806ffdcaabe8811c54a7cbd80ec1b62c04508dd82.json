{"ast": null, "code": "// Parse link label\n//\n// this function assumes that first character (\"[\") already matches;\n// returns the end of the label\n//\n'use strict';\n\nmodule.exports = function parseLinkLabel(state, start, disableNested) {\n  var level,\n    found,\n    marker,\n    prevPos,\n    labelEnd = -1,\n    max = state.posMax,\n    oldPos = state.pos;\n  state.pos = start + 1;\n  level = 1;\n  while (state.pos < max) {\n    marker = state.src.charCodeAt(state.pos);\n    if (marker === 0x5D /* ] */) {\n      level--;\n      if (level === 0) {\n        found = true;\n        break;\n      }\n    }\n    prevPos = state.pos;\n    state.md.inline.skipToken(state);\n    if (marker === 0x5B /* [ */) {\n      if (prevPos === state.pos - 1) {\n        // increase level if we find text `[`, which is not a part of any token\n        level++;\n      } else if (disableNested) {\n        state.pos = oldPos;\n        return -1;\n      }\n    }\n  }\n  if (found) {\n    labelEnd = state.pos;\n  }\n\n  // restore old state\n  state.pos = oldPos;\n  return labelEnd;\n};", "map": {"version": 3, "names": ["module", "exports", "parseLinkLabel", "state", "start", "disableNested", "level", "found", "marker", "prevPos", "labelEnd", "max", "posMax", "oldPos", "pos", "src", "charCodeAt", "md", "inline", "skipToken"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/helpers/parse_link_label.js"], "sourcesContent": ["// Parse link label\n//\n// this function assumes that first character (\"[\") already matches;\n// returns the end of the label\n//\n'use strict';\n\nmodule.exports = function parseLinkLabel(state, start, disableNested) {\n  var level, found, marker, prevPos,\n      labelEnd = -1,\n      max = state.posMax,\n      oldPos = state.pos;\n\n  state.pos = start + 1;\n  level = 1;\n\n  while (state.pos < max) {\n    marker = state.src.charCodeAt(state.pos);\n    if (marker === 0x5D /* ] */) {\n      level--;\n      if (level === 0) {\n        found = true;\n        break;\n      }\n    }\n\n    prevPos = state.pos;\n    state.md.inline.skipToken(state);\n    if (marker === 0x5B /* [ */) {\n      if (prevPos === state.pos - 1) {\n        // increase level if we find text `[`, which is not a part of any token\n        level++;\n      } else if (disableNested) {\n        state.pos = oldPos;\n        return -1;\n      }\n    }\n  }\n\n  if (found) {\n    labelEnd = state.pos;\n  }\n\n  // restore old state\n  state.pos = oldPos;\n\n  return labelEnd;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,SAASC,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAE;EACpE,IAAIC,KAAK;IAAEC,KAAK;IAAEC,MAAM;IAAEC,OAAO;IAC7BC,QAAQ,GAAG,CAAC,CAAC;IACbC,GAAG,GAAGR,KAAK,CAACS,MAAM;IAClBC,MAAM,GAAGV,KAAK,CAACW,GAAG;EAEtBX,KAAK,CAACW,GAAG,GAAGV,KAAK,GAAG,CAAC;EACrBE,KAAK,GAAG,CAAC;EAET,OAAOH,KAAK,CAACW,GAAG,GAAGH,GAAG,EAAE;IACtBH,MAAM,GAAGL,KAAK,CAACY,GAAG,CAACC,UAAU,CAACb,KAAK,CAACW,GAAG,CAAC;IACxC,IAAIN,MAAM,KAAK,IAAI,CAAC,SAAS;MAC3BF,KAAK,EAAE;MACP,IAAIA,KAAK,KAAK,CAAC,EAAE;QACfC,KAAK,GAAG,IAAI;QACZ;MACF;IACF;IAEAE,OAAO,GAAGN,KAAK,CAACW,GAAG;IACnBX,KAAK,CAACc,EAAE,CAACC,MAAM,CAACC,SAAS,CAAChB,KAAK,CAAC;IAChC,IAAIK,MAAM,KAAK,IAAI,CAAC,SAAS;MAC3B,IAAIC,OAAO,KAAKN,KAAK,CAACW,GAAG,GAAG,CAAC,EAAE;QAC7B;QACAR,KAAK,EAAE;MACT,CAAC,MAAM,IAAID,aAAa,EAAE;QACxBF,KAAK,CAACW,GAAG,GAAGD,MAAM;QAClB,OAAO,CAAC,CAAC;MACX;IACF;EACF;EAEA,IAAIN,KAAK,EAAE;IACTG,QAAQ,GAAGP,KAAK,CAACW,GAAG;EACtB;;EAEA;EACAX,KAAK,CAACW,GAAG,GAAGD,MAAM;EAElB,OAAOH,QAAQ;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}