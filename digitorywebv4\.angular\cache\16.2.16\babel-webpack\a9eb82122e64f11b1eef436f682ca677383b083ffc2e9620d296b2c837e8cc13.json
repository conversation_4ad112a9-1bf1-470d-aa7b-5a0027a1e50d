{"ast": null, "code": "import { marked } from 'marked';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nclass MarkdownPipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n    this.initializeRenderer();\n  }\n  initializeRenderer() {\n    this.markedRenderer = new marked.Renderer();\n    // Paragraph rendering with proper spacing\n    this.markedRenderer.paragraph = text => `<p style=\"margin-bottom: 1em;\">${text}</p>`;\n    // List rendering with proper spacing and styling\n    this.markedRenderer.list = (body, ordered, start) => {\n      const type = ordered ? 'ol' : 'ul';\n      const startAttr = ordered && start !== 1 ? ` start=\"${start}\"` : '';\n      const listStyle = ordered ? 'decimal' : 'disc';\n      return `<${type}${startAttr} style=\"margin-bottom: 1em; padding-left: 2em; list-style-type: ${listStyle};\">${body}</${type}>`;\n    };\n    // List item rendering with proper spacing\n    this.markedRenderer.listitem = text => {\n      return `<li style=\"margin-bottom: 0.5em; display: list-item;\">${text}</li>`;\n    };\n    // Code block rendering\n    this.markedRenderer.code = (code, language) => {\n      return `<pre style=\"white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word; max-width: 100%; overflow-x: auto; background-color: #f5f5f5; padding: 1em; border-radius: 4px; margin: 1em 0;\"><code class=\"language-${language || 'text'}\" style=\"font-size: 1em;\">${code}</code></pre>`;\n    };\n    // Heading rendering with proper spacing and sizing\n    this.markedRenderer.heading = (text, level) => {\n      return `<h${level} style=\"margin-top: 1em; margin-bottom: 0.5em; font-weight: 600; font-size: ${1.5 - level * 0.1}em;\">${text}</h${level}>`;\n    };\n    // Table rendering with proper styling\n    this.markedRenderer.table = (header, body) => {\n      return `<table style=\"border-collapse: collapse; width: 100%; margin-bottom: 1em; border: 1px solid #ddd;\">\n        <thead style=\"background-color: #f5f5f5;\">${header}</thead>\n        <tbody>${body}</tbody>\n      </table>`;\n    };\n    // Table row rendering\n    this.markedRenderer.tablerow = content => {\n      return `<tr style=\"border-bottom: 1px solid #ddd;\">${content}</tr>`;\n    };\n    // Table cell rendering\n    this.markedRenderer.tablecell = (content, {\n      header,\n      align\n    }) => {\n      const tag = header ? 'th' : 'td';\n      const style = `padding: 8px; text-align: ${align || 'left'}; border-right: 1px solid #ddd;`;\n      return `<${tag} style=\"${style}\">${content}</${tag}>`;\n    };\n    // Set global options for marked\n    marked.setOptions({\n      renderer: this.markedRenderer,\n      gfm: true,\n      breaks: true,\n      pedantic: false,\n      smartLists: true,\n      smartypants: true,\n      xhtml: true,\n      headerIds: false // Don't add IDs to headers\n    });\n  }\n\n  transform(value) {\n    if (!value) {\n      return '';\n    }\n    try {\n      const hasCursor = value.includes('<span class=\"blinking-cursor\">|</span>');\n      const textWithoutCursor = value.replace(/<span class=\"blinking-cursor\">\\|<\\/span>$/, '');\n      // Convert markdown to HTML using the standard marked library\n      const html = marked(textWithoutCursor);\n      // Add the cursor back if it was present\n      const finalHtml = hasCursor ? html + '<span class=\"blinking-cursor\">|</span>' : html;\n      return this.sanitizer.bypassSecurityTrustHtml(finalHtml);\n    } catch (error) {\n      console.error('Error rendering markdown:', error);\n      // Fallback to simple text rendering if markdown processing fails\n      const safeText = value.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\n/g, '<br>');\n      return this.sanitizer.bypassSecurityTrustHtml(safeText);\n    }\n  }\n  static {\n    this.ɵfac = function MarkdownPipe_Factory(t) {\n      return new (t || MarkdownPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"markdown\",\n      type: MarkdownPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport { MarkdownPipe };", "map": {"version": 3, "names": ["marked", "MarkdownPipe", "constructor", "sanitizer", "initialize<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "paragraph", "text", "list", "body", "ordered", "start", "type", "startAttr", "listStyle", "listitem", "code", "language", "heading", "level", "table", "header", "tablerow", "content", "tablecell", "align", "tag", "style", "setOptions", "renderer", "gfm", "breaks", "pedantic", "smartLists", "smartypants", "xhtml", "headerIds", "transform", "value", "hasCursor", "includes", "textWithoutCursor", "replace", "html", "finalHtml", "bypassSecurityTrustHtml", "error", "console", "safeText", "i0", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pipes\\markdown.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { marked } from 'marked';\n\n@Pipe({\n  name: 'markdown',\n  standalone: true\n})\nexport class MarkdownPipe implements PipeTransform {\n  private markedRenderer: marked.Renderer;\n\n  constructor(private sanitizer: DomSanitizer) {\n    this.initializeRenderer();\n  }\n\n  private initializeRenderer() {\n    this.markedRenderer = new marked.Renderer();\n\n    // Paragraph rendering with proper spacing\n    this.markedRenderer.paragraph = (text) => `<p style=\"margin-bottom: 1em;\">${text}</p>`;\n    \n    // List rendering with proper spacing and styling\n    this.markedRenderer.list = (body, ordered, start) => {\n      const type = ordered ? 'ol' : 'ul';\n      const startAttr = (ordered && start !== 1) ? ` start=\"${start}\"` : '';\n      const listStyle = ordered ? 'decimal' : 'disc';\n      return `<${type}${startAttr} style=\"margin-bottom: 1em; padding-left: 2em; list-style-type: ${listStyle};\">${body}</${type}>`;\n    };\n\n    // List item rendering with proper spacing\n    this.markedRenderer.listitem = (text) => {\n      return `<li style=\"margin-bottom: 0.5em; display: list-item;\">${text}</li>`;\n    };\n\n    // Code block rendering\n    this.markedRenderer.code = (code, language) => {\n      return `<pre style=\"white-space: pre-wrap; word-break: break-word; overflow-wrap: break-word; max-width: 100%; overflow-x: auto; background-color: #f5f5f5; padding: 1em; border-radius: 4px; margin: 1em 0;\"><code class=\"language-${language || 'text'}\" style=\"font-size: 1em;\">${code}</code></pre>`;\n    };\n\n    // Heading rendering with proper spacing and sizing\n    this.markedRenderer.heading = (text, level) => {\n      return `<h${level} style=\"margin-top: 1em; margin-bottom: 0.5em; font-weight: 600; font-size: ${1.5 - (level * 0.1)}em;\">${text}</h${level}>`;\n    };\n\n    // Table rendering with proper styling\n    this.markedRenderer.table = (header, body) => {\n      return `<table style=\"border-collapse: collapse; width: 100%; margin-bottom: 1em; border: 1px solid #ddd;\">\n        <thead style=\"background-color: #f5f5f5;\">${header}</thead>\n        <tbody>${body}</tbody>\n      </table>`;\n    };\n\n    // Table row rendering\n    this.markedRenderer.tablerow = (content) => {\n      return `<tr style=\"border-bottom: 1px solid #ddd;\">${content}</tr>`;\n    };\n\n    // Table cell rendering\n    this.markedRenderer.tablecell = (content, { header, align }) => {\n      const tag = header ? 'th' : 'td';\n      const style = `padding: 8px; text-align: ${align || 'left'}; border-right: 1px solid #ddd;`;\n      return `<${tag} style=\"${style}\">${content}</${tag}>`;\n    };\n    \n    // Set global options for marked\n    marked.setOptions({\n      renderer: this.markedRenderer,\n      gfm: true,           // GitHub Flavored Markdown\n      breaks: true,        // Convert \\n to <br>\n      pedantic: false,     // Don't be too strict with original markdown spec\n      smartLists: true,    // Use smarter list behavior\n      smartypants: true,   // Use smart punctuation\n      xhtml: true,         // Use self-closing tags\n      headerIds: false     // Don't add IDs to headers\n    });\n  }\n\n  transform(value: string): SafeHtml {\n    if (!value) {\n      return '';\n    }\n\n    try {\n      const hasCursor = value.includes('<span class=\"blinking-cursor\">|</span>');\n      const textWithoutCursor = value.replace(/<span class=\"blinking-cursor\">\\|<\\/span>$/, '');\n      \n      // Convert markdown to HTML using the standard marked library\n      const html = marked(textWithoutCursor);\n\n      // Add the cursor back if it was present\n      const finalHtml = hasCursor ? html + '<span class=\"blinking-cursor\">|</span>' : html;\n\n      return this.sanitizer.bypassSecurityTrustHtml(finalHtml);\n    } catch (error) {\n      console.error('Error rendering markdown:', error);\n      // Fallback to simple text rendering if markdown processing fails\n      const safeText = value\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\\n/g, '<br>');\n      return this.sanitizer.bypassSecurityTrustHtml(safeText);\n    }\n  }\n}\n"], "mappings": "AAEA,SAASA,MAAM,QAAQ,QAAQ;;;AAE/B,MAIaC,YAAY;EAGvBC,YAAoBC,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQA,kBAAkBA,CAAA;IACxB,IAAI,CAACC,cAAc,GAAG,IAAIL,MAAM,CAACM,QAAQ,EAAE;IAE3C;IACA,IAAI,CAACD,cAAc,CAACE,SAAS,GAAIC,IAAI,IAAK,kCAAkCA,IAAI,MAAM;IAEtF;IACA,IAAI,CAACH,cAAc,CAACI,IAAI,GAAG,CAACC,IAAI,EAAEC,OAAO,EAAEC,KAAK,KAAI;MAClD,MAAMC,IAAI,GAAGF,OAAO,GAAG,IAAI,GAAG,IAAI;MAClC,MAAMG,SAAS,GAAIH,OAAO,IAAIC,KAAK,KAAK,CAAC,GAAI,WAAWA,KAAK,GAAG,GAAG,EAAE;MACrE,MAAMG,SAAS,GAAGJ,OAAO,GAAG,SAAS,GAAG,MAAM;MAC9C,OAAO,IAAIE,IAAI,GAAGC,SAAS,mEAAmEC,SAAS,MAAML,IAAI,KAAKG,IAAI,GAAG;IAC/H,CAAC;IAED;IACA,IAAI,CAACR,cAAc,CAACW,QAAQ,GAAIR,IAAI,IAAI;MACtC,OAAO,yDAAyDA,IAAI,OAAO;IAC7E,CAAC;IAED;IACA,IAAI,CAACH,cAAc,CAACY,IAAI,GAAG,CAACA,IAAI,EAAEC,QAAQ,KAAI;MAC5C,OAAO,+NAA+NA,QAAQ,IAAI,MAAM,6BAA6BD,IAAI,eAAe;IAC1S,CAAC;IAED;IACA,IAAI,CAACZ,cAAc,CAACc,OAAO,GAAG,CAACX,IAAI,EAAEY,KAAK,KAAI;MAC5C,OAAO,KAAKA,KAAK,+EAA+E,GAAG,GAAIA,KAAK,GAAG,GAAI,QAAQZ,IAAI,MAAMY,KAAK,GAAG;IAC/I,CAAC;IAED;IACA,IAAI,CAACf,cAAc,CAACgB,KAAK,GAAG,CAACC,MAAM,EAAEZ,IAAI,KAAI;MAC3C,OAAO;oDACuCY,MAAM;iBACzCZ,IAAI;eACN;IACX,CAAC;IAED;IACA,IAAI,CAACL,cAAc,CAACkB,QAAQ,GAAIC,OAAO,IAAI;MACzC,OAAO,8CAA8CA,OAAO,OAAO;IACrE,CAAC;IAED;IACA,IAAI,CAACnB,cAAc,CAACoB,SAAS,GAAG,CAACD,OAAO,EAAE;MAAEF,MAAM;MAAEI;IAAK,CAAE,KAAI;MAC7D,MAAMC,GAAG,GAAGL,MAAM,GAAG,IAAI,GAAG,IAAI;MAChC,MAAMM,KAAK,GAAG,6BAA6BF,KAAK,IAAI,MAAM,iCAAiC;MAC3F,OAAO,IAAIC,GAAG,WAAWC,KAAK,KAAKJ,OAAO,KAAKG,GAAG,GAAG;IACvD,CAAC;IAED;IACA3B,MAAM,CAAC6B,UAAU,CAAC;MAChBC,QAAQ,EAAE,IAAI,CAACzB,cAAc;MAC7B0B,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,KAAK,CAAK;KACtB,CAAC;EACJ;;EAEAC,SAASA,CAACC,KAAa;IACrB,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,EAAE;;IAGX,IAAI;MACF,MAAMC,SAAS,GAAGD,KAAK,CAACE,QAAQ,CAAC,wCAAwC,CAAC;MAC1E,MAAMC,iBAAiB,GAAGH,KAAK,CAACI,OAAO,CAAC,2CAA2C,EAAE,EAAE,CAAC;MAExF;MACA,MAAMC,IAAI,GAAG5C,MAAM,CAAC0C,iBAAiB,CAAC;MAEtC;MACA,MAAMG,SAAS,GAAGL,SAAS,GAAGI,IAAI,GAAG,wCAAwC,GAAGA,IAAI;MAEpF,OAAO,IAAI,CAACzC,SAAS,CAAC2C,uBAAuB,CAACD,SAAS,CAAC;KACzD,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACA,MAAME,QAAQ,GAAGV,KAAK,CACnBI,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;MACzB,OAAO,IAAI,CAACxC,SAAS,CAAC2C,uBAAuB,CAACG,QAAQ,CAAC;;EAE3D;;;uBA9FWhD,YAAY,EAAAiD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;;YAAZpD,YAAY;MAAAqD,IAAA;MAAAC,UAAA;IAAA;EAAA;;SAAZtD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}