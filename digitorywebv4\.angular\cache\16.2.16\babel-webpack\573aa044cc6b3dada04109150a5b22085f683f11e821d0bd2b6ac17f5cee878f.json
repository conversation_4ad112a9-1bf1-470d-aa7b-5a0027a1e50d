{"ast": null, "code": "/**\n * This function is like `arrayIncludes` except that it accepts a comparator.\n *\n * @private\n * @param {Array} [array] The array to inspect.\n * @param {*} target The value to search for.\n * @param {Function} comparator The comparator invoked per element.\n * @returns {boolean} Returns `true` if `target` is found, else `false`.\n */\nfunction arrayIncludesWith(array, value, comparator) {\n  var index = -1,\n    length = array == null ? 0 : array.length;\n  while (++index < length) {\n    if (comparator(value, array[index])) {\n      return true;\n    }\n  }\n  return false;\n}\nexport default arrayIncludesWith;", "map": {"version": 3, "names": ["arrayIncludesWith", "array", "value", "comparator", "index", "length"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/lodash-es/_arrayIncludesWith.js"], "sourcesContent": ["/**\n * This function is like `arrayIncludes` except that it accepts a comparator.\n *\n * @private\n * @param {Array} [array] The array to inspect.\n * @param {*} target The value to search for.\n * @param {Function} comparator The comparator invoked per element.\n * @returns {boolean} Returns `true` if `target` is found, else `false`.\n */\nfunction arrayIncludesWith(array, value, comparator) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    if (comparator(value, array[index])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nexport default arrayIncludesWith;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACnD,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGJ,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACI,MAAM;EAE7C,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIF,UAAU,CAACD,KAAK,EAAED,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE;MACnC,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AAEA,eAAeJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}