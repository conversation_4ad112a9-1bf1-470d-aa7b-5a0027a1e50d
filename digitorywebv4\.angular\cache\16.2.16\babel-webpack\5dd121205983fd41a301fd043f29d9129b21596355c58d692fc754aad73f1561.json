{"ast": null, "code": "// Simple typographic replacements\n//\n// (c) (C) → ©\n// (tm) (TM) → ™\n// (r) (R) → ®\n// +- → ±\n// (p) (P) -> §\n// ... → … (also ?.... → ?.., !.... → !..)\n// ???????? → ???, !!!!! → !!!, `,,` → `,`\n// -- → &ndash;, --- → &mdash;\n//\n'use strict';\n\n// TODO:\n// - fractionals 1/2, 1/4, 3/4 -> ½, ¼, ¾\n// - multiplications 2 x 4 -> 2 × 4\nvar RARE_RE = /\\+-|\\.\\.|\\?\\?\\?\\?|!!!!|,,|--/;\n\n// Workaround for phantomjs - need regex without /g flag,\n// or root check will fail every second time\nvar SCOPED_ABBR_TEST_RE = /\\((c|tm|r)\\)/i;\nvar SCOPED_ABBR_RE = /\\((c|tm|r)\\)/ig;\nvar SCOPED_ABBR = {\n  c: '©',\n  r: '®',\n  tm: '™'\n};\nfunction replaceFn(match, name) {\n  return SCOPED_ABBR[name.toLowerCase()];\n}\nfunction replace_scoped(inlineTokens) {\n  var i,\n    token,\n    inside_autolink = 0;\n  for (i = inlineTokens.length - 1; i >= 0; i--) {\n    token = inlineTokens[i];\n    if (token.type === 'text' && !inside_autolink) {\n      token.content = token.content.replace(SCOPED_ABBR_RE, replaceFn);\n    }\n    if (token.type === 'link_open' && token.info === 'auto') {\n      inside_autolink--;\n    }\n    if (token.type === 'link_close' && token.info === 'auto') {\n      inside_autolink++;\n    }\n  }\n}\nfunction replace_rare(inlineTokens) {\n  var i,\n    token,\n    inside_autolink = 0;\n  for (i = inlineTokens.length - 1; i >= 0; i--) {\n    token = inlineTokens[i];\n    if (token.type === 'text' && !inside_autolink) {\n      if (RARE_RE.test(token.content)) {\n        token.content = token.content.replace(/\\+-/g, '±')\n        // .., ..., ....... -> …\n        // but ?..... & !..... -> ?.. & !..\n        .replace(/\\.{2,}/g, '…').replace(/([?!])…/g, '$1..').replace(/([?!]){4,}/g, '$1$1$1').replace(/,{2,}/g, ',')\n        // em-dash\n        .replace(/(^|[^-])---(?=[^-]|$)/mg, '$1\\u2014')\n        // en-dash\n        .replace(/(^|\\s)--(?=\\s|$)/mg, '$1\\u2013').replace(/(^|[^-\\s])--(?=[^-\\s]|$)/mg, '$1\\u2013');\n      }\n    }\n    if (token.type === 'link_open' && token.info === 'auto') {\n      inside_autolink--;\n    }\n    if (token.type === 'link_close' && token.info === 'auto') {\n      inside_autolink++;\n    }\n  }\n}\nmodule.exports = function replace(state) {\n  var blkIdx;\n  if (!state.md.options.typographer) {\n    return;\n  }\n  for (blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n    if (state.tokens[blkIdx].type !== 'inline') {\n      continue;\n    }\n    if (SCOPED_ABBR_TEST_RE.test(state.tokens[blkIdx].content)) {\n      replace_scoped(state.tokens[blkIdx].children);\n    }\n    if (RARE_RE.test(state.tokens[blkIdx].content)) {\n      replace_rare(state.tokens[blkIdx].children);\n    }\n  }\n};", "map": {"version": 3, "names": ["RARE_RE", "SCOPED_ABBR_TEST_RE", "SCOPED_ABBR_RE", "SCOPED_ABBR", "c", "r", "tm", "replaceFn", "match", "name", "toLowerCase", "replace_scoped", "inlineTokens", "i", "token", "inside_autolink", "length", "type", "content", "replace", "info", "replace_rare", "test", "module", "exports", "state", "blkIdx", "md", "options", "typographer", "tokens", "children"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_core/replacements.js"], "sourcesContent": ["// Simple typographic replacements\n//\n// (c) (C) → ©\n// (tm) (TM) → ™\n// (r) (R) → ®\n// +- → ±\n// (p) (P) -> §\n// ... → … (also ?.... → ?.., !.... → !..)\n// ???????? → ???, !!!!! → !!!, `,,` → `,`\n// -- → &ndash;, --- → &mdash;\n//\n'use strict';\n\n// TODO:\n// - fractionals 1/2, 1/4, 3/4 -> ½, ¼, ¾\n// - multiplications 2 x 4 -> 2 × 4\n\nvar RARE_RE = /\\+-|\\.\\.|\\?\\?\\?\\?|!!!!|,,|--/;\n\n// Workaround for phantomjs - need regex without /g flag,\n// or root check will fail every second time\nvar SCOPED_ABBR_TEST_RE = /\\((c|tm|r)\\)/i;\n\nvar SCOPED_ABBR_RE = /\\((c|tm|r)\\)/ig;\nvar SCOPED_ABBR = {\n  c: '©',\n  r: '®',\n  tm: '™'\n};\n\nfunction replaceFn(match, name) {\n  return SCOPED_ABBR[name.toLowerCase()];\n}\n\nfunction replace_scoped(inlineTokens) {\n  var i, token, inside_autolink = 0;\n\n  for (i = inlineTokens.length - 1; i >= 0; i--) {\n    token = inlineTokens[i];\n\n    if (token.type === 'text' && !inside_autolink) {\n      token.content = token.content.replace(SCOPED_ABBR_RE, replaceFn);\n    }\n\n    if (token.type === 'link_open' && token.info === 'auto') {\n      inside_autolink--;\n    }\n\n    if (token.type === 'link_close' && token.info === 'auto') {\n      inside_autolink++;\n    }\n  }\n}\n\nfunction replace_rare(inlineTokens) {\n  var i, token, inside_autolink = 0;\n\n  for (i = inlineTokens.length - 1; i >= 0; i--) {\n    token = inlineTokens[i];\n\n    if (token.type === 'text' && !inside_autolink) {\n      if (RARE_RE.test(token.content)) {\n        token.content = token.content\n          .replace(/\\+-/g, '±')\n          // .., ..., ....... -> …\n          // but ?..... & !..... -> ?.. & !..\n          .replace(/\\.{2,}/g, '…').replace(/([?!])…/g, '$1..')\n          .replace(/([?!]){4,}/g, '$1$1$1').replace(/,{2,}/g, ',')\n          // em-dash\n          .replace(/(^|[^-])---(?=[^-]|$)/mg, '$1\\u2014')\n          // en-dash\n          .replace(/(^|\\s)--(?=\\s|$)/mg, '$1\\u2013')\n          .replace(/(^|[^-\\s])--(?=[^-\\s]|$)/mg, '$1\\u2013');\n      }\n    }\n\n    if (token.type === 'link_open' && token.info === 'auto') {\n      inside_autolink--;\n    }\n\n    if (token.type === 'link_close' && token.info === 'auto') {\n      inside_autolink++;\n    }\n  }\n}\n\n\nmodule.exports = function replace(state) {\n  var blkIdx;\n\n  if (!state.md.options.typographer) { return; }\n\n  for (blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n\n    if (state.tokens[blkIdx].type !== 'inline') { continue; }\n\n    if (SCOPED_ABBR_TEST_RE.test(state.tokens[blkIdx].content)) {\n      replace_scoped(state.tokens[blkIdx].children);\n    }\n\n    if (RARE_RE.test(state.tokens[blkIdx].content)) {\n      replace_rare(state.tokens[blkIdx].children);\n    }\n\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAEZ;AACA;AACA;AAEA,IAAIA,OAAO,GAAG,8BAA8B;;AAE5C;AACA;AACA,IAAIC,mBAAmB,GAAG,eAAe;AAEzC,IAAIC,cAAc,GAAG,gBAAgB;AACrC,IAAIC,WAAW,GAAG;EAChBC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,EAAE,EAAE;AACN,CAAC;AAED,SAASC,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC9B,OAAON,WAAW,CAACM,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;AACxC;AAEA,SAASC,cAAcA,CAACC,YAAY,EAAE;EACpC,IAAIC,CAAC;IAAEC,KAAK;IAAEC,eAAe,GAAG,CAAC;EAEjC,KAAKF,CAAC,GAAGD,YAAY,CAACI,MAAM,GAAG,CAAC,EAAEH,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC7CC,KAAK,GAAGF,YAAY,CAACC,CAAC,CAAC;IAEvB,IAAIC,KAAK,CAACG,IAAI,KAAK,MAAM,IAAI,CAACF,eAAe,EAAE;MAC7CD,KAAK,CAACI,OAAO,GAAGJ,KAAK,CAACI,OAAO,CAACC,OAAO,CAACjB,cAAc,EAAEK,SAAS,CAAC;IAClE;IAEA,IAAIO,KAAK,CAACG,IAAI,KAAK,WAAW,IAAIH,KAAK,CAACM,IAAI,KAAK,MAAM,EAAE;MACvDL,eAAe,EAAE;IACnB;IAEA,IAAID,KAAK,CAACG,IAAI,KAAK,YAAY,IAAIH,KAAK,CAACM,IAAI,KAAK,MAAM,EAAE;MACxDL,eAAe,EAAE;IACnB;EACF;AACF;AAEA,SAASM,YAAYA,CAACT,YAAY,EAAE;EAClC,IAAIC,CAAC;IAAEC,KAAK;IAAEC,eAAe,GAAG,CAAC;EAEjC,KAAKF,CAAC,GAAGD,YAAY,CAACI,MAAM,GAAG,CAAC,EAAEH,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC7CC,KAAK,GAAGF,YAAY,CAACC,CAAC,CAAC;IAEvB,IAAIC,KAAK,CAACG,IAAI,KAAK,MAAM,IAAI,CAACF,eAAe,EAAE;MAC7C,IAAIf,OAAO,CAACsB,IAAI,CAACR,KAAK,CAACI,OAAO,CAAC,EAAE;QAC/BJ,KAAK,CAACI,OAAO,GAAGJ,KAAK,CAACI,OAAO,CAC1BC,OAAO,CAAC,MAAM,EAAE,GAAG;QACpB;QACA;QAAA,CACCA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CACnDA,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,GAAG;QACvD;QAAA,CACCA,OAAO,CAAC,yBAAyB,EAAE,UAAU;QAC9C;QAAA,CACCA,OAAO,CAAC,oBAAoB,EAAE,UAAU,CAAC,CACzCA,OAAO,CAAC,4BAA4B,EAAE,UAAU,CAAC;MACtD;IACF;IAEA,IAAIL,KAAK,CAACG,IAAI,KAAK,WAAW,IAAIH,KAAK,CAACM,IAAI,KAAK,MAAM,EAAE;MACvDL,eAAe,EAAE;IACnB;IAEA,IAAID,KAAK,CAACG,IAAI,KAAK,YAAY,IAAIH,KAAK,CAACM,IAAI,KAAK,MAAM,EAAE;MACxDL,eAAe,EAAE;IACnB;EACF;AACF;AAGAQ,MAAM,CAACC,OAAO,GAAG,SAASL,OAAOA,CAACM,KAAK,EAAE;EACvC,IAAIC,MAAM;EAEV,IAAI,CAACD,KAAK,CAACE,EAAE,CAACC,OAAO,CAACC,WAAW,EAAE;IAAE;EAAQ;EAE7C,KAAKH,MAAM,GAAGD,KAAK,CAACK,MAAM,CAACd,MAAM,GAAG,CAAC,EAAEU,MAAM,IAAI,CAAC,EAAEA,MAAM,EAAE,EAAE;IAE5D,IAAID,KAAK,CAACK,MAAM,CAACJ,MAAM,CAAC,CAACT,IAAI,KAAK,QAAQ,EAAE;MAAE;IAAU;IAExD,IAAIhB,mBAAmB,CAACqB,IAAI,CAACG,KAAK,CAACK,MAAM,CAACJ,MAAM,CAAC,CAACR,OAAO,CAAC,EAAE;MAC1DP,cAAc,CAACc,KAAK,CAACK,MAAM,CAACJ,MAAM,CAAC,CAACK,QAAQ,CAAC;IAC/C;IAEA,IAAI/B,OAAO,CAACsB,IAAI,CAACG,KAAK,CAACK,MAAM,CAACJ,MAAM,CAAC,CAACR,OAAO,CAAC,EAAE;MAC9CG,YAAY,CAACI,KAAK,CAACK,MAAM,CAACJ,MAAM,CAAC,CAACK,QAAQ,CAAC;IAC7C;EAEF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}