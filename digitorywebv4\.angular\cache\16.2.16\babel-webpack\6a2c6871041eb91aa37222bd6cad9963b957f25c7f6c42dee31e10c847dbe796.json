{"ast": null, "code": "// Join raw text tokens with the rest of the text\n//\n// This is set as a separate rule to provide an opportunity for plugins\n// to run text replacements after text join, but before escape join.\n//\n// For example, `\\:)` shouldn't be replaced with an emoji.\n//\n'use strict';\n\nmodule.exports = function text_join(state) {\n  var j,\n    l,\n    tokens,\n    curr,\n    max,\n    last,\n    blockTokens = state.tokens;\n  for (j = 0, l = blockTokens.length; j < l; j++) {\n    if (blockTokens[j].type !== 'inline') continue;\n    tokens = blockTokens[j].children;\n    max = tokens.length;\n    for (curr = 0; curr < max; curr++) {\n      if (tokens[curr].type === 'text_special') {\n        tokens[curr].type = 'text';\n      }\n    }\n    for (curr = last = 0; curr < max; curr++) {\n      if (tokens[curr].type === 'text' && curr + 1 < max && tokens[curr + 1].type === 'text') {\n        // collapse two adjacent text nodes\n        tokens[curr + 1].content = tokens[curr].content + tokens[curr + 1].content;\n      } else {\n        if (curr !== last) {\n          tokens[last] = tokens[curr];\n        }\n        last++;\n      }\n    }\n    if (curr !== last) {\n      tokens.length = last;\n    }\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "text_join", "state", "j", "l", "tokens", "curr", "max", "last", "blockTokens", "length", "type", "children", "content"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_core/text_join.js"], "sourcesContent": ["// Join raw text tokens with the rest of the text\n//\n// This is set as a separate rule to provide an opportunity for plugins\n// to run text replacements after text join, but before escape join.\n//\n// For example, `\\:)` shouldn't be replaced with an emoji.\n//\n'use strict';\n\n\nmodule.exports = function text_join(state) {\n  var j, l, tokens, curr, max, last,\n      blockTokens = state.tokens;\n\n  for (j = 0, l = blockTokens.length; j < l; j++) {\n    if (blockTokens[j].type !== 'inline') continue;\n\n    tokens = blockTokens[j].children;\n    max = tokens.length;\n\n    for (curr = 0; curr < max; curr++) {\n      if (tokens[curr].type === 'text_special') {\n        tokens[curr].type = 'text';\n      }\n    }\n\n    for (curr = last = 0; curr < max; curr++) {\n      if (tokens[curr].type === 'text' &&\n          curr + 1 < max &&\n          tokens[curr + 1].type === 'text') {\n\n        // collapse two adjacent text nodes\n        tokens[curr + 1].content = tokens[curr].content + tokens[curr + 1].content;\n      } else {\n        if (curr !== last) { tokens[last] = tokens[curr]; }\n\n        last++;\n      }\n    }\n\n    if (curr !== last) {\n      tokens.length = last;\n    }\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,SAASA,CAACC,KAAK,EAAE;EACzC,IAAIC,CAAC;IAAEC,CAAC;IAAEC,MAAM;IAAEC,IAAI;IAAEC,GAAG;IAAEC,IAAI;IAC7BC,WAAW,GAAGP,KAAK,CAACG,MAAM;EAE9B,KAAKF,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGK,WAAW,CAACC,MAAM,EAAEP,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAIM,WAAW,CAACN,CAAC,CAAC,CAACQ,IAAI,KAAK,QAAQ,EAAE;IAEtCN,MAAM,GAAGI,WAAW,CAACN,CAAC,CAAC,CAACS,QAAQ;IAChCL,GAAG,GAAGF,MAAM,CAACK,MAAM;IAEnB,KAAKJ,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGC,GAAG,EAAED,IAAI,EAAE,EAAE;MACjC,IAAID,MAAM,CAACC,IAAI,CAAC,CAACK,IAAI,KAAK,cAAc,EAAE;QACxCN,MAAM,CAACC,IAAI,CAAC,CAACK,IAAI,GAAG,MAAM;MAC5B;IACF;IAEA,KAAKL,IAAI,GAAGE,IAAI,GAAG,CAAC,EAAEF,IAAI,GAAGC,GAAG,EAAED,IAAI,EAAE,EAAE;MACxC,IAAID,MAAM,CAACC,IAAI,CAAC,CAACK,IAAI,KAAK,MAAM,IAC5BL,IAAI,GAAG,CAAC,GAAGC,GAAG,IACdF,MAAM,CAACC,IAAI,GAAG,CAAC,CAAC,CAACK,IAAI,KAAK,MAAM,EAAE;QAEpC;QACAN,MAAM,CAACC,IAAI,GAAG,CAAC,CAAC,CAACO,OAAO,GAAGR,MAAM,CAACC,IAAI,CAAC,CAACO,OAAO,GAAGR,MAAM,CAACC,IAAI,GAAG,CAAC,CAAC,CAACO,OAAO;MAC5E,CAAC,MAAM;QACL,IAAIP,IAAI,KAAKE,IAAI,EAAE;UAAEH,MAAM,CAACG,IAAI,CAAC,GAAGH,MAAM,CAACC,IAAI,CAAC;QAAE;QAElDE,IAAI,EAAE;MACR;IACF;IAEA,IAAIF,IAAI,KAAKE,IAAI,EAAE;MACjBH,MAAM,CAACK,MAAM,GAAGF,IAAI;IACtB;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}