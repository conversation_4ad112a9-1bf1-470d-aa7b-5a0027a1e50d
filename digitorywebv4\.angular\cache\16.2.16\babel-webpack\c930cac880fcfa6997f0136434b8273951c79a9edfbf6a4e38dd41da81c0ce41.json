{"ast": null, "code": "// Process links like https://example.org/\n\n'use strict';\n\n// RFC3986: scheme = ALPHA *( ALPHA / DIGIT / \"+\" / \"-\" / \".\" )\nvar SCHEME_RE = /(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;\nmodule.exports = function linkify(state, silent) {\n  var pos, max, match, proto, link, url, fullUrl, token;\n  if (!state.md.options.linkify) return false;\n  if (state.linkLevel > 0) return false;\n  pos = state.pos;\n  max = state.posMax;\n  if (pos + 3 > max) return false;\n  if (state.src.charCodeAt(pos) !== 0x3A /* : */) return false;\n  if (state.src.charCodeAt(pos + 1) !== 0x2F /* / */) return false;\n  if (state.src.charCodeAt(pos + 2) !== 0x2F /* / */) return false;\n  match = state.pending.match(SCHEME_RE);\n  if (!match) return false;\n  proto = match[1];\n  link = state.md.linkify.matchAtStart(state.src.slice(pos - proto.length));\n  if (!link) return false;\n  url = link.url;\n\n  // disallow '*' at the end of the link (conflicts with emphasis)\n  url = url.replace(/\\*+$/, '');\n  fullUrl = state.md.normalizeLink(url);\n  if (!state.md.validateLink(fullUrl)) return false;\n  if (!silent) {\n    state.pending = state.pending.slice(0, -proto.length);\n    token = state.push('link_open', 'a', 1);\n    token.attrs = [['href', fullUrl]];\n    token.markup = 'linkify';\n    token.info = 'auto';\n    token = state.push('text', '', 0);\n    token.content = state.md.normalizeLinkText(url);\n    token = state.push('link_close', 'a', -1);\n    token.markup = 'linkify';\n    token.info = 'auto';\n  }\n  state.pos += url.length - proto.length;\n  return true;\n};", "map": {"version": 3, "names": ["SCHEME_RE", "module", "exports", "linkify", "state", "silent", "pos", "max", "match", "proto", "link", "url", "fullUrl", "token", "md", "options", "linkLevel", "posMax", "src", "charCodeAt", "pending", "matchAtStart", "slice", "length", "replace", "normalizeLink", "validateLink", "push", "attrs", "markup", "info", "content", "normalizeLinkText"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_inline/linkify.js"], "sourcesContent": ["// Process links like https://example.org/\n\n'use strict';\n\n\n// RFC3986: scheme = ALPHA *( ALPHA / DIGIT / \"+\" / \"-\" / \".\" )\nvar SCHEME_RE = /(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;\n\n\nmodule.exports = function linkify(state, silent) {\n  var pos, max, match, proto, link, url, fullUrl, token;\n\n  if (!state.md.options.linkify) return false;\n  if (state.linkLevel > 0) return false;\n\n  pos = state.pos;\n  max = state.posMax;\n\n  if (pos + 3 > max) return false;\n  if (state.src.charCodeAt(pos) !== 0x3A/* : */) return false;\n  if (state.src.charCodeAt(pos + 1) !== 0x2F/* / */) return false;\n  if (state.src.charCodeAt(pos + 2) !== 0x2F/* / */) return false;\n\n  match = state.pending.match(SCHEME_RE);\n  if (!match) return false;\n\n  proto = match[1];\n\n  link = state.md.linkify.matchAtStart(state.src.slice(pos - proto.length));\n  if (!link) return false;\n\n  url = link.url;\n\n  // disallow '*' at the end of the link (conflicts with emphasis)\n  url = url.replace(/\\*+$/, '');\n\n  fullUrl = state.md.normalizeLink(url);\n  if (!state.md.validateLink(fullUrl)) return false;\n\n  if (!silent) {\n    state.pending = state.pending.slice(0, -proto.length);\n\n    token         = state.push('link_open', 'a', 1);\n    token.attrs   = [ [ 'href', fullUrl ] ];\n    token.markup  = 'linkify';\n    token.info    = 'auto';\n\n    token         = state.push('text', '', 0);\n    token.content = state.md.normalizeLinkText(url);\n\n    token         = state.push('link_close', 'a', -1);\n    token.markup  = 'linkify';\n    token.info    = 'auto';\n  }\n\n  state.pos += url.length - proto.length;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZ;AACA,IAAIA,SAAS,GAAG,yCAAyC;AAGzDC,MAAM,CAACC,OAAO,GAAG,SAASC,OAAOA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC/C,IAAIC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAEC,KAAK;EAErD,IAAI,CAACT,KAAK,CAACU,EAAE,CAACC,OAAO,CAACZ,OAAO,EAAE,OAAO,KAAK;EAC3C,IAAIC,KAAK,CAACY,SAAS,GAAG,CAAC,EAAE,OAAO,KAAK;EAErCV,GAAG,GAAGF,KAAK,CAACE,GAAG;EACfC,GAAG,GAAGH,KAAK,CAACa,MAAM;EAElB,IAAIX,GAAG,GAAG,CAAC,GAAGC,GAAG,EAAE,OAAO,KAAK;EAC/B,IAAIH,KAAK,CAACc,GAAG,CAACC,UAAU,CAACb,GAAG,CAAC,KAAK,IAAI,UAAS,OAAO,KAAK;EAC3D,IAAIF,KAAK,CAACc,GAAG,CAACC,UAAU,CAACb,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS,OAAO,KAAK;EAC/D,IAAIF,KAAK,CAACc,GAAG,CAACC,UAAU,CAACb,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS,OAAO,KAAK;EAE/DE,KAAK,GAAGJ,KAAK,CAACgB,OAAO,CAACZ,KAAK,CAACR,SAAS,CAAC;EACtC,IAAI,CAACQ,KAAK,EAAE,OAAO,KAAK;EAExBC,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC;EAEhBE,IAAI,GAAGN,KAAK,CAACU,EAAE,CAACX,OAAO,CAACkB,YAAY,CAACjB,KAAK,CAACc,GAAG,CAACI,KAAK,CAAChB,GAAG,GAAGG,KAAK,CAACc,MAAM,CAAC,CAAC;EACzE,IAAI,CAACb,IAAI,EAAE,OAAO,KAAK;EAEvBC,GAAG,GAAGD,IAAI,CAACC,GAAG;;EAEd;EACAA,GAAG,GAAGA,GAAG,CAACa,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EAE7BZ,OAAO,GAAGR,KAAK,CAACU,EAAE,CAACW,aAAa,CAACd,GAAG,CAAC;EACrC,IAAI,CAACP,KAAK,CAACU,EAAE,CAACY,YAAY,CAACd,OAAO,CAAC,EAAE,OAAO,KAAK;EAEjD,IAAI,CAACP,MAAM,EAAE;IACXD,KAAK,CAACgB,OAAO,GAAGhB,KAAK,CAACgB,OAAO,CAACE,KAAK,CAAC,CAAC,EAAE,CAACb,KAAK,CAACc,MAAM,CAAC;IAErDV,KAAK,GAAWT,KAAK,CAACuB,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;IAC/Cd,KAAK,CAACe,KAAK,GAAK,CAAE,CAAE,MAAM,EAAEhB,OAAO,CAAE,CAAE;IACvCC,KAAK,CAACgB,MAAM,GAAI,SAAS;IACzBhB,KAAK,CAACiB,IAAI,GAAM,MAAM;IAEtBjB,KAAK,GAAWT,KAAK,CAACuB,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IACzCd,KAAK,CAACkB,OAAO,GAAG3B,KAAK,CAACU,EAAE,CAACkB,iBAAiB,CAACrB,GAAG,CAAC;IAE/CE,KAAK,GAAWT,KAAK,CAACuB,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACjDd,KAAK,CAACgB,MAAM,GAAI,SAAS;IACzBhB,KAAK,CAACiB,IAAI,GAAM,MAAM;EACxB;EAEA1B,KAAK,CAACE,GAAG,IAAIK,GAAG,CAACY,MAAM,GAAGd,KAAK,CAACc,MAAM;EACtC,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}