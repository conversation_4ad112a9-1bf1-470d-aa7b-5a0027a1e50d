{"ast": null, "code": "// Process html tags\n\n'use strict';\n\nvar HTML_TAG_RE = require('../common/html_re').HTML_TAG_RE;\nfunction isLinkOpen(str) {\n  return /^<a[>\\s]/i.test(str);\n}\nfunction isLinkClose(str) {\n  return /^<\\/a\\s*>/i.test(str);\n}\nfunction isLetter(ch) {\n  /*eslint no-bitwise:0*/\n  var lc = ch | 0x20; // to lower case\n  return lc >= 0x61 /* a */ && lc <= 0x7a /* z */;\n}\n\nmodule.exports = function html_inline(state, silent) {\n  var ch,\n    match,\n    max,\n    token,\n    pos = state.pos;\n  if (!state.md.options.html) {\n    return false;\n  }\n\n  // Check start\n  max = state.posMax;\n  if (state.src.charCodeAt(pos) !== 0x3C /* < */ || pos + 2 >= max) {\n    return false;\n  }\n\n  // Quick fail on second char\n  ch = state.src.charCodeAt(pos + 1);\n  if (ch !== 0x21 /* ! */ && ch !== 0x3F /* ? */ && ch !== 0x2F /* / */ && !isLetter(ch)) {\n    return false;\n  }\n  match = state.src.slice(pos).match(HTML_TAG_RE);\n  if (!match) {\n    return false;\n  }\n  if (!silent) {\n    token = state.push('html_inline', '', 0);\n    token.content = state.src.slice(pos, pos + match[0].length);\n    if (isLinkOpen(token.content)) state.linkLevel++;\n    if (isLinkClose(token.content)) state.linkLevel--;\n  }\n  state.pos += match[0].length;\n  return true;\n};", "map": {"version": 3, "names": ["HTML_TAG_RE", "require", "isLinkOpen", "str", "test", "isLinkClose", "isLetter", "ch", "lc", "module", "exports", "html_inline", "state", "silent", "match", "max", "token", "pos", "md", "options", "html", "posMax", "src", "charCodeAt", "slice", "push", "content", "length", "linkLevel"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_inline/html_inline.js"], "sourcesContent": ["// Process html tags\n\n'use strict';\n\n\nvar HTML_TAG_RE = require('../common/html_re').HTML_TAG_RE;\n\n\nfunction isLinkOpen(str) {\n  return /^<a[>\\s]/i.test(str);\n}\nfunction isLinkClose(str) {\n  return /^<\\/a\\s*>/i.test(str);\n}\n\n\nfunction isLetter(ch) {\n  /*eslint no-bitwise:0*/\n  var lc = ch | 0x20; // to lower case\n  return (lc >= 0x61/* a */) && (lc <= 0x7a/* z */);\n}\n\n\nmodule.exports = function html_inline(state, silent) {\n  var ch, match, max, token,\n      pos = state.pos;\n\n  if (!state.md.options.html) { return false; }\n\n  // Check start\n  max = state.posMax;\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */ ||\n      pos + 2 >= max) {\n    return false;\n  }\n\n  // Quick fail on second char\n  ch = state.src.charCodeAt(pos + 1);\n  if (ch !== 0x21/* ! */ &&\n      ch !== 0x3F/* ? */ &&\n      ch !== 0x2F/* / */ &&\n      !isLetter(ch)) {\n    return false;\n  }\n\n  match = state.src.slice(pos).match(HTML_TAG_RE);\n  if (!match) { return false; }\n\n  if (!silent) {\n    token         = state.push('html_inline', '', 0);\n    token.content = state.src.slice(pos, pos + match[0].length);\n\n    if (isLinkOpen(token.content))  state.linkLevel++;\n    if (isLinkClose(token.content)) state.linkLevel--;\n  }\n  state.pos += match[0].length;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,mBAAmB,CAAC,CAACD,WAAW;AAG1D,SAASE,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAO,WAAW,CAACC,IAAI,CAACD,GAAG,CAAC;AAC9B;AACA,SAASE,WAAWA,CAACF,GAAG,EAAE;EACxB,OAAO,YAAY,CAACC,IAAI,CAACD,GAAG,CAAC;AAC/B;AAGA,SAASG,QAAQA,CAACC,EAAE,EAAE;EACpB;EACA,IAAIC,EAAE,GAAGD,EAAE,GAAG,IAAI,CAAC,CAAC;EACpB,OAAQC,EAAE,IAAI,IAAI,YAAaA,EAAE,IAAI,IAAI,QAAQ;AACnD;;AAGAC,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACnD,IAAIN,EAAE;IAAEO,KAAK;IAAEC,GAAG;IAAEC,KAAK;IACrBC,GAAG,GAAGL,KAAK,CAACK,GAAG;EAEnB,IAAI,CAACL,KAAK,CAACM,EAAE,CAACC,OAAO,CAACC,IAAI,EAAE;IAAE,OAAO,KAAK;EAAE;;EAE5C;EACAL,GAAG,GAAGH,KAAK,CAACS,MAAM;EAClB,IAAIT,KAAK,CAACU,GAAG,CAACC,UAAU,CAACN,GAAG,CAAC,KAAK,IAAI,YAClCA,GAAG,GAAG,CAAC,IAAIF,GAAG,EAAE;IAClB,OAAO,KAAK;EACd;;EAEA;EACAR,EAAE,GAAGK,KAAK,CAACU,GAAG,CAACC,UAAU,CAACN,GAAG,GAAG,CAAC,CAAC;EAClC,IAAIV,EAAE,KAAK,IAAI,YACXA,EAAE,KAAK,IAAI,YACXA,EAAE,KAAK,IAAI,YACX,CAACD,QAAQ,CAACC,EAAE,CAAC,EAAE;IACjB,OAAO,KAAK;EACd;EAEAO,KAAK,GAAGF,KAAK,CAACU,GAAG,CAACE,KAAK,CAACP,GAAG,CAAC,CAACH,KAAK,CAACd,WAAW,CAAC;EAC/C,IAAI,CAACc,KAAK,EAAE;IAAE,OAAO,KAAK;EAAE;EAE5B,IAAI,CAACD,MAAM,EAAE;IACXG,KAAK,GAAWJ,KAAK,CAACa,IAAI,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;IAChDT,KAAK,CAACU,OAAO,GAAGd,KAAK,CAACU,GAAG,CAACE,KAAK,CAACP,GAAG,EAAEA,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACa,MAAM,CAAC;IAE3D,IAAIzB,UAAU,CAACc,KAAK,CAACU,OAAO,CAAC,EAAGd,KAAK,CAACgB,SAAS,EAAE;IACjD,IAAIvB,WAAW,CAACW,KAAK,CAACU,OAAO,CAAC,EAAEd,KAAK,CAACgB,SAAS,EAAE;EACnD;EACAhB,KAAK,CAACK,GAAG,IAAIH,KAAK,CAAC,CAAC,CAAC,CAACa,MAAM;EAC5B,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}