{"ast": null, "code": "// Process [link](<to> \"stuff\")\n\n'use strict';\n\nvar normalizeReference = require('../common/utils').normalizeReference;\nvar isSpace = require('../common/utils').isSpace;\nmodule.exports = function link(state, silent) {\n  var attrs,\n    code,\n    label,\n    labelEnd,\n    labelStart,\n    pos,\n    res,\n    ref,\n    token,\n    href = '',\n    title = '',\n    oldPos = state.pos,\n    max = state.posMax,\n    start = state.pos,\n    parseReference = true;\n  if (state.src.charCodeAt(state.pos) !== 0x5B /* [ */) {\n    return false;\n  }\n  labelStart = state.pos + 1;\n  labelEnd = state.md.helpers.parseLinkLabel(state, state.pos, true);\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) {\n    return false;\n  }\n  pos = labelEnd + 1;\n  if (pos < max && state.src.charCodeAt(pos) === 0x28 /* ( */) {\n    //\n    // Inline link\n    //\n\n    // might have found a valid shortcut link, disable reference parsing\n    parseReference = false;\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!isSpace(code) && code !== 0x0A) {\n        break;\n      }\n    }\n    if (pos >= max) {\n      return false;\n    }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos;\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax);\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str);\n      if (state.md.validateLink(href)) {\n        pos = res.pos;\n      } else {\n        href = '';\n      }\n\n      // [link](  <href>  \"title\"  )\n      //                ^^ skipping these spaces\n      start = pos;\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos);\n        if (!isSpace(code) && code !== 0x0A) {\n          break;\n        }\n      }\n\n      // [link](  <href>  \"title\"  )\n      //                  ^^^^^^^ parsing link title\n      res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax);\n      if (pos < max && start !== pos && res.ok) {\n        title = res.str;\n        pos = res.pos;\n\n        // [link](  <href>  \"title\"  )\n        //                         ^^ skipping these spaces\n        for (; pos < max; pos++) {\n          code = state.src.charCodeAt(pos);\n          if (!isSpace(code) && code !== 0x0A) {\n            break;\n          }\n        }\n      }\n    }\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29 /* ) */) {\n      // parsing a valid shortcut link failed, fallback to reference\n      parseReference = true;\n    }\n    pos++;\n  }\n  if (parseReference) {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined') {\n      return false;\n    }\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B /* [ */) {\n      start = pos + 1;\n      pos = state.md.helpers.parseLinkLabel(state, pos);\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++);\n      } else {\n        pos = labelEnd + 1;\n      }\n    } else {\n      pos = labelEnd + 1;\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) {\n      label = state.src.slice(labelStart, labelEnd);\n    }\n    ref = state.env.references[normalizeReference(label)];\n    if (!ref) {\n      state.pos = oldPos;\n      return false;\n    }\n    href = ref.href;\n    title = ref.title;\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    state.pos = labelStart;\n    state.posMax = labelEnd;\n    token = state.push('link_open', 'a', 1);\n    token.attrs = attrs = [['href', href]];\n    if (title) {\n      attrs.push(['title', title]);\n    }\n    state.linkLevel++;\n    state.md.inline.tokenize(state);\n    state.linkLevel--;\n    token = state.push('link_close', 'a', -1);\n  }\n  state.pos = pos;\n  state.posMax = max;\n  return true;\n};", "map": {"version": 3, "names": ["normalizeReference", "require", "isSpace", "module", "exports", "link", "state", "silent", "attrs", "code", "label", "labelEnd", "labelStart", "pos", "res", "ref", "token", "href", "title", "oldPos", "max", "posMax", "start", "parseReference", "src", "charCodeAt", "md", "helpers", "parseLinkLabel", "parseLinkDestination", "ok", "normalizeLink", "str", "validateLink", "parseLinkTitle", "env", "references", "slice", "push", "linkLevel", "inline", "tokenize"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_inline/link.js"], "sourcesContent": ["// Process [link](<to> \"stuff\")\n\n'use strict';\n\nvar normalizeReference   = require('../common/utils').normalizeReference;\nvar isSpace              = require('../common/utils').isSpace;\n\n\nmodule.exports = function link(state, silent) {\n  var attrs,\n      code,\n      label,\n      labelEnd,\n      labelStart,\n      pos,\n      res,\n      ref,\n      token,\n      href = '',\n      title = '',\n      oldPos = state.pos,\n      max = state.posMax,\n      start = state.pos,\n      parseReference = true;\n\n  if (state.src.charCodeAt(state.pos) !== 0x5B/* [ */) { return false; }\n\n  labelStart = state.pos + 1;\n  labelEnd = state.md.helpers.parseLinkLabel(state, state.pos, true);\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) { return false; }\n\n  pos = labelEnd + 1;\n  if (pos < max && state.src.charCodeAt(pos) === 0x28/* ( */) {\n    //\n    // Inline link\n    //\n\n    // might have found a valid shortcut link, disable reference parsing\n    parseReference = false;\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!isSpace(code) && code !== 0x0A) { break; }\n    }\n    if (pos >= max) { return false; }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos;\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax);\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str);\n      if (state.md.validateLink(href)) {\n        pos = res.pos;\n      } else {\n        href = '';\n      }\n\n      // [link](  <href>  \"title\"  )\n      //                ^^ skipping these spaces\n      start = pos;\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos);\n        if (!isSpace(code) && code !== 0x0A) { break; }\n      }\n\n      // [link](  <href>  \"title\"  )\n      //                  ^^^^^^^ parsing link title\n      res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax);\n      if (pos < max && start !== pos && res.ok) {\n        title = res.str;\n        pos = res.pos;\n\n        // [link](  <href>  \"title\"  )\n        //                         ^^ skipping these spaces\n        for (; pos < max; pos++) {\n          code = state.src.charCodeAt(pos);\n          if (!isSpace(code) && code !== 0x0A) { break; }\n        }\n      }\n    }\n\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29/* ) */) {\n      // parsing a valid shortcut link failed, fallback to reference\n      parseReference = true;\n    }\n    pos++;\n  }\n\n  if (parseReference) {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined') { return false; }\n\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B/* [ */) {\n      start = pos + 1;\n      pos = state.md.helpers.parseLinkLabel(state, pos);\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++);\n      } else {\n        pos = labelEnd + 1;\n      }\n    } else {\n      pos = labelEnd + 1;\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) { label = state.src.slice(labelStart, labelEnd); }\n\n    ref = state.env.references[normalizeReference(label)];\n    if (!ref) {\n      state.pos = oldPos;\n      return false;\n    }\n    href = ref.href;\n    title = ref.title;\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    state.pos = labelStart;\n    state.posMax = labelEnd;\n\n    token        = state.push('link_open', 'a', 1);\n    token.attrs  = attrs = [ [ 'href', href ] ];\n    if (title) {\n      attrs.push([ 'title', title ]);\n    }\n\n    state.linkLevel++;\n    state.md.inline.tokenize(state);\n    state.linkLevel--;\n\n    token        = state.push('link_close', 'a', -1);\n  }\n\n  state.pos = pos;\n  state.posMax = max;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,kBAAkB,GAAKC,OAAO,CAAC,iBAAiB,CAAC,CAACD,kBAAkB;AACxE,IAAIE,OAAO,GAAgBD,OAAO,CAAC,iBAAiB,CAAC,CAACC,OAAO;AAG7DC,MAAM,CAACC,OAAO,GAAG,SAASC,IAAIA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC5C,IAAIC,KAAK;IACLC,IAAI;IACJC,KAAK;IACLC,QAAQ;IACRC,UAAU;IACVC,GAAG;IACHC,GAAG;IACHC,GAAG;IACHC,KAAK;IACLC,IAAI,GAAG,EAAE;IACTC,KAAK,GAAG,EAAE;IACVC,MAAM,GAAGb,KAAK,CAACO,GAAG;IAClBO,GAAG,GAAGd,KAAK,CAACe,MAAM;IAClBC,KAAK,GAAGhB,KAAK,CAACO,GAAG;IACjBU,cAAc,GAAG,IAAI;EAEzB,IAAIjB,KAAK,CAACkB,GAAG,CAACC,UAAU,CAACnB,KAAK,CAACO,GAAG,CAAC,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;EAErED,UAAU,GAAGN,KAAK,CAACO,GAAG,GAAG,CAAC;EAC1BF,QAAQ,GAAGL,KAAK,CAACoB,EAAE,CAACC,OAAO,CAACC,cAAc,CAACtB,KAAK,EAAEA,KAAK,CAACO,GAAG,EAAE,IAAI,CAAC;;EAElE;EACA,IAAIF,QAAQ,GAAG,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAElCE,GAAG,GAAGF,QAAQ,GAAG,CAAC;EAClB,IAAIE,GAAG,GAAGO,GAAG,IAAId,KAAK,CAACkB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC,KAAK,IAAI,UAAS;IAC1D;IACA;IACA;;IAEA;IACAU,cAAc,GAAG,KAAK;;IAEtB;IACA;IACAV,GAAG,EAAE;IACL,OAAOA,GAAG,GAAGO,GAAG,EAAEP,GAAG,EAAE,EAAE;MACvBJ,IAAI,GAAGH,KAAK,CAACkB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC;MAChC,IAAI,CAACX,OAAO,CAACO,IAAI,CAAC,IAAIA,IAAI,KAAK,IAAI,EAAE;QAAE;MAAO;IAChD;IACA,IAAII,GAAG,IAAIO,GAAG,EAAE;MAAE,OAAO,KAAK;IAAE;;IAEhC;IACA;IACAE,KAAK,GAAGT,GAAG;IACXC,GAAG,GAAGR,KAAK,CAACoB,EAAE,CAACC,OAAO,CAACE,oBAAoB,CAACvB,KAAK,CAACkB,GAAG,EAAEX,GAAG,EAAEP,KAAK,CAACe,MAAM,CAAC;IACzE,IAAIP,GAAG,CAACgB,EAAE,EAAE;MACVb,IAAI,GAAGX,KAAK,CAACoB,EAAE,CAACK,aAAa,CAACjB,GAAG,CAACkB,GAAG,CAAC;MACtC,IAAI1B,KAAK,CAACoB,EAAE,CAACO,YAAY,CAAChB,IAAI,CAAC,EAAE;QAC/BJ,GAAG,GAAGC,GAAG,CAACD,GAAG;MACf,CAAC,MAAM;QACLI,IAAI,GAAG,EAAE;MACX;;MAEA;MACA;MACAK,KAAK,GAAGT,GAAG;MACX,OAAOA,GAAG,GAAGO,GAAG,EAAEP,GAAG,EAAE,EAAE;QACvBJ,IAAI,GAAGH,KAAK,CAACkB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC;QAChC,IAAI,CAACX,OAAO,CAACO,IAAI,CAAC,IAAIA,IAAI,KAAK,IAAI,EAAE;UAAE;QAAO;MAChD;;MAEA;MACA;MACAK,GAAG,GAAGR,KAAK,CAACoB,EAAE,CAACC,OAAO,CAACO,cAAc,CAAC5B,KAAK,CAACkB,GAAG,EAAEX,GAAG,EAAEP,KAAK,CAACe,MAAM,CAAC;MACnE,IAAIR,GAAG,GAAGO,GAAG,IAAIE,KAAK,KAAKT,GAAG,IAAIC,GAAG,CAACgB,EAAE,EAAE;QACxCZ,KAAK,GAAGJ,GAAG,CAACkB,GAAG;QACfnB,GAAG,GAAGC,GAAG,CAACD,GAAG;;QAEb;QACA;QACA,OAAOA,GAAG,GAAGO,GAAG,EAAEP,GAAG,EAAE,EAAE;UACvBJ,IAAI,GAAGH,KAAK,CAACkB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC;UAChC,IAAI,CAACX,OAAO,CAACO,IAAI,CAAC,IAAIA,IAAI,KAAK,IAAI,EAAE;YAAE;UAAO;QAChD;MACF;IACF;IAEA,IAAII,GAAG,IAAIO,GAAG,IAAId,KAAK,CAACkB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC,KAAK,IAAI,UAAS;MAC3D;MACAU,cAAc,GAAG,IAAI;IACvB;IACAV,GAAG,EAAE;EACP;EAEA,IAAIU,cAAc,EAAE;IAClB;IACA;IACA;IACA,IAAI,OAAOjB,KAAK,CAAC6B,GAAG,CAACC,UAAU,KAAK,WAAW,EAAE;MAAE,OAAO,KAAK;IAAE;IAEjE,IAAIvB,GAAG,GAAGO,GAAG,IAAId,KAAK,CAACkB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC,KAAK,IAAI,UAAS;MAC1DS,KAAK,GAAGT,GAAG,GAAG,CAAC;MACfA,GAAG,GAAGP,KAAK,CAACoB,EAAE,CAACC,OAAO,CAACC,cAAc,CAACtB,KAAK,EAAEO,GAAG,CAAC;MACjD,IAAIA,GAAG,IAAI,CAAC,EAAE;QACZH,KAAK,GAAGJ,KAAK,CAACkB,GAAG,CAACa,KAAK,CAACf,KAAK,EAAET,GAAG,EAAE,CAAC;MACvC,CAAC,MAAM;QACLA,GAAG,GAAGF,QAAQ,GAAG,CAAC;MACpB;IACF,CAAC,MAAM;MACLE,GAAG,GAAGF,QAAQ,GAAG,CAAC;IACpB;;IAEA;IACA;IACA,IAAI,CAACD,KAAK,EAAE;MAAEA,KAAK,GAAGJ,KAAK,CAACkB,GAAG,CAACa,KAAK,CAACzB,UAAU,EAAED,QAAQ,CAAC;IAAE;IAE7DI,GAAG,GAAGT,KAAK,CAAC6B,GAAG,CAACC,UAAU,CAACpC,kBAAkB,CAACU,KAAK,CAAC,CAAC;IACrD,IAAI,CAACK,GAAG,EAAE;MACRT,KAAK,CAACO,GAAG,GAAGM,MAAM;MAClB,OAAO,KAAK;IACd;IACAF,IAAI,GAAGF,GAAG,CAACE,IAAI;IACfC,KAAK,GAAGH,GAAG,CAACG,KAAK;EACnB;;EAEA;EACA;EACA;EACA;EACA,IAAI,CAACX,MAAM,EAAE;IACXD,KAAK,CAACO,GAAG,GAAGD,UAAU;IACtBN,KAAK,CAACe,MAAM,GAAGV,QAAQ;IAEvBK,KAAK,GAAUV,KAAK,CAACgC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;IAC9CtB,KAAK,CAACR,KAAK,GAAIA,KAAK,GAAG,CAAE,CAAE,MAAM,EAAES,IAAI,CAAE,CAAE;IAC3C,IAAIC,KAAK,EAAE;MACTV,KAAK,CAAC8B,IAAI,CAAC,CAAE,OAAO,EAAEpB,KAAK,CAAE,CAAC;IAChC;IAEAZ,KAAK,CAACiC,SAAS,EAAE;IACjBjC,KAAK,CAACoB,EAAE,CAACc,MAAM,CAACC,QAAQ,CAACnC,KAAK,CAAC;IAC/BA,KAAK,CAACiC,SAAS,EAAE;IAEjBvB,KAAK,GAAUV,KAAK,CAACgC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;EAClD;EAEAhC,KAAK,CAACO,GAAG,GAAGA,GAAG;EACfP,KAAK,CAACe,MAAM,GAAGD,GAAG;EAClB,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}