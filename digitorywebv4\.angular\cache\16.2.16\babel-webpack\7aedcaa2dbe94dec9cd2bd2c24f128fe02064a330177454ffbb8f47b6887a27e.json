{"ast": null, "code": "// Process autolinks '<protocol:...>'\n\n'use strict';\n\n/*eslint max-len:0*/\nvar EMAIL_RE = /^([a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/;\nvar AUTOLINK_RE = /^([a-zA-Z][a-zA-Z0-9+.\\-]{1,31}):([^<>\\x00-\\x20]*)$/;\nmodule.exports = function autolink(state, silent) {\n  var url,\n    fullUrl,\n    token,\n    ch,\n    start,\n    max,\n    pos = state.pos;\n  if (state.src.charCodeAt(pos) !== 0x3C /* < */) {\n    return false;\n  }\n  start = state.pos;\n  max = state.posMax;\n  for (;;) {\n    if (++pos >= max) return false;\n    ch = state.src.charCodeAt(pos);\n    if (ch === 0x3C /* < */) return false;\n    if (ch === 0x3E /* > */) break;\n  }\n  url = state.src.slice(start + 1, pos);\n  if (AUTOLINK_RE.test(url)) {\n    fullUrl = state.md.normalizeLink(url);\n    if (!state.md.validateLink(fullUrl)) {\n      return false;\n    }\n    if (!silent) {\n      token = state.push('link_open', 'a', 1);\n      token.attrs = [['href', fullUrl]];\n      token.markup = 'autolink';\n      token.info = 'auto';\n      token = state.push('text', '', 0);\n      token.content = state.md.normalizeLinkText(url);\n      token = state.push('link_close', 'a', -1);\n      token.markup = 'autolink';\n      token.info = 'auto';\n    }\n    state.pos += url.length + 2;\n    return true;\n  }\n  if (EMAIL_RE.test(url)) {\n    fullUrl = state.md.normalizeLink('mailto:' + url);\n    if (!state.md.validateLink(fullUrl)) {\n      return false;\n    }\n    if (!silent) {\n      token = state.push('link_open', 'a', 1);\n      token.attrs = [['href', fullUrl]];\n      token.markup = 'autolink';\n      token.info = 'auto';\n      token = state.push('text', '', 0);\n      token.content = state.md.normalizeLinkText(url);\n      token = state.push('link_close', 'a', -1);\n      token.markup = 'autolink';\n      token.info = 'auto';\n    }\n    state.pos += url.length + 2;\n    return true;\n  }\n  return false;\n};", "map": {"version": 3, "names": ["EMAIL_RE", "AUTOLINK_RE", "module", "exports", "autolink", "state", "silent", "url", "fullUrl", "token", "ch", "start", "max", "pos", "src", "charCodeAt", "posMax", "slice", "test", "md", "normalizeLink", "validateLink", "push", "attrs", "markup", "info", "content", "normalizeLinkText", "length"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_inline/autolink.js"], "sourcesContent": ["// Process autolinks '<protocol:...>'\n\n'use strict';\n\n\n/*eslint max-len:0*/\nvar EMAIL_RE    = /^([a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/;\nvar AUTOLINK_RE = /^([a-zA-Z][a-zA-Z0-9+.\\-]{1,31}):([^<>\\x00-\\x20]*)$/;\n\n\nmodule.exports = function autolink(state, silent) {\n  var url, fullUrl, token, ch, start, max,\n      pos = state.pos;\n\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */) { return false; }\n\n  start = state.pos;\n  max = state.posMax;\n\n  for (;;) {\n    if (++pos >= max) return false;\n\n    ch = state.src.charCodeAt(pos);\n\n    if (ch === 0x3C /* < */) return false;\n    if (ch === 0x3E /* > */) break;\n  }\n\n  url = state.src.slice(start + 1, pos);\n\n  if (AUTOLINK_RE.test(url)) {\n    fullUrl = state.md.normalizeLink(url);\n    if (!state.md.validateLink(fullUrl)) { return false; }\n\n    if (!silent) {\n      token         = state.push('link_open', 'a', 1);\n      token.attrs   = [ [ 'href', fullUrl ] ];\n      token.markup  = 'autolink';\n      token.info    = 'auto';\n\n      token         = state.push('text', '', 0);\n      token.content = state.md.normalizeLinkText(url);\n\n      token         = state.push('link_close', 'a', -1);\n      token.markup  = 'autolink';\n      token.info    = 'auto';\n    }\n\n    state.pos += url.length + 2;\n    return true;\n  }\n\n  if (EMAIL_RE.test(url)) {\n    fullUrl = state.md.normalizeLink('mailto:' + url);\n    if (!state.md.validateLink(fullUrl)) { return false; }\n\n    if (!silent) {\n      token         = state.push('link_open', 'a', 1);\n      token.attrs   = [ [ 'href', fullUrl ] ];\n      token.markup  = 'autolink';\n      token.info    = 'auto';\n\n      token         = state.push('text', '', 0);\n      token.content = state.md.normalizeLinkText(url);\n\n      token         = state.push('link_close', 'a', -1);\n      token.markup  = 'autolink';\n      token.info    = 'auto';\n    }\n\n    state.pos += url.length + 2;\n    return true;\n  }\n\n  return false;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZ;AACA,IAAIA,QAAQ,GAAM,yIAAyI;AAC3J,IAAIC,WAAW,GAAG,qDAAqD;AAGvEC,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAChD,IAAIC,GAAG;IAAEC,OAAO;IAAEC,KAAK;IAAEC,EAAE;IAAEC,KAAK;IAAEC,GAAG;IACnCC,GAAG,GAAGR,KAAK,CAACQ,GAAG;EAEnB,IAAIR,KAAK,CAACS,GAAG,CAACC,UAAU,CAACF,GAAG,CAAC,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;EAE/DF,KAAK,GAAGN,KAAK,CAACQ,GAAG;EACjBD,GAAG,GAAGP,KAAK,CAACW,MAAM;EAElB,SAAS;IACP,IAAI,EAAEH,GAAG,IAAID,GAAG,EAAE,OAAO,KAAK;IAE9BF,EAAE,GAAGL,KAAK,CAACS,GAAG,CAACC,UAAU,CAACF,GAAG,CAAC;IAE9B,IAAIH,EAAE,KAAK,IAAI,CAAC,SAAS,OAAO,KAAK;IACrC,IAAIA,EAAE,KAAK,IAAI,CAAC,SAAS;EAC3B;EAEAH,GAAG,GAAGF,KAAK,CAACS,GAAG,CAACG,KAAK,CAACN,KAAK,GAAG,CAAC,EAAEE,GAAG,CAAC;EAErC,IAAIZ,WAAW,CAACiB,IAAI,CAACX,GAAG,CAAC,EAAE;IACzBC,OAAO,GAAGH,KAAK,CAACc,EAAE,CAACC,aAAa,CAACb,GAAG,CAAC;IACrC,IAAI,CAACF,KAAK,CAACc,EAAE,CAACE,YAAY,CAACb,OAAO,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;IAErD,IAAI,CAACF,MAAM,EAAE;MACXG,KAAK,GAAWJ,KAAK,CAACiB,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;MAC/Cb,KAAK,CAACc,KAAK,GAAK,CAAE,CAAE,MAAM,EAAEf,OAAO,CAAE,CAAE;MACvCC,KAAK,CAACe,MAAM,GAAI,UAAU;MAC1Bf,KAAK,CAACgB,IAAI,GAAM,MAAM;MAEtBhB,KAAK,GAAWJ,KAAK,CAACiB,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;MACzCb,KAAK,CAACiB,OAAO,GAAGrB,KAAK,CAACc,EAAE,CAACQ,iBAAiB,CAACpB,GAAG,CAAC;MAE/CE,KAAK,GAAWJ,KAAK,CAACiB,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;MACjDb,KAAK,CAACe,MAAM,GAAI,UAAU;MAC1Bf,KAAK,CAACgB,IAAI,GAAM,MAAM;IACxB;IAEApB,KAAK,CAACQ,GAAG,IAAIN,GAAG,CAACqB,MAAM,GAAG,CAAC;IAC3B,OAAO,IAAI;EACb;EAEA,IAAI5B,QAAQ,CAACkB,IAAI,CAACX,GAAG,CAAC,EAAE;IACtBC,OAAO,GAAGH,KAAK,CAACc,EAAE,CAACC,aAAa,CAAC,SAAS,GAAGb,GAAG,CAAC;IACjD,IAAI,CAACF,KAAK,CAACc,EAAE,CAACE,YAAY,CAACb,OAAO,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;IAErD,IAAI,CAACF,MAAM,EAAE;MACXG,KAAK,GAAWJ,KAAK,CAACiB,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;MAC/Cb,KAAK,CAACc,KAAK,GAAK,CAAE,CAAE,MAAM,EAAEf,OAAO,CAAE,CAAE;MACvCC,KAAK,CAACe,MAAM,GAAI,UAAU;MAC1Bf,KAAK,CAACgB,IAAI,GAAM,MAAM;MAEtBhB,KAAK,GAAWJ,KAAK,CAACiB,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;MACzCb,KAAK,CAACiB,OAAO,GAAGrB,KAAK,CAACc,EAAE,CAACQ,iBAAiB,CAACpB,GAAG,CAAC;MAE/CE,KAAK,GAAWJ,KAAK,CAACiB,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;MACjDb,KAAK,CAACe,MAAM,GAAI,UAAU;MAC1Bf,KAAK,CAACgB,IAAI,GAAM,MAAM;IACxB;IAEApB,KAAK,CAACQ,GAAG,IAAIN,GAAG,CAACqB,MAAM,GAAG,CAAC;IAC3B,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}