{"ast": null, "code": "// Convert straight quotation marks to typographic ones\n//\n'use strict';\n\nvar isWhiteSpace = require('../common/utils').isWhiteSpace;\nvar isPunctChar = require('../common/utils').isPunctChar;\nvar isMdAsciiPunct = require('../common/utils').isMdAsciiPunct;\nvar QUOTE_TEST_RE = /['\"]/;\nvar QUOTE_RE = /['\"]/g;\nvar APOSTROPHE = '\\u2019'; /* ’ */\n\nfunction replaceAt(str, index, ch) {\n  return str.slice(0, index) + ch + str.slice(index + 1);\n}\nfunction process_inlines(tokens, state) {\n  var i, token, text, t, pos, max, thisLevel, item, lastChar, nextChar, isLastPunctChar, isNextPunctChar, isLastWhiteSpace, isNextWhiteSpace, canOpen, canClose, j, isSingle, stack, openQuote, closeQuote;\n  stack = [];\n  for (i = 0; i < tokens.length; i++) {\n    token = tokens[i];\n    thisLevel = tokens[i].level;\n    for (j = stack.length - 1; j >= 0; j--) {\n      if (stack[j].level <= thisLevel) {\n        break;\n      }\n    }\n    stack.length = j + 1;\n    if (token.type !== 'text') {\n      continue;\n    }\n    text = token.content;\n    pos = 0;\n    max = text.length;\n\n    /*eslint no-labels:0,block-scoped-var:0*/\n    OUTER: while (pos < max) {\n      QUOTE_RE.lastIndex = pos;\n      t = QUOTE_RE.exec(text);\n      if (!t) {\n        break;\n      }\n      canOpen = canClose = true;\n      pos = t.index + 1;\n      isSingle = t[0] === \"'\";\n\n      // Find previous character,\n      // default to space if it's the beginning of the line\n      //\n      lastChar = 0x20;\n      if (t.index - 1 >= 0) {\n        lastChar = text.charCodeAt(t.index - 1);\n      } else {\n        for (j = i - 1; j >= 0; j--) {\n          if (tokens[j].type === 'softbreak' || tokens[j].type === 'hardbreak') break; // lastChar defaults to 0x20\n          if (!tokens[j].content) continue; // should skip all tokens except 'text', 'html_inline' or 'code_inline'\n\n          lastChar = tokens[j].content.charCodeAt(tokens[j].content.length - 1);\n          break;\n        }\n      }\n\n      // Find next character,\n      // default to space if it's the end of the line\n      //\n      nextChar = 0x20;\n      if (pos < max) {\n        nextChar = text.charCodeAt(pos);\n      } else {\n        for (j = i + 1; j < tokens.length; j++) {\n          if (tokens[j].type === 'softbreak' || tokens[j].type === 'hardbreak') break; // nextChar defaults to 0x20\n          if (!tokens[j].content) continue; // should skip all tokens except 'text', 'html_inline' or 'code_inline'\n\n          nextChar = tokens[j].content.charCodeAt(0);\n          break;\n        }\n      }\n      isLastPunctChar = isMdAsciiPunct(lastChar) || isPunctChar(String.fromCharCode(lastChar));\n      isNextPunctChar = isMdAsciiPunct(nextChar) || isPunctChar(String.fromCharCode(nextChar));\n      isLastWhiteSpace = isWhiteSpace(lastChar);\n      isNextWhiteSpace = isWhiteSpace(nextChar);\n      if (isNextWhiteSpace) {\n        canOpen = false;\n      } else if (isNextPunctChar) {\n        if (!(isLastWhiteSpace || isLastPunctChar)) {\n          canOpen = false;\n        }\n      }\n      if (isLastWhiteSpace) {\n        canClose = false;\n      } else if (isLastPunctChar) {\n        if (!(isNextWhiteSpace || isNextPunctChar)) {\n          canClose = false;\n        }\n      }\n      if (nextChar === 0x22 /* \" */ && t[0] === '\"') {\n        if (lastChar >= 0x30 /* 0 */ && lastChar <= 0x39 /* 9 */) {\n          // special case: 1\"\" - count first quote as an inch\n          canClose = canOpen = false;\n        }\n      }\n      if (canOpen && canClose) {\n        // Replace quotes in the middle of punctuation sequence, but not\n        // in the middle of the words, i.e.:\n        //\n        // 1. foo \" bar \" baz - not replaced\n        // 2. foo-\"-bar-\"-baz - replaced\n        // 3. foo\"bar\"baz     - not replaced\n        //\n        canOpen = isLastPunctChar;\n        canClose = isNextPunctChar;\n      }\n      if (!canOpen && !canClose) {\n        // middle of word\n        if (isSingle) {\n          token.content = replaceAt(token.content, t.index, APOSTROPHE);\n        }\n        continue;\n      }\n      if (canClose) {\n        // this could be a closing quote, rewind the stack to get a match\n        for (j = stack.length - 1; j >= 0; j--) {\n          item = stack[j];\n          if (stack[j].level < thisLevel) {\n            break;\n          }\n          if (item.single === isSingle && stack[j].level === thisLevel) {\n            item = stack[j];\n            if (isSingle) {\n              openQuote = state.md.options.quotes[2];\n              closeQuote = state.md.options.quotes[3];\n            } else {\n              openQuote = state.md.options.quotes[0];\n              closeQuote = state.md.options.quotes[1];\n            }\n\n            // replace token.content *before* tokens[item.token].content,\n            // because, if they are pointing at the same token, replaceAt\n            // could mess up indices when quote length != 1\n            token.content = replaceAt(token.content, t.index, closeQuote);\n            tokens[item.token].content = replaceAt(tokens[item.token].content, item.pos, openQuote);\n            pos += closeQuote.length - 1;\n            if (item.token === i) {\n              pos += openQuote.length - 1;\n            }\n            text = token.content;\n            max = text.length;\n            stack.length = j;\n            continue OUTER;\n          }\n        }\n      }\n      if (canOpen) {\n        stack.push({\n          token: i,\n          pos: t.index,\n          single: isSingle,\n          level: thisLevel\n        });\n      } else if (canClose && isSingle) {\n        token.content = replaceAt(token.content, t.index, APOSTROPHE);\n      }\n    }\n  }\n}\nmodule.exports = function smartquotes(state) {\n  /*eslint max-depth:0*/\n  var blkIdx;\n  if (!state.md.options.typographer) {\n    return;\n  }\n  for (blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n    if (state.tokens[blkIdx].type !== 'inline' || !QUOTE_TEST_RE.test(state.tokens[blkIdx].content)) {\n      continue;\n    }\n    process_inlines(state.tokens[blkIdx].children, state);\n  }\n};", "map": {"version": 3, "names": ["isWhiteSpace", "require", "isPunctChar", "isMdAsciiPunct", "QUOTE_TEST_RE", "QUOTE_RE", "APOSTROPHE", "replaceAt", "str", "index", "ch", "slice", "process_inlines", "tokens", "state", "i", "token", "text", "t", "pos", "max", "thisLevel", "item", "lastChar", "nextChar", "isLastPunctChar", "isNextPunctChar", "isLastWhiteSpace", "isNextWhiteSpace", "canOpen", "canClose", "j", "isSingle", "stack", "openQuote", "closeQuote", "length", "level", "type", "content", "OUTER", "lastIndex", "exec", "charCodeAt", "String", "fromCharCode", "single", "md", "options", "quotes", "push", "module", "exports", "smartquotes", "blkIdx", "typographer", "test", "children"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_core/smartquotes.js"], "sourcesContent": ["// Convert straight quotation marks to typographic ones\n//\n'use strict';\n\n\nvar isWhiteSpace   = require('../common/utils').isWhiteSpace;\nvar isPunctChar    = require('../common/utils').isPunctChar;\nvar isMdAsciiPunct = require('../common/utils').isMdAsciiPunct;\n\nvar QUOTE_TEST_RE = /['\"]/;\nvar QUOTE_RE = /['\"]/g;\nvar APOSTROPHE = '\\u2019'; /* ’ */\n\n\nfunction replaceAt(str, index, ch) {\n  return str.slice(0, index) + ch + str.slice(index + 1);\n}\n\nfunction process_inlines(tokens, state) {\n  var i, token, text, t, pos, max, thisLevel, item, lastChar, nextChar,\n      isLastPunctChar, isNextPunctChar, isLastWhiteSpace, isNextWhiteSpace,\n      canOpen, canClose, j, isSingle, stack, openQuote, closeQuote;\n\n  stack = [];\n\n  for (i = 0; i < tokens.length; i++) {\n    token = tokens[i];\n\n    thisLevel = tokens[i].level;\n\n    for (j = stack.length - 1; j >= 0; j--) {\n      if (stack[j].level <= thisLevel) { break; }\n    }\n    stack.length = j + 1;\n\n    if (token.type !== 'text') { continue; }\n\n    text = token.content;\n    pos = 0;\n    max = text.length;\n\n    /*eslint no-labels:0,block-scoped-var:0*/\n    OUTER:\n    while (pos < max) {\n      QUOTE_RE.lastIndex = pos;\n      t = QUOTE_RE.exec(text);\n      if (!t) { break; }\n\n      canOpen = canClose = true;\n      pos = t.index + 1;\n      isSingle = (t[0] === \"'\");\n\n      // Find previous character,\n      // default to space if it's the beginning of the line\n      //\n      lastChar = 0x20;\n\n      if (t.index - 1 >= 0) {\n        lastChar = text.charCodeAt(t.index - 1);\n      } else {\n        for (j = i - 1; j >= 0; j--) {\n          if (tokens[j].type === 'softbreak' || tokens[j].type === 'hardbreak') break; // lastChar defaults to 0x20\n          if (!tokens[j].content) continue; // should skip all tokens except 'text', 'html_inline' or 'code_inline'\n\n          lastChar = tokens[j].content.charCodeAt(tokens[j].content.length - 1);\n          break;\n        }\n      }\n\n      // Find next character,\n      // default to space if it's the end of the line\n      //\n      nextChar = 0x20;\n\n      if (pos < max) {\n        nextChar = text.charCodeAt(pos);\n      } else {\n        for (j = i + 1; j < tokens.length; j++) {\n          if (tokens[j].type === 'softbreak' || tokens[j].type === 'hardbreak') break; // nextChar defaults to 0x20\n          if (!tokens[j].content) continue; // should skip all tokens except 'text', 'html_inline' or 'code_inline'\n\n          nextChar = tokens[j].content.charCodeAt(0);\n          break;\n        }\n      }\n\n      isLastPunctChar = isMdAsciiPunct(lastChar) || isPunctChar(String.fromCharCode(lastChar));\n      isNextPunctChar = isMdAsciiPunct(nextChar) || isPunctChar(String.fromCharCode(nextChar));\n\n      isLastWhiteSpace = isWhiteSpace(lastChar);\n      isNextWhiteSpace = isWhiteSpace(nextChar);\n\n      if (isNextWhiteSpace) {\n        canOpen = false;\n      } else if (isNextPunctChar) {\n        if (!(isLastWhiteSpace || isLastPunctChar)) {\n          canOpen = false;\n        }\n      }\n\n      if (isLastWhiteSpace) {\n        canClose = false;\n      } else if (isLastPunctChar) {\n        if (!(isNextWhiteSpace || isNextPunctChar)) {\n          canClose = false;\n        }\n      }\n\n      if (nextChar === 0x22 /* \" */ && t[0] === '\"') {\n        if (lastChar >= 0x30 /* 0 */ && lastChar <= 0x39 /* 9 */) {\n          // special case: 1\"\" - count first quote as an inch\n          canClose = canOpen = false;\n        }\n      }\n\n      if (canOpen && canClose) {\n        // Replace quotes in the middle of punctuation sequence, but not\n        // in the middle of the words, i.e.:\n        //\n        // 1. foo \" bar \" baz - not replaced\n        // 2. foo-\"-bar-\"-baz - replaced\n        // 3. foo\"bar\"baz     - not replaced\n        //\n        canOpen = isLastPunctChar;\n        canClose = isNextPunctChar;\n      }\n\n      if (!canOpen && !canClose) {\n        // middle of word\n        if (isSingle) {\n          token.content = replaceAt(token.content, t.index, APOSTROPHE);\n        }\n        continue;\n      }\n\n      if (canClose) {\n        // this could be a closing quote, rewind the stack to get a match\n        for (j = stack.length - 1; j >= 0; j--) {\n          item = stack[j];\n          if (stack[j].level < thisLevel) { break; }\n          if (item.single === isSingle && stack[j].level === thisLevel) {\n            item = stack[j];\n\n            if (isSingle) {\n              openQuote = state.md.options.quotes[2];\n              closeQuote = state.md.options.quotes[3];\n            } else {\n              openQuote = state.md.options.quotes[0];\n              closeQuote = state.md.options.quotes[1];\n            }\n\n            // replace token.content *before* tokens[item.token].content,\n            // because, if they are pointing at the same token, replaceAt\n            // could mess up indices when quote length != 1\n            token.content = replaceAt(token.content, t.index, closeQuote);\n            tokens[item.token].content = replaceAt(\n              tokens[item.token].content, item.pos, openQuote);\n\n            pos += closeQuote.length - 1;\n            if (item.token === i) { pos += openQuote.length - 1; }\n\n            text = token.content;\n            max = text.length;\n\n            stack.length = j;\n            continue OUTER;\n          }\n        }\n      }\n\n      if (canOpen) {\n        stack.push({\n          token: i,\n          pos: t.index,\n          single: isSingle,\n          level: thisLevel\n        });\n      } else if (canClose && isSingle) {\n        token.content = replaceAt(token.content, t.index, APOSTROPHE);\n      }\n    }\n  }\n}\n\n\nmodule.exports = function smartquotes(state) {\n  /*eslint max-depth:0*/\n  var blkIdx;\n\n  if (!state.md.options.typographer) { return; }\n\n  for (blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n\n    if (state.tokens[blkIdx].type !== 'inline' ||\n        !QUOTE_TEST_RE.test(state.tokens[blkIdx].content)) {\n      continue;\n    }\n\n    process_inlines(state.tokens[blkIdx].children, state);\n  }\n};\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAGZ,IAAIA,YAAY,GAAKC,OAAO,CAAC,iBAAiB,CAAC,CAACD,YAAY;AAC5D,IAAIE,WAAW,GAAMD,OAAO,CAAC,iBAAiB,CAAC,CAACC,WAAW;AAC3D,IAAIC,cAAc,GAAGF,OAAO,CAAC,iBAAiB,CAAC,CAACE,cAAc;AAE9D,IAAIC,aAAa,GAAG,MAAM;AAC1B,IAAIC,QAAQ,GAAG,OAAO;AACtB,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;;AAG3B,SAASC,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAEC,EAAE,EAAE;EACjC,OAAOF,GAAG,CAACG,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC,GAAGC,EAAE,GAAGF,GAAG,CAACG,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC;AACxD;AAEA,SAASG,eAAeA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACtC,IAAIC,CAAC,EAAEC,KAAK,EAAEC,IAAI,EAAEC,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAChEC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,gBAAgB,EACpEC,OAAO,EAAEC,QAAQ,EAAEC,CAAC,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU;EAEhEF,KAAK,GAAG,EAAE;EAEV,KAAKlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACuB,MAAM,EAAErB,CAAC,EAAE,EAAE;IAClCC,KAAK,GAAGH,MAAM,CAACE,CAAC,CAAC;IAEjBM,SAAS,GAAGR,MAAM,CAACE,CAAC,CAAC,CAACsB,KAAK;IAE3B,KAAKN,CAAC,GAAGE,KAAK,CAACG,MAAM,GAAG,CAAC,EAAEL,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACtC,IAAIE,KAAK,CAACF,CAAC,CAAC,CAACM,KAAK,IAAIhB,SAAS,EAAE;QAAE;MAAO;IAC5C;IACAY,KAAK,CAACG,MAAM,GAAGL,CAAC,GAAG,CAAC;IAEpB,IAAIf,KAAK,CAACsB,IAAI,KAAK,MAAM,EAAE;MAAE;IAAU;IAEvCrB,IAAI,GAAGD,KAAK,CAACuB,OAAO;IACpBpB,GAAG,GAAG,CAAC;IACPC,GAAG,GAAGH,IAAI,CAACmB,MAAM;;IAEjB;IACAI,KAAK,EACL,OAAOrB,GAAG,GAAGC,GAAG,EAAE;MAChBf,QAAQ,CAACoC,SAAS,GAAGtB,GAAG;MACxBD,CAAC,GAAGb,QAAQ,CAACqC,IAAI,CAACzB,IAAI,CAAC;MACvB,IAAI,CAACC,CAAC,EAAE;QAAE;MAAO;MAEjBW,OAAO,GAAGC,QAAQ,GAAG,IAAI;MACzBX,GAAG,GAAGD,CAAC,CAACT,KAAK,GAAG,CAAC;MACjBuB,QAAQ,GAAId,CAAC,CAAC,CAAC,CAAC,KAAK,GAAI;;MAEzB;MACA;MACA;MACAK,QAAQ,GAAG,IAAI;MAEf,IAAIL,CAAC,CAACT,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE;QACpBc,QAAQ,GAAGN,IAAI,CAAC0B,UAAU,CAACzB,CAAC,CAACT,KAAK,GAAG,CAAC,CAAC;MACzC,CAAC,MAAM;QACL,KAAKsB,CAAC,GAAGhB,CAAC,GAAG,CAAC,EAAEgB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC3B,IAAIlB,MAAM,CAACkB,CAAC,CAAC,CAACO,IAAI,KAAK,WAAW,IAAIzB,MAAM,CAACkB,CAAC,CAAC,CAACO,IAAI,KAAK,WAAW,EAAE,MAAM,CAAC;UAC7E,IAAI,CAACzB,MAAM,CAACkB,CAAC,CAAC,CAACQ,OAAO,EAAE,SAAS,CAAC;;UAElChB,QAAQ,GAAGV,MAAM,CAACkB,CAAC,CAAC,CAACQ,OAAO,CAACI,UAAU,CAAC9B,MAAM,CAACkB,CAAC,CAAC,CAACQ,OAAO,CAACH,MAAM,GAAG,CAAC,CAAC;UACrE;QACF;MACF;;MAEA;MACA;MACA;MACAZ,QAAQ,GAAG,IAAI;MAEf,IAAIL,GAAG,GAAGC,GAAG,EAAE;QACbI,QAAQ,GAAGP,IAAI,CAAC0B,UAAU,CAACxB,GAAG,CAAC;MACjC,CAAC,MAAM;QACL,KAAKY,CAAC,GAAGhB,CAAC,GAAG,CAAC,EAAEgB,CAAC,GAAGlB,MAAM,CAACuB,MAAM,EAAEL,CAAC,EAAE,EAAE;UACtC,IAAIlB,MAAM,CAACkB,CAAC,CAAC,CAACO,IAAI,KAAK,WAAW,IAAIzB,MAAM,CAACkB,CAAC,CAAC,CAACO,IAAI,KAAK,WAAW,EAAE,MAAM,CAAC;UAC7E,IAAI,CAACzB,MAAM,CAACkB,CAAC,CAAC,CAACQ,OAAO,EAAE,SAAS,CAAC;;UAElCf,QAAQ,GAAGX,MAAM,CAACkB,CAAC,CAAC,CAACQ,OAAO,CAACI,UAAU,CAAC,CAAC,CAAC;UAC1C;QACF;MACF;MAEAlB,eAAe,GAAGtB,cAAc,CAACoB,QAAQ,CAAC,IAAIrB,WAAW,CAAC0C,MAAM,CAACC,YAAY,CAACtB,QAAQ,CAAC,CAAC;MACxFG,eAAe,GAAGvB,cAAc,CAACqB,QAAQ,CAAC,IAAItB,WAAW,CAAC0C,MAAM,CAACC,YAAY,CAACrB,QAAQ,CAAC,CAAC;MAExFG,gBAAgB,GAAG3B,YAAY,CAACuB,QAAQ,CAAC;MACzCK,gBAAgB,GAAG5B,YAAY,CAACwB,QAAQ,CAAC;MAEzC,IAAII,gBAAgB,EAAE;QACpBC,OAAO,GAAG,KAAK;MACjB,CAAC,MAAM,IAAIH,eAAe,EAAE;QAC1B,IAAI,EAAEC,gBAAgB,IAAIF,eAAe,CAAC,EAAE;UAC1CI,OAAO,GAAG,KAAK;QACjB;MACF;MAEA,IAAIF,gBAAgB,EAAE;QACpBG,QAAQ,GAAG,KAAK;MAClB,CAAC,MAAM,IAAIL,eAAe,EAAE;QAC1B,IAAI,EAAEG,gBAAgB,IAAIF,eAAe,CAAC,EAAE;UAC1CI,QAAQ,GAAG,KAAK;QAClB;MACF;MAEA,IAAIN,QAAQ,KAAK,IAAI,CAAC,WAAWN,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC7C,IAAIK,QAAQ,IAAI,IAAI,CAAC,WAAWA,QAAQ,IAAI,IAAI,CAAC,SAAS;UACxD;UACAO,QAAQ,GAAGD,OAAO,GAAG,KAAK;QAC5B;MACF;MAEA,IAAIA,OAAO,IAAIC,QAAQ,EAAE;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACAD,OAAO,GAAGJ,eAAe;QACzBK,QAAQ,GAAGJ,eAAe;MAC5B;MAEA,IAAI,CAACG,OAAO,IAAI,CAACC,QAAQ,EAAE;QACzB;QACA,IAAIE,QAAQ,EAAE;UACZhB,KAAK,CAACuB,OAAO,GAAGhC,SAAS,CAACS,KAAK,CAACuB,OAAO,EAAErB,CAAC,CAACT,KAAK,EAAEH,UAAU,CAAC;QAC/D;QACA;MACF;MAEA,IAAIwB,QAAQ,EAAE;QACZ;QACA,KAAKC,CAAC,GAAGE,KAAK,CAACG,MAAM,GAAG,CAAC,EAAEL,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UACtCT,IAAI,GAAGW,KAAK,CAACF,CAAC,CAAC;UACf,IAAIE,KAAK,CAACF,CAAC,CAAC,CAACM,KAAK,GAAGhB,SAAS,EAAE;YAAE;UAAO;UACzC,IAAIC,IAAI,CAACwB,MAAM,KAAKd,QAAQ,IAAIC,KAAK,CAACF,CAAC,CAAC,CAACM,KAAK,KAAKhB,SAAS,EAAE;YAC5DC,IAAI,GAAGW,KAAK,CAACF,CAAC,CAAC;YAEf,IAAIC,QAAQ,EAAE;cACZE,SAAS,GAAGpB,KAAK,CAACiC,EAAE,CAACC,OAAO,CAACC,MAAM,CAAC,CAAC,CAAC;cACtCd,UAAU,GAAGrB,KAAK,CAACiC,EAAE,CAACC,OAAO,CAACC,MAAM,CAAC,CAAC,CAAC;YACzC,CAAC,MAAM;cACLf,SAAS,GAAGpB,KAAK,CAACiC,EAAE,CAACC,OAAO,CAACC,MAAM,CAAC,CAAC,CAAC;cACtCd,UAAU,GAAGrB,KAAK,CAACiC,EAAE,CAACC,OAAO,CAACC,MAAM,CAAC,CAAC,CAAC;YACzC;;YAEA;YACA;YACA;YACAjC,KAAK,CAACuB,OAAO,GAAGhC,SAAS,CAACS,KAAK,CAACuB,OAAO,EAAErB,CAAC,CAACT,KAAK,EAAE0B,UAAU,CAAC;YAC7DtB,MAAM,CAACS,IAAI,CAACN,KAAK,CAAC,CAACuB,OAAO,GAAGhC,SAAS,CACpCM,MAAM,CAACS,IAAI,CAACN,KAAK,CAAC,CAACuB,OAAO,EAAEjB,IAAI,CAACH,GAAG,EAAEe,SAAS,CAAC;YAElDf,GAAG,IAAIgB,UAAU,CAACC,MAAM,GAAG,CAAC;YAC5B,IAAId,IAAI,CAACN,KAAK,KAAKD,CAAC,EAAE;cAAEI,GAAG,IAAIe,SAAS,CAACE,MAAM,GAAG,CAAC;YAAE;YAErDnB,IAAI,GAAGD,KAAK,CAACuB,OAAO;YACpBnB,GAAG,GAAGH,IAAI,CAACmB,MAAM;YAEjBH,KAAK,CAACG,MAAM,GAAGL,CAAC;YAChB,SAASS,KAAK;UAChB;QACF;MACF;MAEA,IAAIX,OAAO,EAAE;QACXI,KAAK,CAACiB,IAAI,CAAC;UACTlC,KAAK,EAAED,CAAC;UACRI,GAAG,EAAED,CAAC,CAACT,KAAK;UACZqC,MAAM,EAAEd,QAAQ;UAChBK,KAAK,EAAEhB;QACT,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIS,QAAQ,IAAIE,QAAQ,EAAE;QAC/BhB,KAAK,CAACuB,OAAO,GAAGhC,SAAS,CAACS,KAAK,CAACuB,OAAO,EAAErB,CAAC,CAACT,KAAK,EAAEH,UAAU,CAAC;MAC/D;IACF;EACF;AACF;AAGA6C,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACvC,KAAK,EAAE;EAC3C;EACA,IAAIwC,MAAM;EAEV,IAAI,CAACxC,KAAK,CAACiC,EAAE,CAACC,OAAO,CAACO,WAAW,EAAE;IAAE;EAAQ;EAE7C,KAAKD,MAAM,GAAGxC,KAAK,CAACD,MAAM,CAACuB,MAAM,GAAG,CAAC,EAAEkB,MAAM,IAAI,CAAC,EAAEA,MAAM,EAAE,EAAE;IAE5D,IAAIxC,KAAK,CAACD,MAAM,CAACyC,MAAM,CAAC,CAAChB,IAAI,KAAK,QAAQ,IACtC,CAAClC,aAAa,CAACoD,IAAI,CAAC1C,KAAK,CAACD,MAAM,CAACyC,MAAM,CAAC,CAACf,OAAO,CAAC,EAAE;MACrD;IACF;IAEA3B,eAAe,CAACE,KAAK,CAACD,MAAM,CAACyC,MAAM,CAAC,CAACG,QAAQ,EAAE3C,KAAK,CAAC;EACvD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}