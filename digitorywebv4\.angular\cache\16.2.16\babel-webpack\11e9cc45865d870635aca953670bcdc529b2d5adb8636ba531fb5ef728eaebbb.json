{"ast": null, "code": "import Duration from \"../duration.js\";\nfunction dayDiff(earlier, later) {\n  const utcDayStart = dt => dt.toUTC(0, {\n      keepLocalTime: true\n    }).startOf(\"day\").valueOf(),\n    ms = utcDayStart(later) - utcDayStart(earlier);\n  return Math.floor(Duration.fromMillis(ms).as(\"days\"));\n}\nfunction highOrderDiffs(cursor, later, units) {\n  const differs = [[\"years\", (a, b) => b.year - a.year], [\"quarters\", (a, b) => b.quarter - a.quarter + (b.year - a.year) * 4], [\"months\", (a, b) => b.month - a.month + (b.year - a.year) * 12], [\"weeks\", (a, b) => {\n    const days = dayDiff(a, b);\n    return (days - days % 7) / 7;\n  }], [\"days\", dayDiff]];\n  const results = {};\n  const earlier = cursor;\n  let lowestOrder, highWater;\n\n  /* This loop tries to diff using larger units first.\n     If we overshoot, we backtrack and try the next smaller unit.\n     \"cursor\" starts out at the earlier timestamp and moves closer and closer to \"later\"\n     as we use smaller and smaller units.\n     highWater keeps track of where we would be if we added one more of the smallest unit,\n     this is used later to potentially convert any difference smaller than the smallest higher order unit\n     into a fraction of that smallest higher order unit\n  */\n  for (const [unit, differ] of differs) {\n    if (units.indexOf(unit) >= 0) {\n      lowestOrder = unit;\n      results[unit] = differ(cursor, later);\n      highWater = earlier.plus(results);\n      if (highWater > later) {\n        // we overshot the end point, backtrack cursor by 1\n        results[unit]--;\n        cursor = earlier.plus(results);\n\n        // if we are still overshooting now, we need to backtrack again\n        // this happens in certain situations when diffing times in different zones,\n        // because this calculation ignores time zones\n        if (cursor > later) {\n          // keep the \"overshot by 1\" around as highWater\n          highWater = cursor;\n          // backtrack cursor by 1\n          results[unit]--;\n          cursor = earlier.plus(results);\n        }\n      } else {\n        cursor = highWater;\n      }\n    }\n  }\n  return [cursor, results, highWater, lowestOrder];\n}\nexport default function (earlier, later, units, opts) {\n  let [cursor, results, highWater, lowestOrder] = highOrderDiffs(earlier, later, units);\n  const remainingMillis = later - cursor;\n  const lowerOrderUnits = units.filter(u => [\"hours\", \"minutes\", \"seconds\", \"milliseconds\"].indexOf(u) >= 0);\n  if (lowerOrderUnits.length === 0) {\n    if (highWater < later) {\n      highWater = cursor.plus({\n        [lowestOrder]: 1\n      });\n    }\n    if (highWater !== cursor) {\n      results[lowestOrder] = (results[lowestOrder] || 0) + remainingMillis / (highWater - cursor);\n    }\n  }\n  const duration = Duration.fromObject(results, opts);\n  if (lowerOrderUnits.length > 0) {\n    return Duration.fromMillis(remainingMillis, opts).shiftTo(...lowerOrderUnits).plus(duration);\n  } else {\n    return duration;\n  }\n}", "map": {"version": 3, "names": ["Duration", "dayDiff", "earlier", "later", "utcDayStart", "dt", "toUTC", "keepLocalTime", "startOf", "valueOf", "ms", "Math", "floor", "fromMillis", "as", "highOrderDiffs", "cursor", "units", "differs", "a", "b", "year", "quarter", "month", "days", "results", "lowestOrder", "highWater", "unit", "differ", "indexOf", "plus", "opts", "remaining<PERSON>ill<PERSON>", "lowerOrderUnits", "filter", "u", "length", "duration", "fromObject", "shiftTo"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/luxon/src/impl/diff.js"], "sourcesContent": ["import Duration from \"../duration.js\";\n\nfunction dayDiff(earlier, later) {\n  const utcDayStart = (dt) => dt.toUTC(0, { keepLocalTime: true }).startOf(\"day\").valueOf(),\n    ms = utcDayStart(later) - utcDayStart(earlier);\n  return Math.floor(Duration.fromMillis(ms).as(\"days\"));\n}\n\nfunction highOrderDiffs(cursor, later, units) {\n  const differs = [\n    [\"years\", (a, b) => b.year - a.year],\n    [\"quarters\", (a, b) => b.quarter - a.quarter + (b.year - a.year) * 4],\n    [\"months\", (a, b) => b.month - a.month + (b.year - a.year) * 12],\n    [\n      \"weeks\",\n      (a, b) => {\n        const days = dayDiff(a, b);\n        return (days - (days % 7)) / 7;\n      },\n    ],\n    [\"days\", dayDiff],\n  ];\n\n  const results = {};\n  const earlier = cursor;\n  let lowestOrder, highWater;\n\n  /* This loop tries to diff using larger units first.\n     If we overshoot, we backtrack and try the next smaller unit.\n     \"cursor\" starts out at the earlier timestamp and moves closer and closer to \"later\"\n     as we use smaller and smaller units.\n     highWater keeps track of where we would be if we added one more of the smallest unit,\n     this is used later to potentially convert any difference smaller than the smallest higher order unit\n     into a fraction of that smallest higher order unit\n  */\n  for (const [unit, differ] of differs) {\n    if (units.indexOf(unit) >= 0) {\n      lowestOrder = unit;\n\n      results[unit] = differ(cursor, later);\n      highWater = earlier.plus(results);\n\n      if (highWater > later) {\n        // we overshot the end point, backtrack cursor by 1\n        results[unit]--;\n        cursor = earlier.plus(results);\n\n        // if we are still overshooting now, we need to backtrack again\n        // this happens in certain situations when diffing times in different zones,\n        // because this calculation ignores time zones\n        if (cursor > later) {\n          // keep the \"overshot by 1\" around as highWater\n          highWater = cursor;\n          // backtrack cursor by 1\n          results[unit]--;\n          cursor = earlier.plus(results);\n        }\n      } else {\n        cursor = highWater;\n      }\n    }\n  }\n\n  return [cursor, results, highWater, lowestOrder];\n}\n\nexport default function (earlier, later, units, opts) {\n  let [cursor, results, highWater, lowestOrder] = highOrderDiffs(earlier, later, units);\n\n  const remainingMillis = later - cursor;\n\n  const lowerOrderUnits = units.filter(\n    (u) => [\"hours\", \"minutes\", \"seconds\", \"milliseconds\"].indexOf(u) >= 0\n  );\n\n  if (lowerOrderUnits.length === 0) {\n    if (highWater < later) {\n      highWater = cursor.plus({ [lowestOrder]: 1 });\n    }\n\n    if (highWater !== cursor) {\n      results[lowestOrder] = (results[lowestOrder] || 0) + remainingMillis / (highWater - cursor);\n    }\n  }\n\n  const duration = Duration.fromObject(results, opts);\n\n  if (lowerOrderUnits.length > 0) {\n    return Duration.fromMillis(remainingMillis, opts)\n      .shiftTo(...lowerOrderUnits)\n      .plus(duration);\n  } else {\n    return duration;\n  }\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AAErC,SAASC,OAAOA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAC/B,MAAMC,WAAW,GAAIC,EAAE,IAAKA,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC;IACvFC,EAAE,GAAGN,WAAW,CAACD,KAAK,CAAC,GAAGC,WAAW,CAACF,OAAO,CAAC;EAChD,OAAOS,IAAI,CAACC,KAAK,CAACZ,QAAQ,CAACa,UAAU,CAACH,EAAE,CAAC,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC;AACvD;AAEA,SAASC,cAAcA,CAACC,MAAM,EAAEb,KAAK,EAAEc,KAAK,EAAE;EAC5C,MAAMC,OAAO,GAAG,CACd,CAAC,OAAO,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,IAAI,GAAGF,CAAC,CAACE,IAAI,CAAC,EACpC,CAAC,UAAU,EAAE,CAACF,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACE,OAAO,GAAGH,CAAC,CAACG,OAAO,GAAG,CAACF,CAAC,CAACC,IAAI,GAAGF,CAAC,CAACE,IAAI,IAAI,CAAC,CAAC,EACrE,CAAC,QAAQ,EAAE,CAACF,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACG,KAAK,GAAGJ,CAAC,CAACI,KAAK,GAAG,CAACH,CAAC,CAACC,IAAI,GAAGF,CAAC,CAACE,IAAI,IAAI,EAAE,CAAC,EAChE,CACE,OAAO,EACP,CAACF,CAAC,EAAEC,CAAC,KAAK;IACR,MAAMI,IAAI,GAAGvB,OAAO,CAACkB,CAAC,EAAEC,CAAC,CAAC;IAC1B,OAAO,CAACI,IAAI,GAAIA,IAAI,GAAG,CAAE,IAAI,CAAC;EAChC,CAAC,CACF,EACD,CAAC,MAAM,EAAEvB,OAAO,CAAC,CAClB;EAED,MAAMwB,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMvB,OAAO,GAAGc,MAAM;EACtB,IAAIU,WAAW,EAAEC,SAAS;;EAE1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,KAAK,MAAM,CAACC,IAAI,EAAEC,MAAM,CAAC,IAAIX,OAAO,EAAE;IACpC,IAAID,KAAK,CAACa,OAAO,CAACF,IAAI,CAAC,IAAI,CAAC,EAAE;MAC5BF,WAAW,GAAGE,IAAI;MAElBH,OAAO,CAACG,IAAI,CAAC,GAAGC,MAAM,CAACb,MAAM,EAAEb,KAAK,CAAC;MACrCwB,SAAS,GAAGzB,OAAO,CAAC6B,IAAI,CAACN,OAAO,CAAC;MAEjC,IAAIE,SAAS,GAAGxB,KAAK,EAAE;QACrB;QACAsB,OAAO,CAACG,IAAI,CAAC,EAAE;QACfZ,MAAM,GAAGd,OAAO,CAAC6B,IAAI,CAACN,OAAO,CAAC;;QAE9B;QACA;QACA;QACA,IAAIT,MAAM,GAAGb,KAAK,EAAE;UAClB;UACAwB,SAAS,GAAGX,MAAM;UAClB;UACAS,OAAO,CAACG,IAAI,CAAC,EAAE;UACfZ,MAAM,GAAGd,OAAO,CAAC6B,IAAI,CAACN,OAAO,CAAC;QAChC;MACF,CAAC,MAAM;QACLT,MAAM,GAAGW,SAAS;MACpB;IACF;EACF;EAEA,OAAO,CAACX,MAAM,EAAES,OAAO,EAAEE,SAAS,EAAED,WAAW,CAAC;AAClD;AAEA,eAAe,UAAUxB,OAAO,EAAEC,KAAK,EAAEc,KAAK,EAAEe,IAAI,EAAE;EACpD,IAAI,CAAChB,MAAM,EAAES,OAAO,EAAEE,SAAS,EAAED,WAAW,CAAC,GAAGX,cAAc,CAACb,OAAO,EAAEC,KAAK,EAAEc,KAAK,CAAC;EAErF,MAAMgB,eAAe,GAAG9B,KAAK,GAAGa,MAAM;EAEtC,MAAMkB,eAAe,GAAGjB,KAAK,CAACkB,MAAM,CACjCC,CAAC,IAAK,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC,CAACN,OAAO,CAACM,CAAC,CAAC,IAAI,CACvE,CAAC;EAED,IAAIF,eAAe,CAACG,MAAM,KAAK,CAAC,EAAE;IAChC,IAAIV,SAAS,GAAGxB,KAAK,EAAE;MACrBwB,SAAS,GAAGX,MAAM,CAACe,IAAI,CAAC;QAAE,CAACL,WAAW,GAAG;MAAE,CAAC,CAAC;IAC/C;IAEA,IAAIC,SAAS,KAAKX,MAAM,EAAE;MACxBS,OAAO,CAACC,WAAW,CAAC,GAAG,CAACD,OAAO,CAACC,WAAW,CAAC,IAAI,CAAC,IAAIO,eAAe,IAAIN,SAAS,GAAGX,MAAM,CAAC;IAC7F;EACF;EAEA,MAAMsB,QAAQ,GAAGtC,QAAQ,CAACuC,UAAU,CAACd,OAAO,EAAEO,IAAI,CAAC;EAEnD,IAAIE,eAAe,CAACG,MAAM,GAAG,CAAC,EAAE;IAC9B,OAAOrC,QAAQ,CAACa,UAAU,CAACoB,eAAe,EAAED,IAAI,CAAC,CAC9CQ,OAAO,CAAC,GAAGN,eAAe,CAAC,CAC3BH,IAAI,CAACO,QAAQ,CAAC;EACnB,CAAC,MAAM;IACL,OAAOA,QAAQ;EACjB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}