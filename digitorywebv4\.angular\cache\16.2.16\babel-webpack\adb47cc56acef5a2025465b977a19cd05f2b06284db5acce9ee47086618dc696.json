{"ast": null, "code": "import isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/**\n * The base implementation of `_.toNumber` which doesn't ensure correct\n * conversions of binary, hexadecimal, or octal string values.\n *\n * @private\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n */\nfunction baseToNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  return +value;\n}\nexport default baseToNumber;", "map": {"version": 3, "names": ["isSymbol", "NAN", "baseToNumber", "value"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/lodash-es/_baseToNumber.js"], "sourcesContent": ["import isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/**\n * The base implementation of `_.toNumber` which doesn't ensure correct\n * conversions of binary, hexadecimal, or octal string values.\n *\n * @private\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n */\nfunction baseToNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  return +value;\n}\n\nexport default baseToNumber;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,GAAG,GAAG,CAAC,GAAG,CAAC;;AAEf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAI,OAAOA,KAAK,IAAI,QAAQ,EAAE;IAC5B,OAAOA,KAAK;EACd;EACA,IAAIH,QAAQ,CAACG,KAAK,CAAC,EAAE;IACnB,OAAOF,GAAG;EACZ;EACA,OAAO,CAACE,KAAK;AACf;AAEA,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}