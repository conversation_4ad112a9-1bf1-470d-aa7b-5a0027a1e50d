{"ast": null, "code": "/** internal\n * class ParserBlock\n *\n * Block-level tokenizer.\n **/\n'use strict';\n\nvar Ruler = require('./ruler');\nvar _rules = [\n// First 2 params - rule name & source. Secondary array - list of rules,\n// which can be terminated by this one.\n['table', require('./rules_block/table'), ['paragraph', 'reference']], ['code', require('./rules_block/code')], ['fence', require('./rules_block/fence'), ['paragraph', 'reference', 'blockquote', 'list']], ['blockquote', require('./rules_block/blockquote'), ['paragraph', 'reference', 'blockquote', 'list']], ['hr', require('./rules_block/hr'), ['paragraph', 'reference', 'blockquote', 'list']], ['list', require('./rules_block/list'), ['paragraph', 'reference', 'blockquote']], ['reference', require('./rules_block/reference')], ['html_block', require('./rules_block/html_block'), ['paragraph', 'reference', 'blockquote']], ['heading', require('./rules_block/heading'), ['paragraph', 'reference', 'blockquote']], ['lheading', require('./rules_block/lheading')], ['paragraph', require('./rules_block/paragraph')]];\n\n/**\n * new ParserBlock()\n **/\nfunction ParserBlock() {\n  /**\n   * ParserBlock#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of block rules.\n   **/\n  this.ruler = new Ruler();\n  for (var i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1], {\n      alt: (_rules[i][2] || []).slice()\n    });\n  }\n}\n\n// Generate tokens for input range\n//\nParserBlock.prototype.tokenize = function (state, startLine, endLine) {\n  var ok,\n    i,\n    rules = this.ruler.getRules(''),\n    len = rules.length,\n    line = startLine,\n    hasEmptyLines = false,\n    maxNesting = state.md.options.maxNesting;\n  while (line < endLine) {\n    state.line = line = state.skipEmptyLines(line);\n    if (line >= endLine) {\n      break;\n    }\n\n    // Termination condition for nested calls.\n    // Nested calls currently used for blockquotes & lists\n    if (state.sCount[line] < state.blkIndent) {\n      break;\n    }\n\n    // If nesting level exceeded - skip tail to the end. That's not ordinary\n    // situation and we should not care about content.\n    if (state.level >= maxNesting) {\n      state.line = endLine;\n      break;\n    }\n\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.line`\n    // - update `state.tokens`\n    // - return true\n\n    for (i = 0; i < len; i++) {\n      ok = rules[i](state, line, endLine, false);\n      if (ok) {\n        break;\n      }\n    }\n\n    // set state.tight if we had an empty line before current tag\n    // i.e. latest empty line should not count\n    state.tight = !hasEmptyLines;\n\n    // paragraph might \"eat\" one newline after it in nested lists\n    if (state.isEmpty(state.line - 1)) {\n      hasEmptyLines = true;\n    }\n    line = state.line;\n    if (line < endLine && state.isEmpty(line)) {\n      hasEmptyLines = true;\n      line++;\n      state.line = line;\n    }\n  }\n};\n\n/**\n * ParserBlock.parse(str, md, env, outTokens)\n *\n * Process input string and push block tokens into `outTokens`\n **/\nParserBlock.prototype.parse = function (src, md, env, outTokens) {\n  var state;\n  if (!src) {\n    return;\n  }\n  state = new this.State(src, md, env, outTokens);\n  this.tokenize(state, state.line, state.lineMax);\n};\nParserBlock.prototype.State = require('./rules_block/state_block');\nmodule.exports = ParserBlock;", "map": {"version": 3, "names": ["Ruler", "require", "_rules", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ruler", "i", "length", "push", "alt", "slice", "prototype", "tokenize", "state", "startLine", "endLine", "ok", "rules", "getRules", "len", "line", "hasEmptyLines", "maxNesting", "md", "options", "skipEmptyLines", "sCount", "blkIndent", "level", "tight", "isEmpty", "parse", "src", "env", "outTokens", "State", "lineMax", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/parser_block.js"], "sourcesContent": ["/** internal\n * class ParserBlock\n *\n * Block-level tokenizer.\n **/\n'use strict';\n\n\nvar Ruler           = require('./ruler');\n\n\nvar _rules = [\n  // First 2 params - rule name & source. Secondary array - list of rules,\n  // which can be terminated by this one.\n  [ 'table',      require('./rules_block/table'),      [ 'paragraph', 'reference' ] ],\n  [ 'code',       require('./rules_block/code') ],\n  [ 'fence',      require('./rules_block/fence'),      [ 'paragraph', 'reference', 'blockquote', 'list' ] ],\n  [ 'blockquote', require('./rules_block/blockquote'), [ 'paragraph', 'reference', 'blockquote', 'list' ] ],\n  [ 'hr',         require('./rules_block/hr'),         [ 'paragraph', 'reference', 'blockquote', 'list' ] ],\n  [ 'list',       require('./rules_block/list'),       [ 'paragraph', 'reference', 'blockquote' ] ],\n  [ 'reference',  require('./rules_block/reference') ],\n  [ 'html_block', require('./rules_block/html_block'), [ 'paragraph', 'reference', 'blockquote' ] ],\n  [ 'heading',    require('./rules_block/heading'),    [ 'paragraph', 'reference', 'blockquote' ] ],\n  [ 'lheading',   require('./rules_block/lheading') ],\n  [ 'paragraph',  require('./rules_block/paragraph') ]\n];\n\n\n/**\n * new ParserBlock()\n **/\nfunction ParserBlock() {\n  /**\n   * ParserBlock#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of block rules.\n   **/\n  this.ruler = new Ruler();\n\n  for (var i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1], { alt: (_rules[i][2] || []).slice() });\n  }\n}\n\n\n// Generate tokens for input range\n//\nParserBlock.prototype.tokenize = function (state, startLine, endLine) {\n  var ok, i,\n      rules = this.ruler.getRules(''),\n      len = rules.length,\n      line = startLine,\n      hasEmptyLines = false,\n      maxNesting = state.md.options.maxNesting;\n\n  while (line < endLine) {\n    state.line = line = state.skipEmptyLines(line);\n    if (line >= endLine) { break; }\n\n    // Termination condition for nested calls.\n    // Nested calls currently used for blockquotes & lists\n    if (state.sCount[line] < state.blkIndent) { break; }\n\n    // If nesting level exceeded - skip tail to the end. That's not ordinary\n    // situation and we should not care about content.\n    if (state.level >= maxNesting) {\n      state.line = endLine;\n      break;\n    }\n\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.line`\n    // - update `state.tokens`\n    // - return true\n\n    for (i = 0; i < len; i++) {\n      ok = rules[i](state, line, endLine, false);\n      if (ok) { break; }\n    }\n\n    // set state.tight if we had an empty line before current tag\n    // i.e. latest empty line should not count\n    state.tight = !hasEmptyLines;\n\n    // paragraph might \"eat\" one newline after it in nested lists\n    if (state.isEmpty(state.line - 1)) {\n      hasEmptyLines = true;\n    }\n\n    line = state.line;\n\n    if (line < endLine && state.isEmpty(line)) {\n      hasEmptyLines = true;\n      line++;\n      state.line = line;\n    }\n  }\n};\n\n\n/**\n * ParserBlock.parse(str, md, env, outTokens)\n *\n * Process input string and push block tokens into `outTokens`\n **/\nParserBlock.prototype.parse = function (src, md, env, outTokens) {\n  var state;\n\n  if (!src) { return; }\n\n  state = new this.State(src, md, env, outTokens);\n\n  this.tokenize(state, state.line, state.lineMax);\n};\n\n\nParserBlock.prototype.State = require('./rules_block/state_block');\n\n\nmodule.exports = ParserBlock;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAGZ,IAAIA,KAAK,GAAaC,OAAO,CAAC,SAAS,CAAC;AAGxC,IAAIC,MAAM,GAAG;AACX;AACA;AACA,CAAE,OAAO,EAAOD,OAAO,CAAC,qBAAqB,CAAC,EAAO,CAAE,WAAW,EAAE,WAAW,CAAE,CAAE,EACnF,CAAE,MAAM,EAAQA,OAAO,CAAC,oBAAoB,CAAC,CAAE,EAC/C,CAAE,OAAO,EAAOA,OAAO,CAAC,qBAAqB,CAAC,EAAO,CAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,CAAE,CAAE,EACzG,CAAE,YAAY,EAAEA,OAAO,CAAC,0BAA0B,CAAC,EAAE,CAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,CAAE,CAAE,EACzG,CAAE,IAAI,EAAUA,OAAO,CAAC,kBAAkB,CAAC,EAAU,CAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,CAAE,CAAE,EACzG,CAAE,MAAM,EAAQA,OAAO,CAAC,oBAAoB,CAAC,EAAQ,CAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAE,CAAE,EACjG,CAAE,WAAW,EAAGA,OAAO,CAAC,yBAAyB,CAAC,CAAE,EACpD,CAAE,YAAY,EAAEA,OAAO,CAAC,0BAA0B,CAAC,EAAE,CAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAE,CAAE,EACjG,CAAE,SAAS,EAAKA,OAAO,CAAC,uBAAuB,CAAC,EAAK,CAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAE,CAAE,EACjG,CAAE,UAAU,EAAIA,OAAO,CAAC,wBAAwB,CAAC,CAAE,EACnD,CAAE,WAAW,EAAGA,OAAO,CAAC,yBAAyB,CAAC,CAAE,CACrD;;AAGD;AACA;AACA;AACA,SAASE,WAAWA,CAAA,EAAG;EACrB;AACF;AACA;AACA;AACA;EACE,IAAI,CAACC,KAAK,GAAG,IAAIJ,KAAK,CAAC,CAAC;EAExB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAI,CAACD,KAAK,CAACG,IAAI,CAACL,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEH,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAAEG,GAAG,EAAE,CAACN,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEI,KAAK,CAAC;IAAE,CAAC,CAAC;EACpF;AACF;;AAGA;AACA;AACAN,WAAW,CAACO,SAAS,CAACC,QAAQ,GAAG,UAAUC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACpE,IAAIC,EAAE;IAAEV,CAAC;IACLW,KAAK,GAAG,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAAC,EAAE,CAAC;IAC/BC,GAAG,GAAGF,KAAK,CAACV,MAAM;IAClBa,IAAI,GAAGN,SAAS;IAChBO,aAAa,GAAG,KAAK;IACrBC,UAAU,GAAGT,KAAK,CAACU,EAAE,CAACC,OAAO,CAACF,UAAU;EAE5C,OAAOF,IAAI,GAAGL,OAAO,EAAE;IACrBF,KAAK,CAACO,IAAI,GAAGA,IAAI,GAAGP,KAAK,CAACY,cAAc,CAACL,IAAI,CAAC;IAC9C,IAAIA,IAAI,IAAIL,OAAO,EAAE;MAAE;IAAO;;IAE9B;IACA;IACA,IAAIF,KAAK,CAACa,MAAM,CAACN,IAAI,CAAC,GAAGP,KAAK,CAACc,SAAS,EAAE;MAAE;IAAO;;IAEnD;IACA;IACA,IAAId,KAAK,CAACe,KAAK,IAAIN,UAAU,EAAE;MAC7BT,KAAK,CAACO,IAAI,GAAGL,OAAO;MACpB;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA;;IAEA,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,GAAG,EAAEb,CAAC,EAAE,EAAE;MACxBU,EAAE,GAAGC,KAAK,CAACX,CAAC,CAAC,CAACO,KAAK,EAAEO,IAAI,EAAEL,OAAO,EAAE,KAAK,CAAC;MAC1C,IAAIC,EAAE,EAAE;QAAE;MAAO;IACnB;;IAEA;IACA;IACAH,KAAK,CAACgB,KAAK,GAAG,CAACR,aAAa;;IAE5B;IACA,IAAIR,KAAK,CAACiB,OAAO,CAACjB,KAAK,CAACO,IAAI,GAAG,CAAC,CAAC,EAAE;MACjCC,aAAa,GAAG,IAAI;IACtB;IAEAD,IAAI,GAAGP,KAAK,CAACO,IAAI;IAEjB,IAAIA,IAAI,GAAGL,OAAO,IAAIF,KAAK,CAACiB,OAAO,CAACV,IAAI,CAAC,EAAE;MACzCC,aAAa,GAAG,IAAI;MACpBD,IAAI,EAAE;MACNP,KAAK,CAACO,IAAI,GAAGA,IAAI;IACnB;EACF;AACF,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACAhB,WAAW,CAACO,SAAS,CAACoB,KAAK,GAAG,UAAUC,GAAG,EAAET,EAAE,EAAEU,GAAG,EAAEC,SAAS,EAAE;EAC/D,IAAIrB,KAAK;EAET,IAAI,CAACmB,GAAG,EAAE;IAAE;EAAQ;EAEpBnB,KAAK,GAAG,IAAI,IAAI,CAACsB,KAAK,CAACH,GAAG,EAAET,EAAE,EAAEU,GAAG,EAAEC,SAAS,CAAC;EAE/C,IAAI,CAACtB,QAAQ,CAACC,KAAK,EAAEA,KAAK,CAACO,IAAI,EAAEP,KAAK,CAACuB,OAAO,CAAC;AACjD,CAAC;AAGDhC,WAAW,CAACO,SAAS,CAACwB,KAAK,GAAGjC,OAAO,CAAC,2BAA2B,CAAC;AAGlEmC,MAAM,CAACC,OAAO,GAAGlC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}