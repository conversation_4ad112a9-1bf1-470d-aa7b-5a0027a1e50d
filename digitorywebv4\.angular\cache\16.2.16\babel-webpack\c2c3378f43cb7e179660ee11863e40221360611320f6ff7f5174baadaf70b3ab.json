{"ast": null, "code": "import root from './_root.js';\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\nexport default coreJsData;", "map": {"version": 3, "names": ["root", "coreJsData"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/lodash-es/_coreJsData.js"], "sourcesContent": ["import root from './_root.js';\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\nexport default coreJsData;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,UAAU,GAAGD,IAAI,CAAC,oBAAoB,CAAC;AAE3C,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}