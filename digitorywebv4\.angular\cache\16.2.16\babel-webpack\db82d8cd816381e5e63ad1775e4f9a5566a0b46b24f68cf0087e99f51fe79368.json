{"ast": null, "code": "// Clean up tokens after emphasis and strikethrough postprocessing:\n// merge adjacent text nodes into one and re-calculate all token levels\n//\n// This is necessary because initially emphasis delimiter markers (*, _, ~)\n// are treated as their own separate text tokens. Then emphasis rule either\n// leaves them as text (needed to merge with adjacent text) or turns them\n// into opening/closing tags (which messes up levels inside).\n//\n'use strict';\n\nmodule.exports = function fragments_join(state) {\n  var curr,\n    last,\n    level = 0,\n    tokens = state.tokens,\n    max = state.tokens.length;\n  for (curr = last = 0; curr < max; curr++) {\n    // re-calculate levels after emphasis/strikethrough turns some text nodes\n    // into opening/closing tags\n    if (tokens[curr].nesting < 0) level--; // closing tag\n    tokens[curr].level = level;\n    if (tokens[curr].nesting > 0) level++; // opening tag\n\n    if (tokens[curr].type === 'text' && curr + 1 < max && tokens[curr + 1].type === 'text') {\n      // collapse two adjacent text nodes\n      tokens[curr + 1].content = tokens[curr].content + tokens[curr + 1].content;\n    } else {\n      if (curr !== last) {\n        tokens[last] = tokens[curr];\n      }\n      last++;\n    }\n  }\n  if (curr !== last) {\n    tokens.length = last;\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "fragments_join", "state", "curr", "last", "level", "tokens", "max", "length", "nesting", "type", "content"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/rules_inline/fragments_join.js"], "sourcesContent": ["// Clean up tokens after emphasis and strikethrough postprocessing:\n// merge adjacent text nodes into one and re-calculate all token levels\n//\n// This is necessary because initially emphasis delimiter markers (*, _, ~)\n// are treated as their own separate text tokens. Then emphasis rule either\n// leaves them as text (needed to merge with adjacent text) or turns them\n// into opening/closing tags (which messes up levels inside).\n//\n'use strict';\n\n\nmodule.exports = function fragments_join(state) {\n  var curr, last,\n      level = 0,\n      tokens = state.tokens,\n      max = state.tokens.length;\n\n  for (curr = last = 0; curr < max; curr++) {\n    // re-calculate levels after emphasis/strikethrough turns some text nodes\n    // into opening/closing tags\n    if (tokens[curr].nesting < 0) level--; // closing tag\n    tokens[curr].level = level;\n    if (tokens[curr].nesting > 0) level++; // opening tag\n\n    if (tokens[curr].type === 'text' &&\n        curr + 1 < max &&\n        tokens[curr + 1].type === 'text') {\n\n      // collapse two adjacent text nodes\n      tokens[curr + 1].content = tokens[curr].content + tokens[curr + 1].content;\n    } else {\n      if (curr !== last) { tokens[last] = tokens[curr]; }\n\n      last++;\n    }\n  }\n\n  if (curr !== last) {\n    tokens.length = last;\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC9C,IAAIC,IAAI;IAAEC,IAAI;IACVC,KAAK,GAAG,CAAC;IACTC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,GAAG,GAAGL,KAAK,CAACI,MAAM,CAACE,MAAM;EAE7B,KAAKL,IAAI,GAAGC,IAAI,GAAG,CAAC,EAAED,IAAI,GAAGI,GAAG,EAAEJ,IAAI,EAAE,EAAE;IACxC;IACA;IACA,IAAIG,MAAM,CAACH,IAAI,CAAC,CAACM,OAAO,GAAG,CAAC,EAAEJ,KAAK,EAAE,CAAC,CAAC;IACvCC,MAAM,CAACH,IAAI,CAAC,CAACE,KAAK,GAAGA,KAAK;IAC1B,IAAIC,MAAM,CAACH,IAAI,CAAC,CAACM,OAAO,GAAG,CAAC,EAAEJ,KAAK,EAAE,CAAC,CAAC;;IAEvC,IAAIC,MAAM,CAACH,IAAI,CAAC,CAACO,IAAI,KAAK,MAAM,IAC5BP,IAAI,GAAG,CAAC,GAAGI,GAAG,IACdD,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,CAACO,IAAI,KAAK,MAAM,EAAE;MAEpC;MACAJ,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,CAACQ,OAAO,GAAGL,MAAM,CAACH,IAAI,CAAC,CAACQ,OAAO,GAAGL,MAAM,CAACH,IAAI,GAAG,CAAC,CAAC,CAACQ,OAAO;IAC5E,CAAC,MAAM;MACL,IAAIR,IAAI,KAAKC,IAAI,EAAE;QAAEE,MAAM,CAACF,IAAI,CAAC,GAAGE,MAAM,CAACH,IAAI,CAAC;MAAE;MAElDC,IAAI,EAAE;IACR;EACF;EAEA,IAAID,IAAI,KAAKC,IAAI,EAAE;IACjBE,MAAM,CAACE,MAAM,GAAGJ,IAAI;EACtB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}