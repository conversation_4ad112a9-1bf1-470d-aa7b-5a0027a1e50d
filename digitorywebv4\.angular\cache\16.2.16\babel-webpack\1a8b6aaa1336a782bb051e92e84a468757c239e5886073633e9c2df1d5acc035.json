{"ast": null, "code": "// Regexps to match html elements\n\n'use strict';\n\nvar attr_name = '[a-zA-Z_:][a-zA-Z0-9:._-]*';\nvar unquoted = '[^\"\\'=<>`\\\\x00-\\\\x20]+';\nvar single_quoted = \"'[^']*'\";\nvar double_quoted = '\"[^\"]*\"';\nvar attr_value = '(?:' + unquoted + '|' + single_quoted + '|' + double_quoted + ')';\nvar attribute = '(?:\\\\s+' + attr_name + '(?:\\\\s*=\\\\s*' + attr_value + ')?)';\nvar open_tag = '<[A-Za-z][A-Za-z0-9\\\\-]*' + attribute + '*\\\\s*\\\\/?>';\nvar close_tag = '<\\\\/[A-Za-z][A-Za-z0-9\\\\-]*\\\\s*>';\nvar comment = '<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->';\nvar processing = '<[?][\\\\s\\\\S]*?[?]>';\nvar declaration = '<![A-Z]+\\\\s+[^>]*>';\nvar cdata = '<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>';\nvar HTML_TAG_RE = new RegExp('^(?:' + open_tag + '|' + close_tag + '|' + comment + '|' + processing + '|' + declaration + '|' + cdata + ')');\nvar HTML_OPEN_CLOSE_TAG_RE = new RegExp('^(?:' + open_tag + '|' + close_tag + ')');\nmodule.exports.HTML_TAG_RE = HTML_TAG_RE;\nmodule.exports.HTML_OPEN_CLOSE_TAG_RE = HTML_OPEN_CLOSE_TAG_RE;", "map": {"version": 3, "names": ["attr_name", "unquoted", "single_quoted", "double_quoted", "attr_value", "attribute", "open_tag", "close_tag", "comment", "processing", "declaration", "cdata", "HTML_TAG_RE", "RegExp", "HTML_OPEN_CLOSE_TAG_RE", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/markdown-it/lib/common/html_re.js"], "sourcesContent": ["// Regexps to match html elements\n\n'use strict';\n\nvar attr_name     = '[a-zA-Z_:][a-zA-Z0-9:._-]*';\n\nvar unquoted      = '[^\"\\'=<>`\\\\x00-\\\\x20]+';\nvar single_quoted = \"'[^']*'\";\nvar double_quoted = '\"[^\"]*\"';\n\nvar attr_value  = '(?:' + unquoted + '|' + single_quoted + '|' + double_quoted + ')';\n\nvar attribute   = '(?:\\\\s+' + attr_name + '(?:\\\\s*=\\\\s*' + attr_value + ')?)';\n\nvar open_tag    = '<[A-Za-z][A-Za-z0-9\\\\-]*' + attribute + '*\\\\s*\\\\/?>';\n\nvar close_tag   = '<\\\\/[A-Za-z][A-Za-z0-9\\\\-]*\\\\s*>';\nvar comment     = '<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->';\nvar processing  = '<[?][\\\\s\\\\S]*?[?]>';\nvar declaration = '<![A-Z]+\\\\s+[^>]*>';\nvar cdata       = '<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>';\n\nvar HTML_TAG_RE = new RegExp('^(?:' + open_tag + '|' + close_tag + '|' + comment +\n                        '|' + processing + '|' + declaration + '|' + cdata + ')');\nvar HTML_OPEN_CLOSE_TAG_RE = new RegExp('^(?:' + open_tag + '|' + close_tag + ')');\n\nmodule.exports.HTML_TAG_RE = HTML_TAG_RE;\nmodule.exports.HTML_OPEN_CLOSE_TAG_RE = HTML_OPEN_CLOSE_TAG_RE;\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,SAAS,GAAO,4BAA4B;AAEhD,IAAIC,QAAQ,GAAQ,wBAAwB;AAC5C,IAAIC,aAAa,GAAG,SAAS;AAC7B,IAAIC,aAAa,GAAG,SAAS;AAE7B,IAAIC,UAAU,GAAI,KAAK,GAAGH,QAAQ,GAAG,GAAG,GAAGC,aAAa,GAAG,GAAG,GAAGC,aAAa,GAAG,GAAG;AAEpF,IAAIE,SAAS,GAAK,SAAS,GAAGL,SAAS,GAAG,cAAc,GAAGI,UAAU,GAAG,KAAK;AAE7E,IAAIE,QAAQ,GAAM,0BAA0B,GAAGD,SAAS,GAAG,YAAY;AAEvE,IAAIE,SAAS,GAAK,kCAAkC;AACpD,IAAIC,OAAO,GAAO,uCAAuC;AACzD,IAAIC,UAAU,GAAI,oBAAoB;AACtC,IAAIC,WAAW,GAAG,oBAAoB;AACtC,IAAIC,KAAK,GAAS,gCAAgC;AAElD,IAAIC,WAAW,GAAG,IAAIC,MAAM,CAAC,MAAM,GAAGP,QAAQ,GAAG,GAAG,GAAGC,SAAS,GAAG,GAAG,GAAGC,OAAO,GACxD,GAAG,GAAGC,UAAU,GAAG,GAAG,GAAGC,WAAW,GAAG,GAAG,GAAGC,KAAK,GAAG,GAAG,CAAC;AACjF,IAAIG,sBAAsB,GAAG,IAAID,MAAM,CAAC,MAAM,GAAGP,QAAQ,GAAG,GAAG,GAAGC,SAAS,GAAG,GAAG,CAAC;AAElFQ,MAAM,CAACC,OAAO,CAACJ,WAAW,GAAGA,WAAW;AACxCG,MAAM,CAACC,OAAO,CAACF,sBAAsB,GAAGA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}