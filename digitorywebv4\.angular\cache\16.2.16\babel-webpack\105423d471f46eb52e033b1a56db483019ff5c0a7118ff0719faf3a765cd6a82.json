{"ast": null, "code": "import isLaziable from './_isLaziable.js';\nimport setData from './_setData.js';\nimport setWrapToString from './_setWrapToString.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n  WRAP_BIND_KEY_FLAG = 2,\n  WRAP_CURRY_BOUND_FLAG = 4,\n  WRAP_CURRY_FLAG = 8,\n  WRAP_PARTIAL_FLAG = 32,\n  WRAP_PARTIAL_RIGHT_FLAG = 64;\n\n/**\n * Creates a function that wraps `func` to continue currying.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {Function} wrapFunc The function to create the `func` wrapper.\n * @param {*} placeholder The placeholder value.\n * @param {*} [thisArg] The `this` binding of `func`.\n * @param {Array} [partials] The arguments to prepend to those provided to\n *  the new function.\n * @param {Array} [holders] The `partials` placeholder indexes.\n * @param {Array} [argPos] The argument positions of the new function.\n * @param {number} [ary] The arity cap of `func`.\n * @param {number} [arity] The arity of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createRecurry(func, bitmask, wrapFunc, placeholder, thisArg, partials, holders, argPos, ary, arity) {\n  var isCurry = bitmask & WRAP_CURRY_FLAG,\n    newHolders = isCurry ? holders : undefined,\n    newHoldersRight = isCurry ? undefined : holders,\n    newPartials = isCurry ? partials : undefined,\n    newPartialsRight = isCurry ? undefined : partials;\n  bitmask |= isCurry ? WRAP_PARTIAL_FLAG : WRAP_PARTIAL_RIGHT_FLAG;\n  bitmask &= ~(isCurry ? WRAP_PARTIAL_RIGHT_FLAG : WRAP_PARTIAL_FLAG);\n  if (!(bitmask & WRAP_CURRY_BOUND_FLAG)) {\n    bitmask &= ~(WRAP_BIND_FLAG | WRAP_BIND_KEY_FLAG);\n  }\n  var newData = [func, bitmask, thisArg, newPartials, newHolders, newPartialsRight, newHoldersRight, argPos, ary, arity];\n  var result = wrapFunc.apply(undefined, newData);\n  if (isLaziable(func)) {\n    setData(result, newData);\n  }\n  result.placeholder = placeholder;\n  return setWrapToString(result, func, bitmask);\n}\nexport default createRecurry;", "map": {"version": 3, "names": ["isLaziable", "setData", "setWrapToString", "WRAP_BIND_FLAG", "WRAP_BIND_KEY_FLAG", "WRAP_CURRY_BOUND_FLAG", "WRAP_CURRY_FLAG", "WRAP_PARTIAL_FLAG", "WRAP_PARTIAL_RIGHT_FLAG", "createRecurry", "func", "bitmask", "wrapFunc", "placeholder", "thisArg", "partials", "holders", "argPos", "ary", "arity", "<PERSON><PERSON><PERSON><PERSON>", "newHolders", "undefined", "newHoldersRight", "newPartials", "newPartialsRight", "newData", "result", "apply"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/lodash-es/_createRecurry.js"], "sourcesContent": ["import isLaziable from './_isLaziable.js';\nimport setData from './_setData.js';\nimport setWrapToString from './_setWrapToString.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n    WRAP_BIND_KEY_FLAG = 2,\n    WRAP_CURRY_BOUND_FLAG = 4,\n    WRAP_CURRY_FLAG = 8,\n    WRAP_PARTIAL_FLAG = 32,\n    WRAP_PARTIAL_RIGHT_FLAG = 64;\n\n/**\n * Creates a function that wraps `func` to continue currying.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {Function} wrapFunc The function to create the `func` wrapper.\n * @param {*} placeholder The placeholder value.\n * @param {*} [thisArg] The `this` binding of `func`.\n * @param {Array} [partials] The arguments to prepend to those provided to\n *  the new function.\n * @param {Array} [holders] The `partials` placeholder indexes.\n * @param {Array} [argPos] The argument positions of the new function.\n * @param {number} [ary] The arity cap of `func`.\n * @param {number} [arity] The arity of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createRecurry(func, bitmask, wrapFunc, placeholder, thisArg, partials, holders, argPos, ary, arity) {\n  var isCurry = bitmask & WRAP_CURRY_FLAG,\n      newHolders = isCurry ? holders : undefined,\n      newHoldersRight = isCurry ? undefined : holders,\n      newPartials = isCurry ? partials : undefined,\n      newPartialsRight = isCurry ? undefined : partials;\n\n  bitmask |= (isCurry ? WRAP_PARTIAL_FLAG : WRAP_PARTIAL_RIGHT_FLAG);\n  bitmask &= ~(isCurry ? WRAP_PARTIAL_RIGHT_FLAG : WRAP_PARTIAL_FLAG);\n\n  if (!(bitmask & WRAP_CURRY_BOUND_FLAG)) {\n    bitmask &= ~(WRAP_BIND_FLAG | WRAP_BIND_KEY_FLAG);\n  }\n  var newData = [\n    func, bitmask, thisArg, newPartials, newHolders, newPartialsRight,\n    newHoldersRight, argPos, ary, arity\n  ];\n\n  var result = wrapFunc.apply(undefined, newData);\n  if (isLaziable(func)) {\n    setData(result, newData);\n  }\n  result.placeholder = placeholder;\n  return setWrapToString(result, func, bitmask);\n}\n\nexport default createRecurry;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,eAAe,MAAM,uBAAuB;;AAEnD;AACA,IAAIC,cAAc,GAAG,CAAC;EAClBC,kBAAkB,GAAG,CAAC;EACtBC,qBAAqB,GAAG,CAAC;EACzBC,eAAe,GAAG,CAAC;EACnBC,iBAAiB,GAAG,EAAE;EACtBC,uBAAuB,GAAG,EAAE;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAC3G,IAAIC,OAAO,GAAGT,OAAO,GAAGL,eAAe;IACnCe,UAAU,GAAGD,OAAO,GAAGJ,OAAO,GAAGM,SAAS;IAC1CC,eAAe,GAAGH,OAAO,GAAGE,SAAS,GAAGN,OAAO;IAC/CQ,WAAW,GAAGJ,OAAO,GAAGL,QAAQ,GAAGO,SAAS;IAC5CG,gBAAgB,GAAGL,OAAO,GAAGE,SAAS,GAAGP,QAAQ;EAErDJ,OAAO,IAAKS,OAAO,GAAGb,iBAAiB,GAAGC,uBAAwB;EAClEG,OAAO,IAAI,EAAES,OAAO,GAAGZ,uBAAuB,GAAGD,iBAAiB,CAAC;EAEnE,IAAI,EAAEI,OAAO,GAAGN,qBAAqB,CAAC,EAAE;IACtCM,OAAO,IAAI,EAAER,cAAc,GAAGC,kBAAkB,CAAC;EACnD;EACA,IAAIsB,OAAO,GAAG,CACZhB,IAAI,EAAEC,OAAO,EAAEG,OAAO,EAAEU,WAAW,EAAEH,UAAU,EAAEI,gBAAgB,EACjEF,eAAe,EAAEN,MAAM,EAAEC,GAAG,EAAEC,KAAK,CACpC;EAED,IAAIQ,MAAM,GAAGf,QAAQ,CAACgB,KAAK,CAACN,SAAS,EAAEI,OAAO,CAAC;EAC/C,IAAI1B,UAAU,CAACU,IAAI,CAAC,EAAE;IACpBT,OAAO,CAAC0B,MAAM,EAAED,OAAO,CAAC;EAC1B;EACAC,MAAM,CAACd,WAAW,GAAGA,WAAW;EAChC,OAAOX,eAAe,CAACyB,MAAM,EAAEjB,IAAI,EAAEC,OAAO,CAAC;AAC/C;AAEA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}