{"ast": null, "code": "import assignValue from './_assignValue.js';\nimport castPath from './_castPath.js';\nimport isIndex from './_isIndex.js';\nimport isObject from './isObject.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.set`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseSet(object, path, value, customizer) {\n  if (!isObject(object)) {\n    return object;\n  }\n  path = castPath(path, object);\n  var index = -1,\n    length = path.length,\n    lastIndex = length - 1,\n    nested = object;\n  while (nested != null && ++index < length) {\n    var key = toKey(path[index]),\n      newValue = value;\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return object;\n    }\n    if (index != lastIndex) {\n      var objValue = nested[key];\n      newValue = customizer ? customizer(objValue, key, nested) : undefined;\n      if (newValue === undefined) {\n        newValue = isObject(objValue) ? objValue : isIndex(path[index + 1]) ? [] : {};\n      }\n    }\n    assignValue(nested, key, newValue);\n    nested = nested[key];\n  }\n  return object;\n}\nexport default baseSet;", "map": {"version": 3, "names": ["assignValue", "<PERSON><PERSON><PERSON>", "isIndex", "isObject", "to<PERSON><PERSON>", "baseSet", "object", "path", "value", "customizer", "index", "length", "lastIndex", "nested", "key", "newValue", "objValue", "undefined"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/lodash-es/_baseSet.js"], "sourcesContent": ["import assignValue from './_assignValue.js';\nimport castPath from './_castPath.js';\nimport isIndex from './_isIndex.js';\nimport isObject from './isObject.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.set`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseSet(object, path, value, customizer) {\n  if (!isObject(object)) {\n    return object;\n  }\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length,\n      lastIndex = length - 1,\n      nested = object;\n\n  while (nested != null && ++index < length) {\n    var key = toKey(path[index]),\n        newValue = value;\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return object;\n    }\n\n    if (index != lastIndex) {\n      var objValue = nested[key];\n      newValue = customizer ? customizer(objValue, key, nested) : undefined;\n      if (newValue === undefined) {\n        newValue = isObject(objValue)\n          ? objValue\n          : (isIndex(path[index + 1]) ? [] : {});\n      }\n    }\n    assignValue(nested, key, newValue);\n    nested = nested[key];\n  }\n  return object;\n}\n\nexport default baseSet;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAE;EAChD,IAAI,CAACN,QAAQ,CAACG,MAAM,CAAC,EAAE;IACrB,OAAOA,MAAM;EACf;EACAC,IAAI,GAAGN,QAAQ,CAACM,IAAI,EAAED,MAAM,CAAC;EAE7B,IAAII,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,SAAS,GAAGD,MAAM,GAAG,CAAC;IACtBE,MAAM,GAAGP,MAAM;EAEnB,OAAOO,MAAM,IAAI,IAAI,IAAI,EAAEH,KAAK,GAAGC,MAAM,EAAE;IACzC,IAAIG,GAAG,GAAGV,KAAK,CAACG,IAAI,CAACG,KAAK,CAAC,CAAC;MACxBK,QAAQ,GAAGP,KAAK;IAEpB,IAAIM,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,aAAa,IAAIA,GAAG,KAAK,WAAW,EAAE;MACvE,OAAOR,MAAM;IACf;IAEA,IAAII,KAAK,IAAIE,SAAS,EAAE;MACtB,IAAII,QAAQ,GAAGH,MAAM,CAACC,GAAG,CAAC;MAC1BC,QAAQ,GAAGN,UAAU,GAAGA,UAAU,CAACO,QAAQ,EAAEF,GAAG,EAAED,MAAM,CAAC,GAAGI,SAAS;MACrE,IAAIF,QAAQ,KAAKE,SAAS,EAAE;QAC1BF,QAAQ,GAAGZ,QAAQ,CAACa,QAAQ,CAAC,GACzBA,QAAQ,GACPd,OAAO,CAACK,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAE;MAC1C;IACF;IACAV,WAAW,CAACa,MAAM,EAAEC,GAAG,EAAEC,QAAQ,CAAC;IAClCF,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC;EACtB;EACA,OAAOR,MAAM;AACf;AAEA,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}