{"ast": null, "code": "/**\n * @private\n */\n\nimport Zone from \"../zone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport InvalidZone from \"../zones/invalidZone.js\";\nimport { isUndefined, isString, isNumber } from \"./util.js\";\nimport SystemZone from \"../zones/systemZone.js\";\nexport function normalizeZone(input, defaultZone) {\n  let offset;\n  if (isUndefined(input) || input === null) {\n    return defaultZone;\n  } else if (input instanceof Zone) {\n    return input;\n  } else if (isString(input)) {\n    const lowered = input.toLowerCase();\n    if (lowered === \"default\") return defaultZone;else if (lowered === \"local\" || lowered === \"system\") return SystemZone.instance;else if (lowered === \"utc\" || lowered === \"gmt\") return FixedOffsetZone.utcInstance;else return FixedOffsetZone.parseSpecifier(lowered) || IANAZone.create(input);\n  } else if (isNumber(input)) {\n    return FixedOffsetZone.instance(input);\n  } else if (typeof input === \"object\" && \"offset\" in input && typeof input.offset === \"function\") {\n    // This is dumb, but the instanceof check above doesn't seem to really work\n    // so we're duck checking it\n    return input;\n  } else {\n    return new InvalidZone(input);\n  }\n}", "map": {"version": 3, "names": ["Zone", "IANAZone", "FixedOffsetZone", "InvalidZone", "isUndefined", "isString", "isNumber", "SystemZone", "normalizeZone", "input", "defaultZone", "offset", "lowered", "toLowerCase", "instance", "utcInstance", "parseSpecifier", "create"], "sources": ["C:/Users/<USER>/Desktop/digii/digitorywebv4/node_modules/luxon/src/impl/zoneUtil.js"], "sourcesContent": ["/**\n * @private\n */\n\nimport Zone from \"../zone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport InvalidZone from \"../zones/invalidZone.js\";\n\nimport { isUndefined, isString, isNumber } from \"./util.js\";\nimport SystemZone from \"../zones/systemZone.js\";\n\nexport function normalizeZone(input, defaultZone) {\n  let offset;\n  if (isUndefined(input) || input === null) {\n    return defaultZone;\n  } else if (input instanceof Zone) {\n    return input;\n  } else if (isString(input)) {\n    const lowered = input.toLowerCase();\n    if (lowered === \"default\") return defaultZone;\n    else if (lowered === \"local\" || lowered === \"system\") return SystemZone.instance;\n    else if (lowered === \"utc\" || lowered === \"gmt\") return FixedOffsetZone.utcInstance;\n    else return FixedOffsetZone.parseSpecifier(lowered) || IANAZone.create(input);\n  } else if (isNumber(input)) {\n    return FixedOffsetZone.instance(input);\n  } else if (typeof input === \"object\" && \"offset\" in input && typeof input.offset === \"function\") {\n    // This is dumb, but the instanceof check above doesn't seem to really work\n    // so we're duck checking it\n    return input;\n  } else {\n    return new InvalidZone(input);\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAOA,IAAI,MAAM,YAAY;AAC7B,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,WAAW,MAAM,yBAAyB;AAEjD,SAASC,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,WAAW;AAC3D,OAAOC,UAAU,MAAM,wBAAwB;AAE/C,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAChD,IAAIC,MAAM;EACV,IAAIP,WAAW,CAACK,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,EAAE;IACxC,OAAOC,WAAW;EACpB,CAAC,MAAM,IAAID,KAAK,YAAYT,IAAI,EAAE;IAChC,OAAOS,KAAK;EACd,CAAC,MAAM,IAAIJ,QAAQ,CAACI,KAAK,CAAC,EAAE;IAC1B,MAAMG,OAAO,GAAGH,KAAK,CAACI,WAAW,CAAC,CAAC;IACnC,IAAID,OAAO,KAAK,SAAS,EAAE,OAAOF,WAAW,CAAC,KACzC,IAAIE,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,QAAQ,EAAE,OAAOL,UAAU,CAACO,QAAQ,CAAC,KAC5E,IAAIF,OAAO,KAAK,KAAK,IAAIA,OAAO,KAAK,KAAK,EAAE,OAAOV,eAAe,CAACa,WAAW,CAAC,KAC/E,OAAOb,eAAe,CAACc,cAAc,CAACJ,OAAO,CAAC,IAAIX,QAAQ,CAACgB,MAAM,CAACR,KAAK,CAAC;EAC/E,CAAC,MAAM,IAAIH,QAAQ,CAACG,KAAK,CAAC,EAAE;IAC1B,OAAOP,eAAe,CAACY,QAAQ,CAACL,KAAK,CAAC;EACxC,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAACE,MAAM,KAAK,UAAU,EAAE;IAC/F;IACA;IACA,OAAOF,KAAK;EACd,CAAC,MAAM;IACL,OAAO,IAAIN,WAAW,CAACM,KAAK,CAAC;EAC/B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}