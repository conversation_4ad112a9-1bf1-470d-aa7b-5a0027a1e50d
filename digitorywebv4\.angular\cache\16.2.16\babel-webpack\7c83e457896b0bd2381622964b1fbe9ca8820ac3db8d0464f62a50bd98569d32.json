{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/smart-dashboard.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"../../services/share-data.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/datepicker\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"ng2-charts\";\nimport * as i15 from \"ngx-mat-select-search\";\nimport * as i16 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const branch_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", branch_r6.restaurantIdOld);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", branch_r6.branchName, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"mat-spinner\", 37);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SmartDashboardComponent_div_67_mat_card_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 44)(1, \"mat-card-content\")(2, \"div\", 45)(3, \"div\", 46)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 47)(7, \"div\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 49);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const card_r10 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", card_r10.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", card_r10.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r10.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(card_r10.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r10.label);\n  }\n}\nfunction SmartDashboardComponent_div_67_mat_card_4_canvas_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 54);\n  }\n  if (rf & 2) {\n    const chart_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"type\", \"line\")(\"data\", ctx_r13.getChartData(chart_r11))(\"options\", ctx_r13.lineChartOptions);\n  }\n}\nfunction SmartDashboardComponent_div_67_mat_card_4_canvas_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 54);\n  }\n  if (rf & 2) {\n    const chart_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"type\", \"bar\")(\"data\", ctx_r14.getChartData(chart_r11))(\"options\", ctx_r14.barChartOptions);\n  }\n}\nfunction SmartDashboardComponent_div_67_mat_card_4_canvas_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"canvas\", 54);\n  }\n  if (rf & 2) {\n    const chart_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"type\", \"doughnut\")(\"data\", ctx_r15.getChartData(chart_r11))(\"options\", ctx_r15.doughnutChartOptions);\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"full-width\": a0,\n    \"half-width\": a1,\n    \"third-width\": a2\n  };\n};\nfunction SmartDashboardComponent_div_67_mat_card_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 50)(1, \"mat-card-header\")(2, \"mat-card-title\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 52);\n    i0.ɵɵtemplate(6, SmartDashboardComponent_div_67_mat_card_4_canvas_6_Template, 1, 3, \"canvas\", 53);\n    i0.ɵɵtemplate(7, SmartDashboardComponent_div_67_mat_card_4_canvas_7_Template, 1, 3, \"canvas\", 53);\n    i0.ɵɵtemplate(8, SmartDashboardComponent_div_67_mat_card_4_canvas_8_Template, 1, 3, \"canvas\", 53);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const chart_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, chart_r11.type === \"line\" || chart_r11.title.toLowerCase().includes(\"trend\"), chart_r11.type !== \"line\" && !chart_r11.title.toLowerCase().includes(\"trend\"), chart_r11.type === \"doughnut\" || chart_r11.type === \"pie\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(chart_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-chart-type\", chart_r11.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chart_r11.type === \"line\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chart_r11.type === \"bar\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chart_r11.type === \"doughnut\" || chart_r11.type === \"pie\");\n  }\n}\nfunction SmartDashboardComponent_div_67_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-card\", 55)(2, \"mat-card-header\")(3, \"mat-card-title\", 51);\n    i0.ɵɵtext(4, \"Purchase Trends (Last 30 Days)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 52)(7, \"div\", 56)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"show_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11, \"Purchase trends data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(12, \"mat-card\", 57)(13, \"mat-card-header\")(14, \"mat-card-title\", 51);\n    i0.ɵɵtext(15, \"Top Vendors by Purchase Amount\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"mat-card-content\")(17, \"div\", 52)(18, \"div\", 56)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\");\n    i0.ɵɵtext(22, \"Vendor data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(23, \"mat-card\", 57)(24, \"mat-card-header\")(25, \"mat-card-title\", 51);\n    i0.ɵɵtext(26, \"Category wise Spending Distribution\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"mat-card-content\")(28, \"div\", 52)(29, \"div\", 56)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"pie_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"Category spending data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"mat-card\", 55)(35, \"mat-card-header\")(36, \"mat-card-title\", 51);\n    i0.ɵɵtext(37, \"Top Items by Quantity Purchased\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"mat-card-content\")(39, \"div\", 52)(40, \"div\", 56)(41, \"mat-icon\");\n    i0.ɵɵtext(42, \"inventory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\");\n    i0.ɵɵtext(44, \"Top items data will appear here\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SmartDashboardComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_67_mat_card_2_Template, 11, 7, \"mat-card\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 41);\n    i0.ɵɵtemplate(4, SmartDashboardComponent_div_67_mat_card_4_Template, 9, 10, \"mat-card\", 42);\n    i0.ɵɵtemplate(5, SmartDashboardComponent_div_67_ng_container_5_Template, 45, 0, \"ng-container\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.summaryCards);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.charts);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.charts.length === 0 && !ctx_r4.isLoading);\n  }\n}\nfunction SmartDashboardComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"mat-icon\", 59);\n    i0.ɵɵtext(2, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Data Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Please select a location and date range to view dashboard data.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_68_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.loadDashboardData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Refresh Data \");\n    i0.ɵɵelementEnd()();\n  }\n}\nclass SmartDashboardComponent {\n  constructor(smartDashboardService, authService, shareDataService, cdr) {\n    this.smartDashboardService = smartDashboardService;\n    this.authService = authService;\n    this.shareDataService = shareDataService;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.branches = [];\n    this.filteredBranches = [];\n    this.selectedLocation = null;\n    // Form controls\n    this.locationFilterCtrl = new FormControl('');\n    this.startDate = new FormControl(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 30 days ago\n    this.endDate = new FormControl(new Date());\n    // Dashboard data\n    this.summaryCards = [];\n    this.charts = [];\n    this.isLoading = false;\n    // Chart configurations\n    this.lineChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: true,\n          position: 'top',\n          labels: {\n            usePointStyle: true,\n            padding: 20\n          }\n        },\n        tooltip: {\n          mode: 'index',\n          intersect: false,\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          titleColor: '#fff',\n          bodyColor: '#fff',\n          borderColor: '#ff6b35',\n          borderWidth: 1\n        }\n      },\n      scales: {\n        x: {\n          display: true,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          },\n          title: {\n            display: false\n          }\n        },\n        y: {\n          display: true,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          },\n          title: {\n            display: false\n          },\n          beginAtZero: true,\n          ticks: {\n            callback: function (value) {\n              return '₹' + value.toLocaleString();\n            }\n          }\n        }\n      },\n      interaction: {\n        mode: 'nearest',\n        axis: 'x',\n        intersect: false\n      }\n    };\n    this.barChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      indexAxis: 'y',\n      plugins: {\n        legend: {\n          display: false\n        },\n        tooltip: {\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          titleColor: '#fff',\n          bodyColor: '#fff',\n          borderColor: '#ff6b35',\n          borderWidth: 1,\n          callbacks: {\n            label: function (context) {\n              return '₹' + context.parsed.x.toLocaleString();\n            }\n          }\n        }\n      },\n      scales: {\n        x: {\n          beginAtZero: true,\n          grid: {\n            display: true,\n            color: 'rgba(0,0,0,0.1)'\n          },\n          title: {\n            display: false\n          },\n          ticks: {\n            callback: function (value) {\n              return '₹' + value.toLocaleString();\n            }\n          }\n        },\n        y: {\n          grid: {\n            display: false\n          },\n          title: {\n            display: false\n          }\n        }\n      }\n    };\n    this.doughnutChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: true,\n          position: 'right',\n          labels: {\n            usePointStyle: true,\n            padding: 15,\n            font: {\n              size: 12\n            }\n          }\n        },\n        tooltip: {\n          backgroundColor: 'rgba(0,0,0,0.8)',\n          titleColor: '#fff',\n          bodyColor: '#fff',\n          borderColor: '#ff6b35',\n          borderWidth: 1,\n          callbacks: {\n            label: function (context) {\n              const label = context.label || '';\n              const value = context.parsed;\n              const total = context.dataset.data.reduce((a, b) => a + b, 0);\n              const percentage = (value / total * 100).toFixed(1);\n              return `${label}: ${percentage}%`;\n            }\n          }\n        }\n      },\n      cutout: '60%'\n    };\n    this.quantityBarChartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          title: {\n            display: true,\n            text: 'Items'\n          }\n        },\n        y: {\n          beginAtZero: true,\n          title: {\n            display: true,\n            text: 'Quantity'\n          }\n        }\n      }\n    };\n    this.user = this.authService.getCurrentUser();\n  }\n  ngOnInit() {\n    this.initializeFilters();\n    this.loadBranches();\n    this.loadDashboardData();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeFilters() {\n    // Location filter\n    this.locationFilterCtrl.valueChanges.pipe(debounceTime(200), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(value => {\n      this.filterBranches(value || '');\n    });\n  }\n  loadBranches() {\n    this.shareDataService.selectedBranchesSource.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.branches = data || [];\n      this.filteredBranches = [...this.branches];\n      if (this.branches.length === 1) {\n        this.selectedLocation = this.branches[0].restaurantIdOld;\n      }\n    });\n  }\n  filterBranches(searchTerm) {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm));\n    }\n  }\n  loadDashboardData() {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          // Load sample data for demonstration\n          this.loadSampleData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading dashboard data:', error);\n        // Load sample data on error for demonstration\n        this.loadSampleData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  loadSampleData() {\n    // Sample data matching the UI shown in the image\n    const sampleData = {\n      summary_items: [{\n        icon: 'attach_money',\n        value: '₹34,766',\n        label: 'Total Purchase Amount',\n        data_type: 'currency'\n      }, {\n        icon: 'shopping_cart',\n        value: '271',\n        label: 'Total Orders',\n        data_type: 'number'\n      }, {\n        icon: 'trending_up',\n        value: '₹1,973',\n        label: 'Average Order Value',\n        data_type: 'currency'\n      }, {\n        icon: 'store',\n        value: 'Fresh Mart',\n        label: 'Top Vendor',\n        data_type: 'vendor'\n      }],\n      charts: [{\n        id: 'purchase-trends',\n        title: 'Purchase Trends (Last 30 Days)',\n        type: 'line',\n        data: {\n          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n          datasets: [{\n            label: 'Daily Purchase Amount',\n            data: [15000, 22000, 18000, 25000],\n            backgroundColor: ['#ff6b35'],\n            borderColor: ['#ff6b35']\n          }]\n        }\n      }, {\n        id: 'top-vendors',\n        title: 'Top Vendors by Purchase Amount',\n        type: 'bar',\n        data: {\n          labels: ['ABC Suppliers', 'XYZ Foods', 'Fresh Mart', 'Quality Grocers', 'Best Price Co'],\n          datasets: [{\n            label: 'Total Purchase Amount',\n            data: [180000, 150000, 140000, 120000, 100000],\n            backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n            borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n          }]\n        }\n      }, {\n        id: 'category-spending',\n        title: 'Category wise Spending Distribution',\n        type: 'doughnut',\n        data: {\n          labels: ['Vegetables', 'Meat', 'Dairy', 'Beverages', 'Dry Goods'],\n          datasets: [{\n            label: 'Spending Distribution',\n            data: [30, 25, 20, 15, 10],\n            backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n            borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n          }]\n        }\n      }]\n    };\n    this.processDashboardData(sampleData);\n  }\n  processDashboardData(data) {\n    // Process summary cards\n    this.summaryCards = data.summary_items?.map(item => ({\n      icon: this.smartDashboardService.getSummaryCardIcon(item.data_type, item.label),\n      value: item.value,\n      label: item.label,\n      color: this.getCardColor(item.data_type)\n    })) || [];\n    // If no summary cards from API, create default ones\n    if (this.summaryCards.length === 0) {\n      this.summaryCards = this.createDefaultSummaryCards();\n    }\n    // Process charts with enhanced data\n    this.charts = data.charts?.map(chart => ({\n      ...chart,\n      data: this.smartDashboardService.processChartData(chart)\n    })) || [];\n    // If no charts from API, create default message\n    if (this.charts.length === 0) {\n      console.log('No charts data received from API');\n    }\n  }\n  createDefaultSummaryCards() {\n    return [{\n      icon: 'attach_money',\n      value: '₹34,766',\n      label: 'Total Purchase Amount',\n      color: '#ff6b35'\n    }, {\n      icon: 'shopping_cart',\n      value: '271',\n      label: 'Total Orders',\n      color: '#ffa66f'\n    }, {\n      icon: 'trending_up',\n      value: '₹1,973',\n      label: 'Average Order Value',\n      color: '#ff8b4d'\n    }, {\n      icon: 'store',\n      value: 'Fresh Mart',\n      label: 'Top Vendor',\n      color: '#ff9966'\n    }];\n  }\n  getCardColor(dataType) {\n    const colorMap = {\n      'currency': '#ff6b35',\n      'number': '#ffa66f',\n      'percentage': '#ff8b4d',\n      'vendor': '#ff9966'\n    };\n    return colorMap[dataType] || '#ff6b35';\n  }\n  formatDate(date) {\n    return date.toISOString().split('T')[0];\n  }\n  onLocationChange() {\n    this.loadDashboardData();\n  }\n  onDateChange() {\n    this.loadDashboardData();\n  }\n  getChartData(chart) {\n    return chart.data;\n  }\n  getChartType(chart) {\n    return chart.type;\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.SmartDashboardService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ShareDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 69,\n      vars: 12,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"header-left\"], [1, \"assistant-info\"], [1, \"assistant-icon\"], [1, \"assistant-text\"], [1, \"assistant-title\"], [1, \"assistant-status\"], [1, \"header-right\"], [1, \"search-container\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Ask me about your business data\"], [\"matSuffix\", \"\", 1, \"search-icon\"], [1, \"dashboard-content\"], [1, \"sidebar\"], [1, \"filters-section\"], [1, \"filters-title\"], [1, \"filter-count\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [3, \"value\", \"valueChange\", \"selectionChange\"], [\"placeholderLabel\", \"Search locations...\", \"noEntriesFoundLabel\", \"No locations found\", 3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", 3, \"matDatepicker\", \"formControl\", \"dateChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"endPicker\", \"\"], [1, \"filter-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-filters-btn\", 3, \"click\"], [1, \"main-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"dashboard-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [3, \"value\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"dashboard-grid\"], [1, \"summary-cards-row\"], [\"class\", \"summary-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"charts-grid\"], [\"class\", \"chart-card\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"summary-card\"], [1, \"card-content\"], [1, \"card-icon\"], [1, \"card-info\"], [1, \"card-value\"], [1, \"card-label\"], [1, \"chart-card\", 3, \"ngClass\"], [1, \"chart-title\"], [1, \"chart-container\"], [\"baseChart\", \"\", 3, \"type\", \"data\", \"options\", 4, \"ngIf\"], [\"baseChart\", \"\", 3, \"type\", \"data\", \"options\"], [1, \"chart-card\", \"full-width\"], [1, \"no-data-message\"], [1, \"chart-card\", \"half-width\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"mat-icon\", 5);\n          i0.ɵɵtext(6, \"smart_toy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"span\", 7);\n          i0.ɵɵtext(9, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"span\", 8);\n          i0.ɵɵtext(11, \"Ready to analyze\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"mat-form-field\", 11)(15, \"mat-label\");\n          i0.ɵɵtext(16, \"Ask me about your business data\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 12);\n          i0.ɵɵelementStart(18, \"mat-icon\", 13);\n          i0.ɵɵtext(19, \"search\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(20, \"div\", 14)(21, \"div\", 15)(22, \"div\", 16)(23, \"h3\", 17)(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \" Smart Filters \");\n          i0.ɵɵelementStart(27, \"span\", 18);\n          i0.ɵɵtext(28, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 19)(30, \"h4\", 20)(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(33, \" Restaurants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"mat-form-field\", 21)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Select Location\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"mat-select\", 22);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_37_listener($event) {\n            return ctx.selectedLocation = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_37_listener() {\n            return ctx.onLocationChange();\n          });\n          i0.ɵɵelementStart(38, \"mat-option\");\n          i0.ɵɵelement(39, \"ngx-mat-select-search\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, SmartDashboardComponent_mat_option_40_Template, 2, 2, \"mat-option\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 19)(42, \"h4\", 20)(43, \"mat-icon\");\n          i0.ɵɵtext(44, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(45, \" Base Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-form-field\", 21)(47, \"mat-label\");\n          i0.ɵɵtext(48, \"Start Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"input\", 25);\n          i0.ɵɵlistener(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_49_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(50, \"mat-datepicker-toggle\", 26)(51, \"mat-datepicker\", null, 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"mat-form-field\", 21)(54, \"mat-label\");\n          i0.ɵɵtext(55, \"End Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"input\", 25);\n          i0.ɵɵlistener(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_56_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(57, \"mat-datepicker-toggle\", 26)(58, \"mat-datepicker\", null, 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 29)(61, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_61_listener() {\n            return ctx.loadDashboardData();\n          });\n          i0.ɵɵelementStart(62, \"mat-icon\");\n          i0.ɵɵtext(63, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(64, \" Reset filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(65, \"div\", 31);\n          i0.ɵɵtemplate(66, SmartDashboardComponent_div_66_Template, 4, 0, \"div\", 32);\n          i0.ɵɵtemplate(67, SmartDashboardComponent_div_67_Template, 6, 3, \"div\", 33);\n          i0.ɵɵtemplate(68, SmartDashboardComponent_div_68_Template, 11, 0, \"div\", 34);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(52);\n          const _r2 = i0.ɵɵreference(59);\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.locationFilterCtrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredBranches);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matDatepicker\", _r1)(\"formControl\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r1);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matDatepicker\", _r2)(\"formControl\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r2);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.summaryCards.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, MatCardModule, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, MatButtonModule, i6.MatButton, MatIconModule, i7.MatIcon, MatSelectModule, i8.MatFormField, i8.MatLabel, i8.MatSuffix, i9.MatSelect, i10.MatOption, MatFormFieldModule, MatInputModule, i11.MatInput, MatDatepickerModule, i12.MatDatepicker, i12.MatDatepickerInput, i12.MatDatepickerToggle, MatNativeDateModule, MatDividerModule, MatProgressSpinnerModule, i13.MatProgressSpinner, NgChartsModule, i14.BaseChartDirective, NgxMatSelectSearchModule, i15.MatSelectSearchComponent, ReactiveFormsModule, i16.DefaultValueAccessor, i16.NgControlStatus, i16.FormControlDirective, FormsModule],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #f8f9fa;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #e9ecef;\\n  padding: 16px 24px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%]   .assistant-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%]   .assistant-status[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #ff6b35;\\n  font-weight: 500;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 400px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  color: #dee2e6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-focus-overlay {\\n  background-color: transparent;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  overflow: hidden;\\n  height: calc(100vh - 80px);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background: white;\\n  border-right: 1px solid #e9ecef;\\n  padding: 20px;\\n  overflow-y: auto;\\n  height: 100%;\\n  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 0 20px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background: #ff6b35;\\n  color: white;\\n  border-radius: 50%;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 11px;\\n  font-weight: bold;\\n  margin-left: auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 0 12px 0;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n  color: #ff6b35;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 8px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background-color: white;\\n  border-radius: 4px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  color: #dee2e6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline-thick {\\n  color: #ff6b35;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n  padding-top: 20px;\\n  border-top: 1px solid #e9ecef;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .reset-filters-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  color: #6c757d;\\n  border-color: #dee2e6;\\n  font-weight: 500;\\n  padding: 10px;\\n  border-radius: 4px;\\n  font-size: 14px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .reset-filters-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  border-color: #adb5bd;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 24px;\\n  overflow-y: auto;\\n  background-color: #f8f9fa;\\n  height: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  gap: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, 1fr);\\n  gap: 16px;\\n  margin-bottom: 24px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n  transition: box-shadow 0.2s ease;\\n  position: relative;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:nth-child(1) {\\n  border-left: 4px solid #ff6b35;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:nth-child(2) {\\n  border-left: 4px solid #ffa66f;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:nth-child(3) {\\n  border-left: 4px solid #ff8b4d;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:nth-child(4) {\\n  border-left: 4px solid #ff9966;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]     .mat-mdc-card-content {\\n  padding: 16px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 4px;\\n  line-height: 1.2;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   .card-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c757d;\\n  font-weight: 500;\\n  line-height: 1.3;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(12, 1fr);\\n  gap: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: box-shadow 0.2s ease;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.full-width[_ngcontent-%COMP%] {\\n  grid-column: span 12;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.half-width[_ngcontent-%COMP%] {\\n  grid-column: span 6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.third-width[_ngcontent-%COMP%] {\\n  grid-column: span 4;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]     .mat-mdc-card-header {\\n  padding: 20px 20px 0 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]     .mat-mdc-card-content {\\n  padding: 16px 20px 20px 20px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  height: 300px;\\n  position: relative;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\\n  max-height: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  color: #999;\\n  text-align: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  margin-bottom: 12px;\\n  opacity: 0.5;\\n  color: #ff6b35;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[data-chart-type=line][_ngcontent-%COMP%] {\\n  height: 350px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[data-chart-type=doughnut][_ngcontent-%COMP%], .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[data-chart-type=pie][_ngcontent-%COMP%] {\\n  height: 280px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  text-align: center;\\n  color: #666;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  margin-bottom: 16px;\\n  opacity: 0.5;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  font-size: 14px;\\n}\\n\\n@media (max-width: 1200px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.half-width[_ngcontent-%COMP%], .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.third-width[_ngcontent-%COMP%] {\\n    grid-column: span 12;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    text-align: center;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e0e0e0;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .dashboard-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n  .chart-container canvas {\\n  max-width: 100% !important;\\n  height: auto !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatFormFieldModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatDividerModule", "MatProgressSpinnerModule", "NgChartsModule", "NgxMatSelectSearchModule", "FormControl", "ReactiveFormsModule", "FormsModule", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "branch_r6", "restaurantIdOld", "ɵɵadvance", "ɵɵtextInterpolate1", "branchName", "ɵɵelement", "ɵɵstyleProp", "card_r10", "color", "ɵɵtextInterpolate", "icon", "value", "label", "ctx_r13", "getChartData", "chart_r11", "lineChartOptions", "ctx_r14", "barChartOptions", "ctx_r15", "doughnutChartOptions", "ɵɵtemplate", "SmartDashboardComponent_div_67_mat_card_4_canvas_6_Template", "SmartDashboardComponent_div_67_mat_card_4_canvas_7_Template", "SmartDashboardComponent_div_67_mat_card_4_canvas_8_Template", "ɵɵpureFunction3", "_c0", "type", "title", "toLowerCase", "includes", "ɵɵattribute", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "SmartDashboardComponent_div_67_mat_card_2_Template", "SmartDashboardComponent_div_67_mat_card_4_Template", "SmartDashboardComponent_div_67_ng_container_5_Template", "ctx_r4", "summaryCards", "charts", "length", "isLoading", "ɵɵlistener", "SmartDashboardComponent_div_68_Template_button_click_7_listener", "ɵɵrestoreView", "_r20", "ctx_r19", "ɵɵnextContext", "ɵɵresetView", "loadDashboardData", "SmartDashboardComponent", "constructor", "smartDashboardService", "authService", "shareDataService", "cdr", "destroy$", "branches", "filteredBranches", "selectedLocation", "locationFilterCtrl", "startDate", "Date", "now", "endDate", "responsive", "maintainAspectRatio", "plugins", "legend", "display", "position", "labels", "usePointStyle", "padding", "tooltip", "mode", "intersect", "backgroundColor", "titleColor", "bodyColor", "borderColor", "borderWidth", "scales", "x", "grid", "y", "beginAtZero", "ticks", "callback", "toLocaleString", "interaction", "axis", "indexAxis", "callbacks", "context", "parsed", "font", "size", "total", "dataset", "data", "reduce", "a", "b", "percentage", "toFixed", "cutout", "quantityBarChartOptions", "text", "user", "getCurrentUser", "ngOnInit", "initializeFilters", "loadBranches", "ngOnDestroy", "next", "complete", "valueChanges", "pipe", "subscribe", "filterBranches", "selectedBranchesSource", "searchTerm", "normalizedSearchTerm", "replace", "filter", "branch", "filters", "locations", "formatDate", "baseDate", "request", "tenant_id", "tenantId", "user_query", "use_default_charts", "getSmartDashboardData", "response", "status", "processDashboardData", "loadSampleData", "detectChanges", "error", "console", "sampleData", "summary_items", "data_type", "id", "datasets", "map", "item", "getSummaryCardIcon", "getCardColor", "createDefaultSummaryCards", "chart", "processChartData", "log", "dataType", "colorMap", "date", "toISOString", "split", "onLocationChange", "onDateChange", "getChartType", "ɵɵdirectiveInject", "i1", "SmartDashboardService", "i2", "AuthService", "i3", "ShareDataService", "ChangeDetectorRef", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "SmartDashboardComponent_Template_mat_select_valueChange_37_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_37_listener", "SmartDashboardComponent_mat_option_40_Template", "SmartDashboardComponent_Template_input_dateChange_49_listener", "SmartDashboardComponent_Template_input_dateChange_56_listener", "SmartDashboardComponent_Template_button_click_61_listener", "SmartDashboardComponent_div_66_Template", "SmartDashboardComponent_div_67_Template", "SmartDashboardComponent_div_68_Template", "_r1", "_r2", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i6", "MatButton", "i7", "MatIcon", "i8", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i9", "MatSelect", "i10", "MatOption", "i11", "MatInput", "i12", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i13", "MatProgressSpinner", "i14", "BaseChartDirective", "i15", "MatSelectSearchComponent", "i16", "DefaultValueAccessor", "NgControlStatus", "FormControlDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { ChartConfiguration, ChartData, ChartType } from 'chart.js';\n\nimport { SmartDashboardService } from '../../services/smart-dashboard.service';\nimport { AuthService } from '../../services/auth.service';\nimport { ShareDataService } from '../../services/share-data.service';\n\ninterface SummaryCard {\n  icon: string;\n  value: string;\n  label: string;\n  color: string;\n}\n\ninterface ChartDataModel {\n  id: string;\n  title: string;\n  type: string;\n  data: ChartData;\n}\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    NgChartsModule,\n    NgxMatSelectSearchModule,\n    ReactiveFormsModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  // User and branch data\n  user: any;\n  branches: any[] = [];\n  filteredBranches: any[] = [];\n  selectedLocation: string | null = null;\n  \n  // Form controls\n  locationFilterCtrl = new FormControl('');\n  startDate = new FormControl(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 30 days ago\n  endDate = new FormControl(new Date());\n  \n  // Dashboard data\n  summaryCards: SummaryCard[] = [];\n  charts: ChartDataModel[] = [];\n  isLoading = false;\n  \n  // Chart configurations\n  lineChartOptions: ChartConfiguration['options'] = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: true,\n        position: 'top',\n        labels: {\n          usePointStyle: true,\n          padding: 20\n        }\n      },\n      tooltip: {\n        mode: 'index',\n        intersect: false,\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: '#ff6b35',\n        borderWidth: 1\n      }\n    },\n    scales: {\n      x: {\n        display: true,\n        grid: {\n          display: true,\n          color: 'rgba(0,0,0,0.1)'\n        },\n        title: {\n          display: false\n        }\n      },\n      y: {\n        display: true,\n        grid: {\n          display: true,\n          color: 'rgba(0,0,0,0.1)'\n        },\n        title: {\n          display: false\n        },\n        beginAtZero: true,\n        ticks: {\n          callback: function(value: any) {\n            return '₹' + value.toLocaleString();\n          }\n        }\n      }\n    },\n    interaction: {\n      mode: 'nearest',\n      axis: 'x',\n      intersect: false\n    }\n  };\n\n  barChartOptions: ChartConfiguration['options'] = {\n    responsive: true,\n    maintainAspectRatio: false,\n    indexAxis: 'y' as const,\n    plugins: {\n      legend: {\n        display: false\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: '#ff6b35',\n        borderWidth: 1,\n        callbacks: {\n          label: function(context: any) {\n            return '₹' + context.parsed.x.toLocaleString();\n          }\n        }\n      }\n    },\n    scales: {\n      x: {\n        beginAtZero: true,\n        grid: {\n          display: true,\n          color: 'rgba(0,0,0,0.1)'\n        },\n        title: {\n          display: false\n        },\n        ticks: {\n          callback: function(value: any) {\n            return '₹' + value.toLocaleString();\n          }\n        }\n      },\n      y: {\n        grid: {\n          display: false\n        },\n        title: {\n          display: false\n        }\n      }\n    }\n  };\n\n  doughnutChartOptions: any = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: true,\n        position: 'right',\n        labels: {\n          usePointStyle: true,\n          padding: 15,\n          font: {\n            size: 12\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: '#ff6b35',\n        borderWidth: 1,\n        callbacks: {\n          label: function(context: any) {\n            const label = context.label || '';\n            const value = context.parsed;\n            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);\n            const percentage = ((value / total) * 100).toFixed(1);\n            return `${label}: ${percentage}%`;\n          }\n        }\n      }\n    },\n    cutout: '60%'\n  };\n\n  quantityBarChartOptions: ChartConfiguration['options'] = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        title: {\n          display: true,\n          text: 'Items'\n        }\n      },\n      y: {\n        beginAtZero: true,\n        title: {\n          display: true,\n          text: 'Quantity'\n        }\n      }\n    }\n  };\n\n  constructor(\n    private smartDashboardService: SmartDashboardService,\n    private authService: AuthService,\n    private shareDataService: ShareDataService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.user = this.authService.getCurrentUser();\n  }\n\n  ngOnInit(): void {\n    this.initializeFilters();\n    this.loadBranches();\n    this.loadDashboardData();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeFilters(): void {\n    // Location filter\n    this.locationFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(200),\n        distinctUntilChanged(),\n        takeUntil(this.destroy$)\n      )\n      .subscribe((value: string | null) => {\n        this.filterBranches(value || '');\n      });\n  }\n\n  private loadBranches(): void {\n    this.shareDataService.selectedBranchesSource\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.branches = data || [];\n        this.filteredBranches = [...this.branches];\n        \n        if (this.branches.length === 1) {\n          this.selectedLocation = this.branches[0].restaurantIdOld;\n        }\n      });\n  }\n\n  private filterBranches(searchTerm: string): void {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch =>\n        branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm)\n      );\n    }\n  }\n\n  loadDashboardData(): void {\n    if (!this.startDate.value || !this.endDate.value) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            // Load sample data for demonstration\n            this.loadSampleData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: (error) => {\n          console.error('Error loading dashboard data:', error);\n          // Load sample data on error for demonstration\n          this.loadSampleData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  private loadSampleData(): void {\n    // Sample data matching the UI shown in the image\n    const sampleData = {\n      summary_items: [\n        { icon: 'attach_money', value: '₹34,766', label: 'Total Purchase Amount', data_type: 'currency' },\n        { icon: 'shopping_cart', value: '271', label: 'Total Orders', data_type: 'number' },\n        { icon: 'trending_up', value: '₹1,973', label: 'Average Order Value', data_type: 'currency' },\n        { icon: 'store', value: 'Fresh Mart', label: 'Top Vendor', data_type: 'vendor' }\n      ],\n      charts: [\n        {\n          id: 'purchase-trends',\n          title: 'Purchase Trends (Last 30 Days)',\n          type: 'line',\n          data: {\n            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n            datasets: [{\n              label: 'Daily Purchase Amount',\n              data: [15000, 22000, 18000, 25000],\n              backgroundColor: ['#ff6b35'],\n              borderColor: ['#ff6b35']\n            }]\n          }\n        },\n        {\n          id: 'top-vendors',\n          title: 'Top Vendors by Purchase Amount',\n          type: 'bar',\n          data: {\n            labels: ['ABC Suppliers', 'XYZ Foods', 'Fresh Mart', 'Quality Grocers', 'Best Price Co'],\n            datasets: [{\n              label: 'Total Purchase Amount',\n              data: [180000, 150000, 140000, 120000, 100000],\n              backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n              borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n            }]\n          }\n        },\n        {\n          id: 'category-spending',\n          title: 'Category wise Spending Distribution',\n          type: 'doughnut',\n          data: {\n            labels: ['Vegetables', 'Meat', 'Dairy', 'Beverages', 'Dry Goods'],\n            datasets: [{\n              label: 'Spending Distribution',\n              data: [30, 25, 20, 15, 10],\n              backgroundColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3'],\n              borderColor: ['#ff6b35', '#ffa66f', '#ff8b4d', '#ff9966', '#ffd1b3']\n            }]\n          }\n        }\n      ]\n    };\n\n    this.processDashboardData(sampleData);\n  }\n\n  private processDashboardData(data: any): void {\n    // Process summary cards\n    this.summaryCards = data.summary_items?.map((item: any) => ({\n      icon: this.smartDashboardService.getSummaryCardIcon(item.data_type, item.label),\n      value: item.value,\n      label: item.label,\n      color: this.getCardColor(item.data_type)\n    })) || [];\n\n    // If no summary cards from API, create default ones\n    if (this.summaryCards.length === 0) {\n      this.summaryCards = this.createDefaultSummaryCards();\n    }\n\n    // Process charts with enhanced data\n    this.charts = data.charts?.map((chart: any) => ({\n      ...chart,\n      data: this.smartDashboardService.processChartData(chart)\n    })) || [];\n\n    // If no charts from API, create default message\n    if (this.charts.length === 0) {\n      console.log('No charts data received from API');\n    }\n  }\n\n  private createDefaultSummaryCards(): SummaryCard[] {\n    return [\n      {\n        icon: 'attach_money',\n        value: '₹34,766',\n        label: 'Total Purchase Amount',\n        color: '#ff6b35'\n      },\n      {\n        icon: 'shopping_cart',\n        value: '271',\n        label: 'Total Orders',\n        color: '#ffa66f'\n      },\n      {\n        icon: 'trending_up',\n        value: '₹1,973',\n        label: 'Average Order Value',\n        color: '#ff8b4d'\n      },\n      {\n        icon: 'store',\n        value: 'Fresh Mart',\n        label: 'Top Vendor',\n        color: '#ff9966'\n      }\n    ];\n  }\n\n  private getCardColor(dataType: string): string {\n    const colorMap: { [key: string]: string } = {\n      'currency': '#ff6b35',\n      'number': '#ffa66f',\n      'percentage': '#ff8b4d',\n      'vendor': '#ff9966'\n    };\n    return colorMap[dataType] || '#ff6b35';\n  }\n\n  private formatDate(date: Date): string {\n    return date.toISOString().split('T')[0];\n  }\n\n  onLocationChange(): void {\n    this.loadDashboardData();\n  }\n\n  onDateChange(): void {\n    this.loadDashboardData();\n  }\n\n  getChartData(chart: ChartDataModel): ChartData {\n    return chart.data;\n  }\n\n  getChartType(chart: ChartDataModel): ChartType {\n    return chart.type as ChartType;\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Header with Input -->\n  <div class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <div class=\"assistant-info\">\n          <mat-icon class=\"assistant-icon\">smart_toy</mat-icon>\n          <div class=\"assistant-text\">\n            <span class=\"assistant-title\">Smart Dashboard Assistant</span>\n            <span class=\"assistant-status\">Ready to analyze</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"header-right\">\n        <div class=\"search-container\">\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <mat-label>Ask me about your business data</mat-label>\n            <input matInput placeholder=\"Ask me about your business data\" />\n            <mat-icon matSuffix class=\"search-icon\">search</mat-icon>\n          </mat-form-field>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"dashboard-content\">\n    <!-- Sidebar Filters -->\n    <div class=\"sidebar\">\n      <div class=\"filters-section\">\n        <h3 class=\"filters-title\">\n          <mat-icon>tune</mat-icon>\n          Smart Filters\n          <span class=\"filter-count\">1</span>\n        </h3>\n\n        <!-- Location Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>location_on</mat-icon>\n            Restaurants\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select Location</mat-label>\n            <mat-select [(value)]=\"selectedLocation\" (selectionChange)=\"onLocationChange()\">\n              <mat-option>\n                <ngx-mat-select-search \n                  [formControl]=\"locationFilterCtrl\"\n                  placeholderLabel=\"Search locations...\"\n                  noEntriesFoundLabel=\"No locations found\">\n                </ngx-mat-select-search>\n              </mat-option>\n              <mat-option *ngFor=\"let branch of filteredBranches\" [value]=\"branch.restaurantIdOld\">\n                {{branch.branchName}}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Date Range Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            Base Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Start Date</mat-label>\n            <input matInput [matDatepicker]=\"startPicker\" [formControl]=\"startDate\" (dateChange)=\"onDateChange()\">\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n          \n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>End Date</mat-label>\n            <input matInput [matDatepicker]=\"endPicker\" [formControl]=\"endDate\" (dateChange)=\"onDateChange()\">\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- Reset Filters Button -->\n        <div class=\"filter-actions\">\n          <button mat-stroked-button class=\"reset-filters-btn\" (click)=\"loadDashboardData()\">\n            <mat-icon>refresh</mat-icon>\n            Reset filters\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Main Content -->\n    <div class=\"main-content\">\n      <!-- Loading Spinner -->\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <mat-spinner diameter=\"50\"></mat-spinner>\n        <p>Loading dashboard data...</p>\n      </div>\n\n      <!-- Dashboard Content -->\n      <div *ngIf=\"!isLoading\" class=\"dashboard-grid\">\n        <!-- Summary Cards Row -->\n        <div class=\"summary-cards-row\">\n          <mat-card *ngFor=\"let card of summaryCards\" class=\"summary-card\" [style.border-left-color]=\"card.color\">\n            <mat-card-content>\n              <div class=\"card-content\">\n                <div class=\"card-icon\" [style.color]=\"card.color\">\n                  <mat-icon>{{card.icon}}</mat-icon>\n                </div>\n                <div class=\"card-info\">\n                  <div class=\"card-value\">{{card.value}}</div>\n                  <div class=\"card-label\">{{card.label}}</div>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n\n        <!-- Charts Grid -->\n        <div class=\"charts-grid\">\n          <!-- Purchase Trends Chart (Full Width) -->\n          <mat-card *ngFor=\"let chart of charts; let i = index\"\n                    class=\"chart-card\"\n                    [ngClass]=\"{\n                      'full-width': chart.type === 'line' || chart.title.toLowerCase().includes('trend'),\n                      'half-width': chart.type !== 'line' && !chart.title.toLowerCase().includes('trend'),\n                      'third-width': chart.type === 'doughnut' || chart.type === 'pie'\n                    }\">\n            <mat-card-header>\n              <mat-card-title class=\"chart-title\">{{chart.title}}</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"chart-container\" [attr.data-chart-type]=\"chart.type\">\n                <!-- Line Chart -->\n                <canvas *ngIf=\"chart.type === 'line'\"\n                        baseChart\n                        [type]=\"'line'\"\n                        [data]=\"getChartData(chart)\"\n                        [options]=\"lineChartOptions\">\n                </canvas>\n\n                <!-- Bar Chart -->\n                <canvas *ngIf=\"chart.type === 'bar'\"\n                        baseChart\n                        [type]=\"'bar'\"\n                        [data]=\"getChartData(chart)\"\n                        [options]=\"barChartOptions\">\n                </canvas>\n\n                <!-- Doughnut Chart -->\n                <canvas *ngIf=\"chart.type === 'doughnut' || chart.type === 'pie'\"\n                        baseChart\n                        [type]=\"'doughnut'\"\n                        [data]=\"getChartData(chart)\"\n                        [options]=\"doughnutChartOptions\">\n                </canvas>\n              </div>\n            </mat-card-content>\n          </mat-card>\n\n          <!-- Default Charts if no data -->\n          <ng-container *ngIf=\"charts.length === 0 && !isLoading\">\n            <!-- Purchase Trends Chart -->\n            <mat-card class=\"chart-card full-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Purchase Trends (Last 30 Days)</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>show_chart</mat-icon>\n                    <p>Purchase trends data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Top Vendors Chart -->\n            <mat-card class=\"chart-card half-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Top Vendors by Purchase Amount</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>bar_chart</mat-icon>\n                    <p>Vendor data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Category Spending Chart -->\n            <mat-card class=\"chart-card half-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Category wise Spending Distribution</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>pie_chart</mat-icon>\n                    <p>Category spending data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <!-- Top Items by Quantity Chart -->\n            <mat-card class=\"chart-card full-width\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">Top Items by Quantity Purchased</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\">\n                  <div class=\"no-data-message\">\n                    <mat-icon>inventory</mat-icon>\n                    <p>Top items data will appear here</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </ng-container>\n        </div>\n      </div>\n\n      <!-- Empty State -->\n      <div *ngIf=\"!isLoading && summaryCards.length === 0\" class=\"empty-state\">\n        <mat-icon class=\"empty-icon\">analytics</mat-icon>\n        <h3>No Data Available</h3>\n        <p>Please select a location and date range to view dashboard data.</p>\n        <button mat-raised-button color=\"primary\" (click)=\"loadDashboardData()\">\n          <mat-icon>refresh</mat-icon>\n          Refresh Data\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;ICoC/DC,EAAA,CAAAC,cAAA,qBAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,eAAA,CAAgC;IAClFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,UAAA,MACF;;;;;IAuCRT,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAU,SAAA,sBAAyC;IACzCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAO9BH,EAAA,CAAAC,cAAA,mBAAwG;IAItFD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEpCH,EAAA,CAAAC,cAAA,cAAuB;IACGD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IARaH,EAAA,CAAAW,WAAA,sBAAAC,QAAA,CAAAC,KAAA,CAAsC;IAG1Eb,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAW,WAAA,UAAAC,QAAA,CAAAC,KAAA,CAA0B;IACrCb,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAc,iBAAA,CAAAF,QAAA,CAAAG,IAAA,CAAa;IAGCf,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAc,iBAAA,CAAAF,QAAA,CAAAI,KAAA,CAAc;IACdhB,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAc,iBAAA,CAAAF,QAAA,CAAAK,KAAA,CAAc;;;;;IAuBxCjB,EAAA,CAAAU,SAAA,iBAKS;;;;;IAHDV,EAAA,CAAAI,UAAA,gBAAe,SAAAc,OAAA,CAAAC,YAAA,CAAAC,SAAA,cAAAF,OAAA,CAAAG,gBAAA;;;;;IAMvBrB,EAAA,CAAAU,SAAA,iBAKS;;;;;IAHDV,EAAA,CAAAI,UAAA,eAAc,SAAAkB,OAAA,CAAAH,YAAA,CAAAC,SAAA,cAAAE,OAAA,CAAAC,eAAA;;;;;IAMtBvB,EAAA,CAAAU,SAAA,iBAKS;;;;;IAHDV,EAAA,CAAAI,UAAA,oBAAmB,SAAAoB,OAAA,CAAAL,YAAA,CAAAC,SAAA,cAAAI,OAAA,CAAAC,oBAAA;;;;;;;;;;;;IA/BjCzB,EAAA,CAAAC,cAAA,mBAMa;IAE2BD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEtEH,EAAA,CAAAC,cAAA,uBAAkB;IAGdD,EAAA,CAAA0B,UAAA,IAAAC,2DAAA,qBAKS;IAGT3B,EAAA,CAAA0B,UAAA,IAAAE,2DAAA,qBAKS;IAGT5B,EAAA,CAAA0B,UAAA,IAAAG,2DAAA,qBAKS;IACX7B,EAAA,CAAAG,YAAA,EAAM;;;;IAjCAH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAX,SAAA,CAAAY,IAAA,eAAAZ,SAAA,CAAAa,KAAA,CAAAC,WAAA,GAAAC,QAAA,WAAAf,SAAA,CAAAY,IAAA,gBAAAZ,SAAA,CAAAa,KAAA,CAAAC,WAAA,GAAAC,QAAA,WAAAf,SAAA,CAAAY,IAAA,mBAAAZ,SAAA,CAAAY,IAAA,YAIE;IAE4BhC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAc,iBAAA,CAAAM,SAAA,CAAAa,KAAA,CAAe;IAGtBjC,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAoC,WAAA,oBAAAhB,SAAA,CAAAY,IAAA,CAAmC;IAErDhC,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAgB,SAAA,CAAAY,IAAA,YAA2B;IAQ3BhC,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,SAAAgB,SAAA,CAAAY,IAAA,WAA0B;IAQ1BhC,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAI,UAAA,SAAAgB,SAAA,CAAAY,IAAA,mBAAAZ,SAAA,CAAAY,IAAA,WAAuD;;;;;IAWtEhC,EAAA,CAAAqC,uBAAA,GAAwD;IAEtDrC,EAAA,CAAAC,cAAA,mBAAwC;IAEAD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAErFH,EAAA,CAAAC,cAAA,uBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAOpDH,EAAA,CAAAC,cAAA,oBAAwC;IAEAD,EAAA,CAAAE,MAAA,sCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAErFH,EAAA,CAAAC,cAAA,wBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAO3CH,EAAA,CAAAC,cAAA,oBAAwC;IAEAD,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAE1FH,EAAA,CAAAC,cAAA,wBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,+CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAOtDH,EAAA,CAAAC,cAAA,oBAAwC;IAEAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEtFH,EAAA,CAAAC,cAAA,wBAAkB;IAGFD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKhDH,EAAA,CAAAsC,qBAAA,EAAe;;;;;IAzHnBtC,EAAA,CAAAC,cAAA,cAA+C;IAG3CD,EAAA,CAAA0B,UAAA,IAAAa,kDAAA,wBAYW;IACbvC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAyB;IAEvBD,EAAA,CAAA0B,UAAA,IAAAc,kDAAA,wBAqCW;IAGXxC,EAAA,CAAA0B,UAAA,IAAAe,sDAAA,4BA4De;IACjBzC,EAAA,CAAAG,YAAA,EAAM;;;;IAvHuBH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,YAAAsC,MAAA,CAAAC,YAAA,CAAe;IAkBd3C,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAI,UAAA,YAAAsC,MAAA,CAAAE,MAAA,CAAW;IAwCxB5C,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAsC,MAAA,CAAAE,MAAA,CAAAC,MAAA,WAAAH,MAAA,CAAAI,SAAA,CAAuC;;;;;;IAiE1D9C,EAAA,CAAAC,cAAA,cAAyE;IAC1CD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sEAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtEH,EAAA,CAAAC,cAAA,iBAAwE;IAA9BD,EAAA,CAAA+C,UAAA,mBAAAC,gEAAA;MAAAhD,EAAA,CAAAiD,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAoD,aAAA;MAAA,OAASpD,EAAA,CAAAqD,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACrEtD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADnMjB,MAuBaoD,uBAAuB;EAyLlCC,YACUC,qBAA4C,EAC5CC,WAAwB,EACxBC,gBAAkC,EAClCC,GAAsB;IAHtB,KAAAH,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,GAAG,GAAHA,GAAG;IA5LL,KAAAC,QAAQ,GAAG,IAAIjE,OAAO,EAAQ;IAItC,KAAAkE,QAAQ,GAAU,EAAE;IACpB,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,kBAAkB,GAAG,IAAIxE,WAAW,CAAC,EAAE,CAAC;IACxC,KAAAyE,SAAS,GAAG,IAAIzE,WAAW,CAAC,IAAI0E,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9E,KAAAC,OAAO,GAAG,IAAI5E,WAAW,CAAC,IAAI0E,IAAI,EAAE,CAAC;IAErC;IACA,KAAAxB,YAAY,GAAkB,EAAE;IAChC,KAAAC,MAAM,GAAqB,EAAE;IAC7B,KAAAE,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAzB,gBAAgB,GAAkC;MAChDiD,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE,KAAK;UACfC,MAAM,EAAE;YACNC,aAAa,EAAE,IAAI;YACnBC,OAAO,EAAE;;SAEZ;QACDC,OAAO,EAAE;UACPC,IAAI,EAAE,OAAO;UACbC,SAAS,EAAE,KAAK;UAChBC,eAAe,EAAE,iBAAiB;UAClCC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE;;OAEhB;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDd,OAAO,EAAE,IAAI;UACbe,IAAI,EAAE;YACJf,OAAO,EAAE,IAAI;YACb7D,KAAK,EAAE;WACR;UACDoB,KAAK,EAAE;YACLyC,OAAO,EAAE;;SAEZ;QACDgB,CAAC,EAAE;UACDhB,OAAO,EAAE,IAAI;UACbe,IAAI,EAAE;YACJf,OAAO,EAAE,IAAI;YACb7D,KAAK,EAAE;WACR;UACDoB,KAAK,EAAE;YACLyC,OAAO,EAAE;WACV;UACDiB,WAAW,EAAE,IAAI;UACjBC,KAAK,EAAE;YACLC,QAAQ,EAAE,SAAAA,CAAS7E,KAAU;cAC3B,OAAO,GAAG,GAAGA,KAAK,CAAC8E,cAAc,EAAE;YACrC;;;OAGL;MACDC,WAAW,EAAE;QACXf,IAAI,EAAE,SAAS;QACfgB,IAAI,EAAE,GAAG;QACTf,SAAS,EAAE;;KAEd;IAED,KAAA1D,eAAe,GAAkC;MAC/C+C,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1B0B,SAAS,EAAE,GAAY;MACvBzB,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;SACV;QACDK,OAAO,EAAE;UACPG,eAAe,EAAE,iBAAiB;UAClCC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE,CAAC;UACdY,SAAS,EAAE;YACTjF,KAAK,EAAE,SAAAA,CAASkF,OAAY;cAC1B,OAAO,GAAG,GAAGA,OAAO,CAACC,MAAM,CAACZ,CAAC,CAACM,cAAc,EAAE;YAChD;;;OAGL;MACDP,MAAM,EAAE;QACNC,CAAC,EAAE;UACDG,WAAW,EAAE,IAAI;UACjBF,IAAI,EAAE;YACJf,OAAO,EAAE,IAAI;YACb7D,KAAK,EAAE;WACR;UACDoB,KAAK,EAAE;YACLyC,OAAO,EAAE;WACV;UACDkB,KAAK,EAAE;YACLC,QAAQ,EAAE,SAAAA,CAAS7E,KAAU;cAC3B,OAAO,GAAG,GAAGA,KAAK,CAAC8E,cAAc,EAAE;YACrC;;SAEH;QACDJ,CAAC,EAAE;UACDD,IAAI,EAAE;YACJf,OAAO,EAAE;WACV;UACDzC,KAAK,EAAE;YACLyC,OAAO,EAAE;;;;KAIhB;IAED,KAAAjD,oBAAoB,GAAQ;MAC1B6C,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;YACNC,aAAa,EAAE,IAAI;YACnBC,OAAO,EAAE,EAAE;YACXuB,IAAI,EAAE;cACJC,IAAI,EAAE;;;SAGX;QACDvB,OAAO,EAAE;UACPG,eAAe,EAAE,iBAAiB;UAClCC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE,CAAC;UACdY,SAAS,EAAE;YACTjF,KAAK,EAAE,SAAAA,CAASkF,OAAY;cAC1B,MAAMlF,KAAK,GAAGkF,OAAO,CAAClF,KAAK,IAAI,EAAE;cACjC,MAAMD,KAAK,GAAGmF,OAAO,CAACC,MAAM;cAC5B,MAAMG,KAAK,GAAGJ,OAAO,CAACK,OAAO,CAACC,IAAI,CAACC,MAAM,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;cAC7E,MAAMC,UAAU,GAAG,CAAE7F,KAAK,GAAGuF,KAAK,GAAI,GAAG,EAAEO,OAAO,CAAC,CAAC,CAAC;cACrD,OAAO,GAAG7F,KAAK,KAAK4F,UAAU,GAAG;YACnC;;;OAGL;MACDE,MAAM,EAAE;KACT;IAED,KAAAC,uBAAuB,GAAkC;MACvD1C,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDa,MAAM,EAAE;QACNC,CAAC,EAAE;UACDvD,KAAK,EAAE;YACLyC,OAAO,EAAE,IAAI;YACbuC,IAAI,EAAE;;SAET;QACDvB,CAAC,EAAE;UACDC,WAAW,EAAE,IAAI;UACjB1D,KAAK,EAAE;YACLyC,OAAO,EAAE,IAAI;YACbuC,IAAI,EAAE;;;;KAIb;IAQC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACxD,WAAW,CAACyD,cAAc,EAAE;EAC/C;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAAChE,iBAAiB,EAAE;EAC1B;EAEAiE,WAAWA,CAAA;IACT,IAAI,CAAC1D,QAAQ,CAAC2D,IAAI,EAAE;IACpB,IAAI,CAAC3D,QAAQ,CAAC4D,QAAQ,EAAE;EAC1B;EAEQJ,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACpD,kBAAkB,CAACyD,YAAY,CACjCC,IAAI,CACH7H,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAACgE,QAAQ,CAAC,CACzB,CACA+D,SAAS,CAAE5G,KAAoB,IAAI;MAClC,IAAI,CAAC6G,cAAc,CAAC7G,KAAK,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC;EACN;EAEQsG,YAAYA,CAAA;IAClB,IAAI,CAAC3D,gBAAgB,CAACmE,sBAAsB,CACzCH,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACgE,QAAQ,CAAC,CAAC,CAC9B+D,SAAS,CAACnB,IAAI,IAAG;MAChB,IAAI,CAAC3C,QAAQ,GAAG2C,IAAI,IAAI,EAAE;MAC1B,IAAI,CAAC1C,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;MAE1C,IAAI,IAAI,CAACA,QAAQ,CAACjB,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACmB,gBAAgB,GAAG,IAAI,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACxD,eAAe;;IAE5D,CAAC,CAAC;EACN;EAEQuH,cAAcA,CAACE,UAAkB;IACvC,IAAI,CAACA,UAAU,EAAE;MACf,IAAI,CAAChE,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;KAC3C,MAAM;MACL,MAAMkE,oBAAoB,GAAGD,UAAU,CAAC7F,WAAW,EAAE,CAAC+F,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxE,IAAI,CAAClE,gBAAgB,GAAG,IAAI,CAACD,QAAQ,CAACoE,MAAM,CAACC,MAAM,IACjDA,MAAM,CAAC1H,UAAU,CAACyB,WAAW,EAAE,CAAC+F,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC9F,QAAQ,CAAC6F,oBAAoB,CAAC,CAClF;;EAEL;EAEA1E,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACY,SAAS,CAAClD,KAAK,IAAI,CAAC,IAAI,CAACqD,OAAO,CAACrD,KAAK,EAAE;MAChD;;IAGF,IAAI,CAAC8B,SAAS,GAAG,IAAI;IAErB,MAAMsF,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACrE,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB,CAAC,GAAG,EAAE;MAC/DE,SAAS,EAAE,IAAI,CAACoE,UAAU,CAAC,IAAI,CAACpE,SAAS,CAAClD,KAAK,CAAC;MAChDqD,OAAO,EAAE,IAAI,CAACiE,UAAU,CAAC,IAAI,CAACjE,OAAO,CAACrD,KAAK,CAAC;MAC5CuH,QAAQ,EAAE;KACX;IAED,MAAMC,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACvB,IAAI,CAACwB,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE;KACrB;IAED,IAAI,CAACnF,qBAAqB,CAACoF,qBAAqB,CAACL,OAAO,CAAC,CACtDb,IAAI,CAAC9H,SAAS,CAAC,IAAI,CAACgE,QAAQ,CAAC,CAAC,CAC9B+D,SAAS,CAAC;MACTJ,IAAI,EAAGsB,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACC,oBAAoB,CAACF,QAAQ,CAACrC,IAAI,CAAC;SACzC,MAAM;UACL;UACA,IAAI,CAACwC,cAAc,EAAE;;QAEvB,IAAI,CAACnG,SAAS,GAAG,KAAK;QACtB,IAAI,CAACc,GAAG,CAACsF,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD;QACA,IAAI,CAACF,cAAc,EAAE;QACrB,IAAI,CAACnG,SAAS,GAAG,KAAK;QACtB,IAAI,CAACc,GAAG,CAACsF,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQD,cAAcA,CAAA;IACpB;IACA,MAAMI,UAAU,GAAG;MACjBC,aAAa,EAAE,CACb;QAAEvI,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE,uBAAuB;QAAEsI,SAAS,EAAE;MAAU,CAAE,EACjG;QAAExI,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,cAAc;QAAEsI,SAAS,EAAE;MAAQ,CAAE,EACnF;QAAExI,IAAI,EAAE,aAAa;QAAEC,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE,qBAAqB;QAAEsI,SAAS,EAAE;MAAU,CAAE,EAC7F;QAAExI,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,YAAY;QAAEC,KAAK,EAAE,YAAY;QAAEsI,SAAS,EAAE;MAAQ,CAAE,CACjF;MACD3G,MAAM,EAAE,CACN;QACE4G,EAAE,EAAE,iBAAiB;QACrBvH,KAAK,EAAE,gCAAgC;QACvCD,IAAI,EAAE,MAAM;QACZyE,IAAI,EAAE;UACJ7B,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;UAChD6E,QAAQ,EAAE,CAAC;YACTxI,KAAK,EAAE,uBAAuB;YAC9BwF,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YAClCvB,eAAe,EAAE,CAAC,SAAS,CAAC;YAC5BG,WAAW,EAAE,CAAC,SAAS;WACxB;;OAEJ,EACD;QACEmE,EAAE,EAAE,aAAa;QACjBvH,KAAK,EAAE,gCAAgC;QACvCD,IAAI,EAAE,KAAK;QACXyE,IAAI,EAAE;UACJ7B,MAAM,EAAE,CAAC,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,eAAe,CAAC;UACxF6E,QAAQ,EAAE,CAAC;YACTxI,KAAK,EAAE,uBAAuB;YAC9BwF,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;YAC9CvB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxEG,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;WACpE;;OAEJ,EACD;QACEmE,EAAE,EAAE,mBAAmB;QACvBvH,KAAK,EAAE,qCAAqC;QAC5CD,IAAI,EAAE,UAAU;QAChByE,IAAI,EAAE;UACJ7B,MAAM,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC;UACjE6E,QAAQ,EAAE,CAAC;YACTxI,KAAK,EAAE,uBAAuB;YAC9BwF,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAC1BvB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxEG,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;WACpE;;OAEJ;KAEJ;IAED,IAAI,CAAC2D,oBAAoB,CAACK,UAAU,CAAC;EACvC;EAEQL,oBAAoBA,CAACvC,IAAS;IACpC;IACA,IAAI,CAAC9D,YAAY,GAAG8D,IAAI,CAAC6C,aAAa,EAAEI,GAAG,CAAEC,IAAS,KAAM;MAC1D5I,IAAI,EAAE,IAAI,CAAC0C,qBAAqB,CAACmG,kBAAkB,CAACD,IAAI,CAACJ,SAAS,EAAEI,IAAI,CAAC1I,KAAK,CAAC;MAC/ED,KAAK,EAAE2I,IAAI,CAAC3I,KAAK;MACjBC,KAAK,EAAE0I,IAAI,CAAC1I,KAAK;MACjBJ,KAAK,EAAE,IAAI,CAACgJ,YAAY,CAACF,IAAI,CAACJ,SAAS;KACxC,CAAC,CAAC,IAAI,EAAE;IAET;IACA,IAAI,IAAI,CAAC5G,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACF,YAAY,GAAG,IAAI,CAACmH,yBAAyB,EAAE;;IAGtD;IACA,IAAI,CAAClH,MAAM,GAAG6D,IAAI,CAAC7D,MAAM,EAAE8G,GAAG,CAAEK,KAAU,KAAM;MAC9C,GAAGA,KAAK;MACRtD,IAAI,EAAE,IAAI,CAAChD,qBAAqB,CAACuG,gBAAgB,CAACD,KAAK;KACxD,CAAC,CAAC,IAAI,EAAE;IAET;IACA,IAAI,IAAI,CAACnH,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5BuG,OAAO,CAACa,GAAG,CAAC,kCAAkC,CAAC;;EAEnD;EAEQH,yBAAyBA,CAAA;IAC/B,OAAO,CACL;MACE/I,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,uBAAuB;MAC9BJ,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,cAAc;MACrBJ,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,QAAQ;MACfC,KAAK,EAAE,qBAAqB;MAC5BJ,KAAK,EAAE;KACR,EACD;MACEE,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,YAAY;MACnBJ,KAAK,EAAE;KACR,CACF;EACH;EAEQgJ,YAAYA,CAACK,QAAgB;IACnC,MAAMC,QAAQ,GAA8B;MAC1C,UAAU,EAAE,SAAS;MACrB,QAAQ,EAAE,SAAS;MACnB,YAAY,EAAE,SAAS;MACvB,QAAQ,EAAE;KACX;IACD,OAAOA,QAAQ,CAACD,QAAQ,CAAC,IAAI,SAAS;EACxC;EAEQ5B,UAAUA,CAAC8B,IAAU;IAC3B,OAAOA,IAAI,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACjH,iBAAiB,EAAE;EAC1B;EAEAkH,YAAYA,CAAA;IACV,IAAI,CAAClH,iBAAiB,EAAE;EAC1B;EAEAnC,YAAYA,CAAC4I,KAAqB;IAChC,OAAOA,KAAK,CAACtD,IAAI;EACnB;EAEAgE,YAAYA,CAACV,KAAqB;IAChC,OAAOA,KAAK,CAAC/H,IAAiB;EAChC;;;uBA3aWuB,uBAAuB,EAAAvD,EAAA,CAAA0K,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAA5K,EAAA,CAAA0K,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9K,EAAA,CAAA0K,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAhL,EAAA,CAAA0K,iBAAA,CAAA1K,EAAA,CAAAiL,iBAAA;IAAA;EAAA;;;YAAvB1H,uBAAuB;MAAA2H,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApL,EAAA,CAAAqL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3DpC3L,EAAA,CAAAC,cAAA,aAAuC;UAMID,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,aAA4B;UACID,EAAA,CAAAE,MAAA,gCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,eAA+B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI5DH,EAAA,CAAAC,cAAA,cAA0B;UAGTD,EAAA,CAAAE,MAAA,uCAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtDH,EAAA,CAAAU,SAAA,iBAAgE;UAChEV,EAAA,CAAAC,cAAA,oBAAwC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAOnEH,EAAA,CAAAC,cAAA,eAA+B;UAKbD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,uBACA;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIrCH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,sBAAgF;UAApED,EAAA,CAAA+C,UAAA,yBAAA8I,oEAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA5H,gBAAA,GAAA8H,MAAA;UAAA,EAA4B,6BAAAC,wEAAA;YAAA,OAAoBH,GAAA,CAAArB,gBAAA,EAAkB;UAAA,EAAtC;UACtCvK,EAAA,CAAAC,cAAA,kBAAY;UACVD,EAAA,CAAAU,SAAA,iCAIwB;UAC1BV,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAA0B,UAAA,KAAAsK,8CAAA,yBAEa;UACfhM,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,iBAAsG;UAA9BD,EAAA,CAAA+C,UAAA,wBAAAkJ,8DAAA;YAAA,OAAcL,GAAA,CAAApB,YAAA,EAAc;UAAA,EAAC;UAArGxK,EAAA,CAAAG,YAAA,EAAsG;UACtGH,EAAA,CAAAU,SAAA,iCAA6E;UAE/EV,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAC,cAAA,iBAAkG;UAA9BD,EAAA,CAAA+C,UAAA,wBAAAmJ,8DAAA;YAAA,OAAcN,GAAA,CAAApB,YAAA,EAAc;UAAA,EAAC;UAAjGxK,EAAA,CAAAG,YAAA,EAAkG;UAClGH,EAAA,CAAAU,SAAA,iCAA2E;UAE7EV,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA4B;UAC2BD,EAAA,CAAA+C,UAAA,mBAAAoJ,0DAAA;YAAA,OAASP,GAAA,CAAAtI,iBAAA,EAAmB;UAAA,EAAC;UAChFtD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA0B;UAExBD,EAAA,CAAA0B,UAAA,KAAA0K,uCAAA,kBAGM;UAGNpM,EAAA,CAAA0B,UAAA,KAAA2K,uCAAA,kBA2HM;UAGNrM,EAAA,CAAA0B,UAAA,KAAA4K,uCAAA,mBAQM;UACRtM,EAAA,CAAAG,YAAA,EAAM;;;;;UA9LcH,EAAA,CAAAO,SAAA,IAA4B;UAA5BP,EAAA,CAAAI,UAAA,UAAAwL,GAAA,CAAA5H,gBAAA,CAA4B;UAGlChE,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,gBAAAwL,GAAA,CAAA3H,kBAAA,CAAkC;UAKPjE,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAAwL,GAAA,CAAA7H,gBAAA,CAAmB;UAepC/D,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,kBAAAmM,GAAA,CAA6B,gBAAAX,GAAA,CAAA1H,SAAA;UACZlE,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,QAAAmM,GAAA,CAAmB;UAMpCvM,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAI,UAAA,kBAAAoM,GAAA,CAA2B,gBAAAZ,GAAA,CAAAvH,OAAA;UACVrE,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,QAAAoM,GAAA,CAAiB;UAkBlDxM,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAwL,GAAA,CAAA9I,SAAA,CAAe;UAMf9C,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,UAAAwL,GAAA,CAAA9I,SAAA,CAAgB;UA8HhB9C,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAI,UAAA,UAAAwL,GAAA,CAAA9I,SAAA,IAAA8I,GAAA,CAAAjJ,YAAA,CAAAE,MAAA,OAA6C;;;qBDxLrDjE,YAAY,EAAA6N,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ/N,aAAa,EAAAgO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbnO,eAAe,EAAAoO,EAAA,CAAAC,SAAA,EACfpO,aAAa,EAAAqO,EAAA,CAAAC,OAAA,EACbrO,eAAe,EAAAsO,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,SAAA,EAAAC,EAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACf5O,kBAAkB,EAClBC,cAAc,EAAA4O,GAAA,CAAAC,QAAA,EACd5O,mBAAmB,EAAA6O,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB/O,mBAAmB,EACnBC,gBAAgB,EAChBC,wBAAwB,EAAA8O,GAAA,CAAAC,kBAAA,EACxB9O,cAAc,EAAA+O,GAAA,CAAAC,kBAAA,EACd/O,wBAAwB,EAAAgP,GAAA,CAAAC,wBAAA,EACxB/O,mBAAmB,EAAAgP,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,oBAAA,EACnBlP,WAAW;MAAAmP,MAAA;IAAA;EAAA;;SAKFvL,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}